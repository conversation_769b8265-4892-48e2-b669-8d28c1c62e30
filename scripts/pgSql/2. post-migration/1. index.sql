-- INDEXES
CREATE INDEX profile_period_information_user_id_channel_id_index ON profile_period_information (user_id, channel_id);
CREATE UNIQUE INDEX api_key_api_key_uindex ON api_key (api_key);
CREATE INDEX accumulator_classifier_id_index ON accumulator (classifier_id);
CREATE INDEX accumulator_parameter_accumulator_id_index ON accumulator_parameter (accumulator_id);
CREATE INDEX accumulator_parameter_parameter_id_index ON accumulator_parameter (parameter_id);
CREATE INDEX accumulator_rule_condition_accumulator_id_index ON accumulator_rule_condition (accumulator_id);
CREATE INDEX accumulator_rule_condition_id_index ON accumulator_rule_condition (rule_condition_id);
CREATE INDEX additional_parameter_event_field1_index ON additional_parameter_event (field1);
CREATE INDEX additional_parameter_event_field2_index ON additional_parameter_event (field2);
CREATE INDEX additional_parameter_event_field3_index ON additional_parameter_event (field3);
CREATE INDEX additional_parameter_event_field4_index ON additional_parameter_event (field4);
CREATE INDEX additional_parameter_event_field5_index ON additional_parameter_event (field5);
CREATE INDEX additional_parameter_event_field6_index ON additional_parameter_event (field6);
CREATE INDEX additional_parameter_event_field7_index ON additional_parameter_event (field7);
CREATE INDEX additional_parameter_event_field8_index ON additional_parameter_event (field8);
CREATE INDEX additional_parameter_event_field9_index ON additional_parameter_event (field9);
CREATE INDEX additional_parameter_event_field10_index ON additional_parameter_event (field10);
CREATE INDEX additional_parameter_transaction_field1_index ON additional_parameter_transaction (field1);
CREATE INDEX additional_parameter_transaction_field2_index ON additional_parameter_transaction (field2);
CREATE INDEX additional_parameter_transaction_field3_index ON additional_parameter_transaction (field3);
CREATE INDEX additional_parameter_transaction_field4_index ON additional_parameter_transaction (field4);
CREATE INDEX additional_parameter_transaction_field5_index ON additional_parameter_transaction (field5);
CREATE INDEX additional_parameter_transaction_field6_index ON additional_parameter_transaction (field6);
CREATE INDEX additional_parameter_transaction_field7_index ON additional_parameter_transaction (field7);
CREATE INDEX additional_parameter_transaction_field8_index ON additional_parameter_transaction (field8);
CREATE INDEX additional_parameter_transaction_field9_index ON additional_parameter_transaction (field9);
CREATE INDEX additional_parameter_transaction_field10_index ON additional_parameter_transaction (field10);
CREATE INDEX auth_privilege_id_parent_index ON auth_privilege (parent_id);
CREATE INDEX auth_privilege_name_index ON auth_privilege (name);
CREATE INDEX auth_role_name_index ON auth_role (name);
CREATE INDEX auth_role_privilege_privilege_id_index ON auth_role_privilege (privilege_id);
CREATE UNIQUE INDEX AUTH_SESSION_IX1 ON AUTH_SESSION (SESSION_ID);
CREATE INDEX AUTH_SESSION_IX2 ON AUTH_SESSION (EXPIRY_TIME);
CREATE INDEX AUTH_SESSION_IX3 ON AUTH_SESSION (PRINCIPAL_NAME);
CREATE INDEX channel_enabled_index ON channel (enabled);
CREATE INDEX classifier_cep_engine_enable_index ON classifier (cep_engine_enable);
CREATE INDEX classifier_id_classifier_as_rule_index ON classifier (classifier_as_rule_id);
CREATE INDEX classifier_classifier_order_index ON classifier (classifier_order);
CREATE INDEX classifier_condition_classifier_index ON classifier_condition (classifier_id);
CREATE INDEX classifier_condition_condition_type_id_index ON classifier_condition (condition_type_id);
CREATE INDEX classifier_condition_custom_list_id_index ON classifier_condition (custom_list_id);
CREATE INDEX classifier_condition_logical_operator_id_index ON classifier_condition (logical_operator_id);
CREATE INDEX classifier_condition_operator_id_index ON classifier_condition (operator_id);
CREATE INDEX classifier_condition_parameter_id_index ON classifier_condition (parameter_id);
CREATE INDEX classifier_rule_classifier_id_index ON classifier_rule (classifier_id);
CREATE INDEX classifier_rule_rule_id_index ON classifier_rule (rule_id);
CREATE INDEX event_channel_id_index ON event (channel_id);
CREATE INDEX event_operation_id_index ON event (operation_id, user_id);
CREATE INDEX fingerprint_agent_value_browser_major_index ON fingerprint_agent_value (browser_major);
CREATE INDEX fingerprint_agent_value_browser_version_index ON fingerprint_agent_value (browser_version);
CREATE INDEX fingerprint_agent_value_device_vendor_index ON fingerprint_agent_value (device_vendor);
CREATE INDEX fingerprint_agent_value_engine_version_index ON fingerprint_agent_value (engine_version);
CREATE INDEX fingerprint_agent_value_os_version_index ON fingerprint_agent_value (os_version);
CREATE INDEX fingerprint_collection_collection_uuid_index ON fingerprint_collection (collection_uuid);
CREATE INDEX fingerprint_collection_user_id_index ON fingerprint_collection (user_id);
CREATE INDEX fingerprint_device_value_device_id_index ON fingerprint_device_value (device_id);
CREATE INDEX fingerprint_hash_fp_hash_index ON fingerprint_hash (fp_hash);
CREATE INDEX fingerprint_unverified_value_token_index ON fingerprint_unverified_value (token);
CREATE INDEX fingerprint_unverified_value_user_hash_id_index ON fingerprint_unverified_value (user_hash_id);
CREATE UNIQUE INDEX fingerprint_user_user_id_index ON fingerprint_user (user_id);
CREATE INDEX fingerprint_user_hash_fingerprint_collection_id_index ON fingerprint_user_hash (fingerprint_collection_id);
CREATE INDEX fingerprint_user_hash_hash_id_index ON fingerprint_user_hash (hash_id);
CREATE INDEX fingerprint_user_hash_multi_index ON fingerprint_user_hash (hash_id, user_id);
CREATE INDEX history_score_transaction_id_index ON history_score (transaction_id);
CREATE INDEX history_score_user_id_index ON history_score (user_id);
CREATE INDEX operation_type_event_type_id_index ON operation_type (event_type_id);
CREATE INDEX parameter_parameter_type_id_index ON parameter (parameter_type_id);
CREATE INDEX parameter_parameter_info_id_index ON parameter (parameter_info_id);
CREATE INDEX parameter_event_type_id_index ON parameter (event_type_id);
ALTER TABLE parameter_channel_operation
    ADD CONSTRAINT parameter_channel_operation_unique UNIQUE (channel_id, parameter_id, operation_type_id);
CREATE INDEX parameter_channel_operation_id_parameter_index ON parameter_channel_operation (parameter_id);
CREATE INDEX parameter_channel_operation_id_operation_type_index ON parameter_channel_operation (operation_type_id);
CREATE INDEX parameter_channel_operation_id_channel_index ON parameter_channel_operation (channel_id);
CREATE INDEX idx_profiledaycounter_user_id ON profile_day_counter (user_id, channel_id, profile_date);
CREATE INDEX rule_condition_condition_rule_type_id_index ON rule_condition (condition_rule_type_id);
CREATE INDEX rule_condition_condition_type_id_index ON rule_condition (condition_type_id);
CREATE INDEX rule_condition_connector_id_index ON rule_condition (connector_id);
CREATE INDEX rule_condition_custom_list_id_index ON rule_condition (custom_list_id);
CREATE INDEX rule_condition_delta_type_id_index ON rule_condition (delta_type_id);
CREATE INDEX rule_condition_field_id_index ON rule_condition (field_id);
CREATE INDEX rule_condition_group_id_index ON rule_condition (group_id);
CREATE INDEX rule_condition_operator_id_index ON rule_condition (operator_id);
CREATE INDEX rule_condition_parent_group_id_index ON rule_condition (parent_group_id);
CREATE INDEX rule_condition_rule_id_index ON rule_condition (rule_id);
CREATE INDEX rule_event_statement_is_context_index ON rule_event_statement (context);
CREATE INDEX rule_event_statement_rule_id_index ON rule_event_statement (rule_id);
CREATE INDEX rule_item_action_id_index ON rule_item (action_id);
CREATE INDEX rule_item_channel_id_index ON rule_item (channel_id);
CREATE INDEX rule_item_operation_type_id_index ON rule_item (operation_type_id);
CREATE INDEX rule_item_rule_type_id_index ON rule_item (rule_type_id);
CREATE INDEX rule_matched_event_event_id_index ON rule_matched_event (event_id);
CREATE INDEX rule_matched_event_event_trace_id_index ON rule_matched_event (event_trace_id);
CREATE INDEX rule_matched_event_triggered_rule_item_id_index ON rule_matched_event (triggered_rule_item_id);
CREATE INDEX rule_matched_transaction_transaction_id_index ON rule_matched_transaction (transaction_id);
CREATE INDEX rule_matched_transaction_triggered_rule_item_id_index ON rule_matched_transaction (triggered_rule_item_id);
CREATE INDEX rule_update_rule_cep_last_update_index ON rule_update (rule_cep_last_update);
CREATE INDEX rule_update_rule_classifiers_last_update_index ON rule_update (rule_classifier_last_update);
CREATE INDEX rule_update_rule_engine_last_update_index ON rule_update (rule_engine_last_update);
CREATE INDEX rule_update_rule_parameter_business_index ON rule_update (rule_parameter_business);
CREATE INDEX system_parameter_parameter_name_index ON system_parameter (parameter_name);
CREATE INDEX transaction_entity_case_status_id_index ON transaction_entity (case_status_id);
CREATE INDEX transaction_entity_operation_type_id_index ON transaction_entity (operation_type_id);
CREATE INDEX transaction_entity_user_id_index ON transaction_entity (user_id);
CREATE INDEX transaction_entity_channel_id_index ON transaction_entity (channel_id);
CREATE INDEX transaction_entity_event_date_index ON transaction_entity (event_date DESC);
CREATE INDEX user_business_group_business_group_id_index ON user_business_group (business_group_id);
CREATE INDEX user_business_group_user_id_index ON user_business_group (user_id);
CREATE INDEX user_transaction_daily_channel_id_index ON user_transaction_daily (channel_id);
CREATE INDEX user_transaction_daily_id_user_index ON user_transaction_daily (user_id);
CREATE INDEX user_transaction_monthly_channel_id_index ON user_transaction_monthly (channel_id);
CREATE INDEX user_transaction_monthly_id_user_index ON user_transaction_monthly (user_id);
CREATE INDEX user_transaction_summary_user_id_index ON user_transaction_summary (user_id);
CREATE INDEX user_transaction_summary_history_user_id_index ON user_transaction_summary_history (user_id);
CREATE INDEX user_transaction_weekly_channel_id_index ON user_transaction_weekly (channel_id);
CREATE INDEX user_transaction_weekly_id_user_index ON user_transaction_weekly (user_id);
CREATE INDEX idx_ac_idtransaction ON assigned_case (transaction_id);
CREATE INDEX "idx_user_transaction_summary_history_idUser_lastProcesedDate" ON user_transaction_summary_history (user_id, last_processed_date);
CREATE INDEX profile_by_operation_k1 ON profile_by_operation (profile_period_info_id, operation_type_id);
CREATE INDEX profileperiodinfobycreditaccount_k1 ON profile_period_info_by_credit_account (profile_period_info_id, credit_account);
CREATE INDEX idx_mltransaction_event_date ON ml_transaction (event_date);
CREATE INDEX idx_mltransaction_debit_account ON ml_transaction (debit_account);
CREATE INDEX idx_mltransaction ON ml_transaction (debit_account, event_date, transaction_id, channel_id,
                                                  operation_type_id, credit_account, amount, ip);
CREATE INDEX additional_parameter_transaction_transaction_id_index ON additional_parameter_transaction (transaction_id);
CREATE INDEX fingerprint_collection_type_index ON fingerprint_collection (type);

CREATE INDEX idx_custom_list_id ON custom_list_value (custom_list_id);
CREATE INDEX idx_list_value ON custom_list_value (list_value);
CREATE INDEX idx_custom_list_id_list_value ON custom_list_value (custom_list_id, list_value);
CREATE UNIQUE INDEX IX_FileEntry_FileName ON file_entry (file_name);

CREATE INDEX idx_event_id ON event_additional_fields_import(event_id);
CREATE INDEX idx_field_name ON event_additional_fields_import(name);
CREATE INDEX IX_EventEntityImport_ImportId ON event_entity_import (import_id);

CREATE INDEX idx_fingerprint_unverified_value_history_user_hash_id ON fingerprint_unverified_value_history (user_hash_id);
CREATE INDEX idx_fingerprint_response_history_user_hash_id ON fingerprint_response_history (user_hash_id);
CREATE INDEX idx_fingerprint_user_hash_history_collection_id ON fingerprint_user_hash_history (fingerprint_collection_id);
CREATE INDEX idx_fingerprint_user_hash_history_hash_id ON fingerprint_user_hash_history (hash_id);
CREATE INDEX idx_fingerprint_user_hash_history_user_id ON fingerprint_user_hash_history (user_id);
CREATE INDEX fingerprint_user_hash_history_multi_index ON fingerprint_user_hash_history (hash_id, user_id);
CREATE INDEX idx_fingerprint_agent_value_history_browser_major ON fingerprint_agent_value_history (browser_major);
CREATE INDEX idx_fingerprint_agent_value_history_browser_version ON fingerprint_agent_value_history (browser_version);
CREATE INDEX idx_fingerprint_agent_value_history_device_vendor ON fingerprint_agent_value_history (device_vendor);
CREATE INDEX idx_fingerprint_agent_value_history_engine_version ON fingerprint_agent_value_history (engine_version);
CREATE INDEX idx_fingerprint_agent_value_history_os_version ON fingerprint_agent_value_history (os_version);
CREATE INDEX idx_fingerprint_web_hash_history_crc_canvas ON fingerprint_web_hash_history (crc_canvas);
CREATE INDEX idx_fingerprint_web_hash_history_crc_geometry ON fingerprint_web_hash_history (crc_geometry);
CREATE INDEX idx_fingerprint_web_hash_history_crc_text ON fingerprint_web_hash_history (crc_text);
CREATE INDEX idx_fingerprint_web_hash_history_crc_webgl ON fingerprint_web_hash_history (crc_webgl);
CREATE INDEX idx_fingerprint_web_hash_history_language ON fingerprint_web_hash_history (language);
CREATE INDEX idx_fingerprint_device_value_history_device_id ON fingerprint_device_value_history (device_id);
CREATE INDEX idx_fingerprint_collection_history_collection_uuid ON fingerprint_collection_history (collection_uuid);
CREATE INDEX idx_fingerprint_collection_history_user_id ON fingerprint_collection_history (user_id);
CREATE INDEX fingerprint_collection_history_type_index ON fingerprint_collection_history (type);
CREATE INDEX idx_fingerprint_hash_history_fp_hash ON fingerprint_hash_history (fp_hash);

-- END INDEXES