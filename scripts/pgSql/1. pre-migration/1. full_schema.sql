/*
========================================================================
Version     : 5.33.1
Type        : FULL SCHEMA
DB          : POSTGRES
========================================================================
*/

-- TABLES
CREATE TABLE accumulator
(
    id            int8 NOT NULL,
    activated     TIMESTAMP,
    classifier_id int4,
    enabled       BOOLEAN,
    name          VARCHAR(255),
    range_end     int4,
    range_start   int4,
    unit_time     CHAR(1),
    PRIMARY KEY (id)
);

CREATE TABLE accumulator_parameter
(
    id             int8 NOT NULL,
    accumulator_id int8,
    parameter_id   int4,
    PRIMARY KEY (id)
);

CREATE TABLE accumulator_rule_condition
(
    id                int8 NOT NULL,
    accumulator_id    int8,
    rule_condition_id int4,
    PRIMARY KEY (id)
);

CREATE TABLE additional_parameter_event
(
    id       int8 NOT NULL,
    field1   VARCHAR(255),
    field10  VARCHAR(255),
    field100 VARCHAR(255),
    field11  VARCHAR(255),
    field12  VARCHAR(255),
    field13  VARCHAR(255),
    field14  VARCHAR(255),
    field15  VARCHAR(255),
    field16  VARCHAR(255),
    field17  VARCHAR(255),
    field18  VARCHAR(255),
    field19  VARCHAR(255),
    field2   VARCHAR(255),
    field20  VARCHAR(255),
    field21  VARCHAR(255),
    field22  VARCHAR(255),
    field23  VARCHAR(255),
    field24  VARCHAR(255),
    field25  VARCHAR(255),
    field26  VARCHAR(255),
    field27  VARCHAR(255),
    field28  VARCHAR(255),
    field29  VARCHAR(255),
    field3   VARCHAR(255),
    field30  VARCHAR(255),
    field31  VARCHAR(255),
    field32  VARCHAR(255),
    field33  VARCHAR(255),
    field34  VARCHAR(255),
    field35  VARCHAR(255),
    field36  VARCHAR(255),
    field37  VARCHAR(255),
    field38  VARCHAR(255),
    field39  VARCHAR(255),
    field4   VARCHAR(255),
    field40  VARCHAR(255),
    field41  VARCHAR(255),
    field42  VARCHAR(255),
    field43  VARCHAR(255),
    field44  VARCHAR(255),
    field45  VARCHAR(255),
    field46  VARCHAR(255),
    field47  VARCHAR(255),
    field48  VARCHAR(255),
    field49  VARCHAR(255),
    field5   VARCHAR(255),
    field50  VARCHAR(255),
    field51  VARCHAR(255),
    field52  VARCHAR(255),
    field53  VARCHAR(255),
    field54  VARCHAR(255),
    field55  VARCHAR(255),
    field56  VARCHAR(255),
    field57  VARCHAR(255),
    field58  VARCHAR(255),
    field59  VARCHAR(255),
    field6   VARCHAR(255),
    field60  VARCHAR(255),
    field61  VARCHAR(255),
    field62  VARCHAR(255),
    field63  VARCHAR(255),
    field64  VARCHAR(255),
    field65  VARCHAR(255),
    field66  VARCHAR(255),
    field67  VARCHAR(255),
    field68  VARCHAR(255),
    field69  VARCHAR(255),
    field7   VARCHAR(255),
    field70  VARCHAR(255),
    field71  VARCHAR(255),
    field72  VARCHAR(255),
    field73  VARCHAR(255),
    field74  VARCHAR(255),
    field75  VARCHAR(255),
    field76  VARCHAR(255),
    field77  VARCHAR(255),
    field78  VARCHAR(255),
    field79  VARCHAR(255),
    field8   VARCHAR(255),
    field80  VARCHAR(255),
    field81  VARCHAR(255),
    field82  VARCHAR(255),
    field83  VARCHAR(255),
    field84  VARCHAR(255),
    field85  VARCHAR(255),
    field86  VARCHAR(255),
    field87  VARCHAR(255),
    field88  VARCHAR(255),
    field89  VARCHAR(255),
    field9   VARCHAR(255),
    field90  VARCHAR(255),
    field91  VARCHAR(255),
    field92  VARCHAR(255),
    field93  VARCHAR(255),
    field94  VARCHAR(255),
    field95  VARCHAR(255),
    field96  VARCHAR(255),
    field97  VARCHAR(255),
    field98  VARCHAR(255),
    field99  VARCHAR(255),
    event_id int8,
    PRIMARY KEY (id)
);

CREATE TABLE additional_parameter_transaction
(
    id             int8 NOT NULL,
    field1         VARCHAR(255),
    field10        VARCHAR(255),
    field100       VARCHAR(255),
    field11        VARCHAR(255),
    field12        VARCHAR(255),
    field13        VARCHAR(255),
    field14        VARCHAR(255),
    field15        VARCHAR(255),
    field16        VARCHAR(255),
    field17        VARCHAR(255),
    field18        VARCHAR(255),
    field19        VARCHAR(255),
    field2         VARCHAR(255),
    field20        VARCHAR(255),
    field21        VARCHAR(255),
    field22        VARCHAR(255),
    field23        VARCHAR(255),
    field24        VARCHAR(255),
    field25        VARCHAR(255),
    field26        VARCHAR(255),
    field27        VARCHAR(255),
    field28        VARCHAR(255),
    field29        VARCHAR(255),
    field3         VARCHAR(255),
    field30        VARCHAR(255),
    field31        VARCHAR(255),
    field32        VARCHAR(255),
    field33        VARCHAR(255),
    field34        VARCHAR(255),
    field35        VARCHAR(255),
    field36        VARCHAR(255),
    field37        VARCHAR(255),
    field38        VARCHAR(255),
    field39        VARCHAR(255),
    field4         VARCHAR(255),
    field40        VARCHAR(255),
    field41        VARCHAR(255),
    field42        VARCHAR(255),
    field43        VARCHAR(255),
    field44        VARCHAR(255),
    field45        VARCHAR(255),
    field46        VARCHAR(255),
    field47        VARCHAR(255),
    field48        VARCHAR(255),
    field49        VARCHAR(255),
    field5         VARCHAR(255),
    field50        VARCHAR(255),
    field51        VARCHAR(255),
    field52        VARCHAR(255),
    field53        VARCHAR(255),
    field54        VARCHAR(255),
    field55        VARCHAR(255),
    field56        VARCHAR(255),
    field57        VARCHAR(255),
    field58        VARCHAR(255),
    field59        VARCHAR(255),
    field6         VARCHAR(255),
    field60        VARCHAR(255),
    field61        VARCHAR(255),
    field62        VARCHAR(255),
    field63        VARCHAR(255),
    field64        VARCHAR(255),
    field65        VARCHAR(255),
    field66        VARCHAR(255),
    field67        VARCHAR(255),
    field68        VARCHAR(255),
    field69        VARCHAR(255),
    field7         VARCHAR(255),
    field70        VARCHAR(255),
    field71        VARCHAR(255),
    field72        VARCHAR(255),
    field73        VARCHAR(255),
    field74        VARCHAR(255),
    field75        VARCHAR(255),
    field76        VARCHAR(255),
    field77        VARCHAR(255),
    field78        VARCHAR(255),
    field79        VARCHAR(255),
    field8         VARCHAR(255),
    field80        VARCHAR(255),
    field81        VARCHAR(255),
    field82        VARCHAR(255),
    field83        VARCHAR(255),
    field84        VARCHAR(255),
    field85        VARCHAR(255),
    field86        VARCHAR(255),
    field87        VARCHAR(255),
    field88        VARCHAR(255),
    field89        VARCHAR(255),
    field9         VARCHAR(255),
    field90        VARCHAR(255),
    field91        VARCHAR(255),
    field92        VARCHAR(255),
    field93        VARCHAR(255),
    field94        VARCHAR(255),
    field95        VARCHAR(255),
    field96        VARCHAR(255),
    field97        VARCHAR(255),
    field98        VARCHAR(255),
    field99        VARCHAR(255),
    transaction_id int8,
    PRIMARY KEY (id)
);

CREATE TABLE api_key
(
    id           int4 NOT NULL,
    api_key      VARCHAR(36),
    api_key_name VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE assigned_case
(
    id                int8 NOT NULL,
    assigned_by       int8,
    assigned_date     TIMESTAMP,
    assigned_to       int8,
    business_group_id int4,
    case_status_id    int4,
    note              TEXT,
    transaction_id    int8,
    PRIMARY KEY (id)
);

CREATE TABLE audit_log
(
    id         int8 NOT NULL,
    action     VARCHAR(255),
    date_audit TIMESTAMP,
    field      TEXT,
    role_name  VARCHAR(255),
    user_name  VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE auth_fa_user
(
    id                   int8 NOT NULL,
    email                VARCHAR(255),
    enabled              BOOLEAN,
    first_name           VARCHAR(255),
    force_reset_password BOOLEAN,
    last_name            VARCHAR(255),
    ldap_user            BOOLEAN,
    password             VARCHAR(60),
    role_changed         BOOLEAN,
    token_expired        BOOLEAN,
    user_name            VARCHAR(255),
    app_user             BOOLEAN,
    PRIMARY KEY (id)
);

CREATE TABLE auth_ldap_role
(
    id        int8 NOT NULL,
    group_cn  VARCHAR(255),
    role_id   int8,
    role_name VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE auth_password_reset_token
(
    id          int8 NOT NULL,
    expiry_date TIMESTAMP,
    token       VARCHAR(255),
    user_id     int8 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE auth_privilege
(
    id              int8 NOT NULL,
    description_key VARCHAR(255),
    name            VARCHAR(255),
    parent_id       int8,
    PRIMARY KEY (id)
);

CREATE TABLE auth_role
(
    id   int8 NOT NULL,
    name VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE auth_role_privilege
(
    role_id      int8 NOT NULL,
    privilege_id int8 NOT NULL,
    PRIMARY KEY (role_id, privilege_id)
);

CREATE TABLE AUTH_SESSION
(
    PRIMARY_ID            CHAR(36) NOT NULL,
    SESSION_ID            CHAR(36) NOT NULL,
    CREATION_TIME         BIGINT   NOT NULL,
    LAST_ACCESS_TIME      BIGINT   NOT NULL,
    MAX_INACTIVE_INTERVAL INT      NOT NULL,
    EXPIRY_TIME           BIGINT   NOT NULL,
    PRINCIPAL_NAME        VARCHAR(100),
    CONSTRAINT AUTH_SESSION_PK PRIMARY KEY (PRIMARY_ID)
);

CREATE TABLE AUTH_SESSION_ATTRIBUTES
(
    SESSION_PRIMARY_ID CHAR(36)     NOT NULL,
    ATTRIBUTE_NAME     VARCHAR(200) NOT NULL,
    ATTRIBUTE_BYTES    BYTEA        NOT NULL,
    CONSTRAINT AUTH_SESSION_ATTRIBUTES_PK PRIMARY KEY (SESSION_PRIMARY_ID, ATTRIBUTE_NAME),
    CONSTRAINT AUTH_SESSION_ATTRIBUTES_FK FOREIGN KEY (SESSION_PRIMARY_ID) REFERENCES AUTH_SESSION (PRIMARY_ID) ON DELETE CASCADE
);

CREATE TABLE auth_user_role
(
    user_id int8 NOT NULL,
    role_id int8 NOT NULL
);

CREATE TABLE auth_verification_token
(
    id          int8 NOT NULL,
    expiry_date TIMESTAMP,
    token       VARCHAR(255),
    user_id     int8 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE background_batch_status
(
    id            int4 NOT NULL,
    batch_status  int4,
    end_date      TIMESTAMP,
    file_name     VARCHAR(255),
    path          VARCHAR(255),
    start_date    TIMESTAMP,
    status_detail TEXT,
    PRIMARY KEY (id)
);

CREATE TABLE blocked_ip_values
(
    id       int4 NOT NULL,
    file_id  int4,
    ip_value VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE business_group
(
    id                 int4 NOT NULL,
    name               VARCHAR(255),
    supervisor_user_id int8,
    PRIMARY KEY (id)
);

CREATE TABLE case_additional_info
(
    id             int4 NOT NULL,
    justification  TEXT,
    transaction_id int8,
    PRIMARY KEY (id)
);

CREATE TABLE case_attachment
(
    id             int4 NOT NULL,
    created_date   TIMESTAMP,
    file_size      float8,
    file_name      VARCHAR(255),
    transaction_id int8,
    PRIMARY KEY (id)
);

CREATE TABLE case_business_auto_assign
(
    id                int4 NOT NULL,
    business_group_id int4,
    channel_id        int4,
    operation_type_id int4,
    PRIMARY KEY (id)
);

CREATE TABLE case_display_field
(
    id         int4 NOT NULL,
    field_id   int4,
    user_id    int8,
    event_type int4 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE case_status
(
    id          int2 NOT NULL,
    Description VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE cep_rule_matched_transaction
(
    transaction_id         int8 NOT NULL,
    rule_action_id         int4,
    cep_rule_id            int4,
    rule_name              VARCHAR(255),
    rule_critical_level_id int4,
    PRIMARY KEY (transaction_id)
);

CREATE TABLE channel
(
    id           int4 NOT NULL,
    description  VARCHAR(255),
    enabled      BOOLEAN,
    ignore_rules BOOLEAN,
    name         VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE classifier
(
    id                    int4    NOT NULL,
    cep_engine_enable     BOOLEAN,
    classifier_as_rule_id int4,
    classifier_order      int4,
    enabled               BOOLEAN NOT NULL,
    name                  VARCHAR(255),
    rule_description      VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE classifier_condition
(
    id                  int4    NOT NULL,
    classifier_id       int4,
    condition_type_id   int4,
    custom_list_id      int4,
    enabled             BOOLEAN NOT NULL,
    logical_operator_id int4,
    operator_id         int4,
    parameter_id        int4,
    value               VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE classifier_rule
(
    classifier_id int4,
    rule_id       int4
);

CREATE TABLE country
(
    id   int4 NOT NULL,
    name VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE custom_list
(
    id            int4 NOT NULL,
    creation_date TIMESTAMP,
    description   VARCHAR(255),
    name          VARCHAR(255),
    update_date   TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE custom_list_value
(
    id             int4 NOT NULL,
    custom_list_id int4,
    list_value     VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE event
(
    id           int8 NOT NULL,
    action_id    int2,
    channel_id   int4,
    event_date   TIMESTAMP,
    fingerprint  VARCHAR(255),
    operation_id int4,
    user_id      VARCHAR(255),
    geo_data_id  int8,
    PRIMARY KEY (id)
);

CREATE TABLE event_with_error
(
    id            int4 NOT NULL,
    creation_date TIMESTAMP,
    event_data    TEXT,
    exception     TEXT,
    PRIMARY KEY (id)
);

CREATE TABLE file_download
(
    id        int4 NOT NULL,
    enabled   BOOLEAN,
    path_file VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE file_entry
(
    id                  int4 NOT NULL,
    additional_field_id int4,
    auto_import         BOOLEAN,
    file_name           VARCHAR(255),
    file_path           VARCHAR(255),
    file_size_in_kb     float8,
    file_status         int4,
    status_detail       VARCHAR(255),
    type                int4,
    upload_date         TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_agent_value
(
    hash_id          int8 NOT NULL,
    browser_major    VARCHAR(255),
    browser_name     VARCHAR(255),
    browser_version  VARCHAR(255),
    cpu_architecture VARCHAR(255),
    device_model     VARCHAR(255),
    device_type      VARCHAR(255),
    device_vendor    VARCHAR(255),
    engine_name      VARCHAR(255),
    engine_version   VARCHAR(255),
    os_name          VARCHAR(255),
    os_version       VARCHAR(255),
    ua               VARCHAR(500),
    PRIMARY KEY (hash_id)
);

CREATE TABLE fingerprint_collection
(
    id              int8 NOT NULL,
    collection_uuid uuid,
    creation_date   TIMESTAMP,
    last_check_date TIMESTAMP,
    status          int4,
    type            int4,
    user_id         int8,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_device_value
(
    api_level              int4,
    device_id              VARCHAR(255),
    device_manufacturer    VARCHAR(255),
    device_name            VARCHAR(255),
    device_resolution      VARCHAR(255),
    fingerprint_compatible BOOLEAN,
    gps_compatible         BOOLEAN,
    rooted                 BOOLEAN,
    os_name                VARCHAR(255),
    os_version             VARCHAR(255),
    random_seed            VARCHAR(255),
    id                     int8 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_hash
(
    id                    int8 NOT NULL,
    creation_date         TIMESTAMP,
    fp_hash               VARCHAR(255),
    primary_hash          VARCHAR(255),
    secondary_hash        VARCHAR(255),
    fingerprint_server_id int8,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_response
(
    id               int8 NOT NULL,
    accuracy_percent float8,
    creation_date    TIMESTAMP,
    hash_known       BOOLEAN,
    result_code      int4,
    user_hash_id     int8,
    user_id          VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_server
(
    id                          int8   NOT NULL,
    cookie                      VARCHAR(60),
    etag                        VARCHAR(255),
    etag_first_request          VARCHAR(255),
    accuracy_radius             int4,
    city                        VARCHAR(255),
    country                     VARCHAR(255),
    latitude                    float8 NOT NULL,
    longitude                   float8 NOT NULL,
    subdivisions                VARCHAR(255),
    header_signature            VARCHAR(255),
    headers                     VARCHAR,
    http2                       VARCHAR,
    http2_fingerprint_hash      VARCHAR(255),
    ip                          VARCHAR(32),
    timestamp_utc               VARCHAR(255),
    tls_fingerprint             VARCHAR,
    tls_fingerprint_hash        VARCHAR(255),
    tls_fingerprint_ssl_greased VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_unverified_value
(
    id            int8 NOT NULL,
    creation_date TIMESTAMP,
    token         uuid,
    user_hash_id  int8,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_user
(
    id      int8 NOT NULL,
    user_id VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_user_hash
(
    id                        int8 NOT NULL,
    accuracy_percent          float8,
    apikey_id                 int4,
    count_used                int4,
    creation_date             TIMESTAMP,
    fingerprint_collection_id int8,
    hash_id                   int8,
    last_check_date           TIMESTAMP,
    status                    int4,
    user_id                   int8,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_web_hash
(
    incognito           BOOLEAN,
    audio               float8,
    cookies_enabled     BOOLEAN,
    crc_canvas          VARCHAR(255),
    crc_geometry        VARCHAR(255),
    crc_text            VARCHAR(255),
    crc_webgl           VARCHAR(255),
    do_not_track        VARCHAR(255),
    dom_blocker         VARCHAR(255),
    extended_fonts      VARCHAR,
    fonts               VARCHAR,
    font_pref_default   float8,
    font_pref_apple     float8,
    font_pref_min       float8,
    font_pref_mono      float8,
    font_pref_sans      float8,
    font_pref_serif     float8,
    font_pref_system    float8,
    gpu                 VARCHAR,
    logical_processors  int4,
    memory              float8,
    has_ad_block        BOOLEAN,
    has_add_behavior    BOOLEAN,
    has_indexed_db      BOOLEAN,
    has_lied_resolution BOOLEAN,
    has_lied_languages  BOOLEAN,
    has_lied_os         BOOLEAN,
    has_local_storage   BOOLEAN,
    has_open_database   BOOLEAN,
    has_session_storage BOOLEAN,
    language            VARCHAR(255),
    local_storage_id    VARCHAR(255),
    maths_acos          float8,
    maths_acosh         float8,
    maths_acos_pf       float8,
    maths_asin          float8,
    maths_asinh         float8,
    maths_asin_pf       float8,
    maths_atan          float8,
    maths_atanh         float8,
    maths_atanh_pf      float8,
    maths_cos           float8,
    maths_cosh          float8,
    maths_cosh_pf       float8,
    maths_exp           float8,
    maths_expm1         float8,
    maths_expm1_pf      float8,
    maths_log1p         float8,
    maths_log1p_pf      float8,
    maths_pow_pi        float8,
    maths_sin           float8,
    maths_sinh          float8,
    maths_sin_pf        float8,
    maths_tan           float8,
    maths_tanh          float8,
    maths_tanh_pf       float8,
    color_gamut         VARCHAR(255),
    contrast            int4,
    forced_colors       BOOLEAN,
    hdr                 BOOLEAN,
    inverted_colors     VARCHAR(255),
    monochrome          int4,
    reduced_motion      BOOLEAN,
    navigator_cpu_class VARCHAR(255),
    navigator_platform  VARCHAR(255),
    plugins             VARCHAR,
    random_seed         VARCHAR(255),
    color_depth         int4,
    frame_size          VARCHAR(255),
    available_height    int4,
    avaible_width       int4,
    screen_height       int4,
    screen_width        int4,
    timezone            VARCHAR(255),
    timezone_offset     int4,
    max_touch_points    int4,
    touch_event         BOOLEAN,
    touch_start         BOOLEAN,
    vendor              VARCHAR(255),
    vendor_flavors      VARCHAR(255),
    type                int4,
    id                  int8 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE geo_atm_data
(
    id          int8   NOT NULL,
    city        VARCHAR(255),
    country     VARCHAR(255),
    postal_code VARCHAR(255),
    code        VARCHAR(255),
    latitude    float8 NOT NULL,
    longitude   float8 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE geo_ip_data
(
    transaction_id int8 NOT NULL,
    city           VARCHAR(255),
    country        VARCHAR(255),
    postal_code    VARCHAR(255),
    continent_name VARCHAR(255),
    user_id        VARCHAR(255),
    PRIMARY KEY (transaction_id)
);

CREATE TABLE geo_profile_data
(
    id             int8 NOT NULL,
    city           VARCHAR(255),
    country        VARCHAR(255),
    postal_code    VARCHAR(255),
    locality       VARCHAR(255),
    province_state VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE geo_user_profile
(
    id              int8    NOT NULL,
    count           int4,
    last_check_date TIMESTAMP,
    latitude        float8  NOT NULL,
    longitude       float8  NOT NULL,
    user_id         VARCHAR(255),
    valid           BOOLEAN NOT NULL,
    geo_data_id     int8,
    PRIMARY KEY (id)
);

CREATE TABLE geodata_error_parameter
(
    id                int4   NOT NULL,
    amount            float8 NOT NULL,
    channel_id        int4,
    credit_account    VARCHAR(255),
    event_date        TIMESTAMP,
    date_ocurrence    TIMESTAMP,
    debit_account     VARCHAR(255),
    user_id           VARCHAR(255),
    operation_type_id int4,
    PRIMARY KEY (id)
);

CREATE TABLE history_score
(
    id             int4 NOT NULL,
    creation_date  TIMESTAMP,
    score          int4 NOT NULL,
    transaction_id int8,
    user_id        VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE last_assigned_case
(
    id                int4 NOT NULL,
    business_group_id int4,
    transaction_id    int8,
    user_id           int8,
    PRIMARY KEY (id)
);

CREATE TABLE ml_batch
(
    id                  int4 NOT NULL,
    batch_state         VARCHAR(255),
    command             VARCHAR(255),
    description         VARCHAR(255),
    end_date            TIMESTAMP,
    parameter           VARCHAR(255),
    proc_item_quantity  int4,
    stage               VARCHAR(255),
    start_date          TIMESTAMP,
    total_item_quantity int4,
    type                VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE ml_experiment
(
    id                   int8 NOT NULL,
    date_from            TIMESTAMP,
    date_to              TIMESTAMP,
    description          VARCHAR(255),
    id_network           int8,
    quantity_transaction int8,
    PRIMARY KEY (id)
);

CREATE TABLE ml_feature
(
    id     int4    NOT NULL,
    cyclic BOOLEAN NOT NULL,
    name   VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE ml_network
(
    id                          int4 NOT NULL,
    date_from                   TIMESTAMP,
    date_to                     TIMESTAMP,
    end_radius                  float4,
    end_rate                    float4,
    feature_quantity            int4,
    grid_side                   int4,
    iteration                   int4 NOT NULL,
    name                        VARCHAR(255),
    network_state               VARCHAR(255),
    network_values              TEXT,
    percentage                  int4 NOT NULL,
    profile_calculated_quantity int4,
    profile_date                TIMESTAMP,
    profile_day_quantity        int4,
    start_radius                float4,
    start_rate                  float4,
    transaction_quantity        int4,
    PRIMARY KEY (id)
);

CREATE TABLE ml_network_feature
(
    feature_id int4 NOT NULL,
    network_id int4 NOT NULL,
    PRIMARY KEY (feature_id, network_id)
);

CREATE TABLE ml_network_log
(
    network_id                   int4   NOT NULL,
    transaction_id               int8   NOT NULL,
    account_id                   VARCHAR(255),
    milis                        int8   NOT NULL,
    network_log_date             TIMESTAMP,
    neuron                       int4   NOT NULL,
    quantity_transaction_profile int4,
    score                        float4 NOT NULL,
    PRIMARY KEY (network_id, transaction_id)
);

CREATE TABLE ml_network_operation_type
(
    network_id        int4 NOT NULL,
    operation_type_id int4 NOT NULL,
    PRIMARY KEY (network_id, operation_type_id)
);

CREATE TABLE ml_som_account_profile
(
    network_id           int4 NOT NULL,
    account_id           VARCHAR(255),
    profile              TEXT,
    som_date             TIMESTAMP,
    transaction_quantity int4,
    PRIMARY KEY (network_id, account_id)
);

CREATE TABLE ml_som_account_profile_usable
(
    network_id           int4 NOT NULL,
    account_id           VARCHAR(255),
    profile              TEXT,
    som_date             TIMESTAMP,
    transaction_quantity int4,
    PRIMARY KEY (network_id, account_id)
);

CREATE TABLE ml_transaction
(
    transaction_id    int8 NOT NULL,
    amount            float8,
    channel_id        int4,
    credit_account    VARCHAR(255),
    debit_account     VARCHAR(255),
    event_date        TIMESTAMP,
    ip                VARCHAR(255),
    operation_type_id int4,
    result            int2,
    PRIMARY KEY (transaction_id)
);

CREATE TABLE ml_transaction_feature
(
    feature_id     int4   NOT NULL,
    transaction_id int8   NOT NULL,
    value          float8 NOT NULL,
    PRIMARY KEY (feature_id, transaction_id)
);

CREATE TABLE ml_transaction_result
(
    transaction_id         int8   NOT NULL,
    experiment             int4   NOT NULL,
    neuron                 int4   NOT NULL,
    quantity_items_profile int4,
    result                 float4 NOT NULL,
    PRIMARY KEY (transaction_id)
);

CREATE TABLE ml_usable_network
(
    id                          int4 NOT NULL,
    date_from                   TIMESTAMP,
    date_to                     TIMESTAMP,
    end_radius                  float4,
    end_rate                    float4,
    feature_quantity            int4,
    grid_side                   int4,
    iteration                   int4 NOT NULL,
    name                        VARCHAR(255),
    network_state               VARCHAR(255),
    network_values              TEXT,
    percentage                  int4 NOT NULL,
    profile_calculated_quantity int4,
    profile_date                TIMESTAMP,
    profile_day_quantity        int4,
    start_radius                float4,
    start_rate                  float4,
    transaction_quantity        int4,
    PRIMARY KEY (id)
);

CREATE TABLE ml_usable_network_feature
(
    feature_id int4 NOT NULL,
    network_id int4 NOT NULL,
    PRIMARY KEY (feature_id, network_id)
);

CREATE TABLE notification_config
(
    id                   int4    NOT NULL,
    business_group_id    int4,
    enabled              BOOLEAN NOT NULL,
    notification_type_id int4,
    notification_value   VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE notification_type
(
    id       int4 NOT NULL,
    api_key  VARCHAR(255),
    enabled  BOOLEAN,
    endpoint VARCHAR(255),
    name     VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE operation_type
(
    id            int4 NOT NULL,
    description   VARCHAR(255),
    enabled       BOOLEAN,
    event_type_id int4,
    name          VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE parameter
(
    id                int4    NOT NULL,
    column_name       VARCHAR(255),
    enabled           BOOLEAN NOT NULL,
    event_type_id     int4,
    name              VARCHAR(255),
    parameter_info_id int4,
    parameter_type_id int4,
    user_generated    BOOLEAN,
    visibility        int4    NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE parameter_business
(
    id                 int4    NOT NULL,
    business           VARCHAR(255),
    daily_max_amount   float8,
    daily_max_count    int4,
    enabled            BOOLEAN NOT NULL,
    monthly_max_amount float8,
    monthly_max_count  int4,
    weekly_max_amount  float8,
    weekly_max_count   int4,
    CONSTRAINT "UN_business" UNIQUE (business),
    PRIMARY KEY (id)
);

CREATE TABLE parameter_channel_operation
(
    id                int4 NOT NULL,
    channel_id        int4 NOT NULL,
    enabled           BOOLEAN NOT NULL,
    operation_type_id int4 NOT NULL,
    parameter_id      int4 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE parameter_query_channel_operation
(
    id                int4 NOT NULL,
    channel_id        int4,
    insert_query      VARCHAR(255),
    operation_type_id int4,
    select_query      VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE profile_by_operation
(
    id                     int4 NOT NULL,
    accumulated_amount     float8,
    event_count            int4,
    max_amount             float8,
    operation_type_id      int4,
    profile_period_info_id int4,
    PRIMARY KEY (id)
);

CREATE TABLE profile_day_counter
(
    id           int4 NOT NULL,
    channel_id   int4,
    event_count  int4,
    profile_date TIMESTAMP,
    user_id      VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE profile_period_info_by_credit_account
(
    id                     int4 NOT NULL,
    accumulated_amount     float8,
    credit_account         VARCHAR(255),
    max_amount             float8,
    profile_period_info_id int4,
    PRIMARY KEY (id)
);

CREATE TABLE profile_period_information
(
    id                 int4 NOT NULL,
    accumulated_amount float8,
    channel_id         int4,
    max_amount         float8,
    period_month       int4,
    period_year        int4,
    transaction_count  int4,
    user_id            VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE report
(
    id          int4 NOT NULL,
    json_format VARCHAR,
    name        VARCHAR(255),
    subtitle    VARCHAR(255),
    title       VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE report_aggregate
(
    id                    int4 NOT NULL,
    aggregate_function_id int4,
    field_id              int4,
    report_group_id       int4 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_display_field
(
    id          int4 NOT NULL,
    field_id    int4,
    field_order int4,
    report_id   int4 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_filter_channel
(
    id         int4 NOT NULL,
    channel_id int4,
    report_id  int4 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_filter_operation_type
(
    id                int4 NOT NULL,
    operation_type_id int4,
    report_id         int4 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_filter_rule
(
    id        int4 NOT NULL,
    rule_id   int4,
    report_id int4 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_group
(
    id          int4 NOT NULL,
    field_id    int4,
    group_order int4,
    report_id   int4 NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE rule_auto_assign_case
(
    id                int4 NOT NULL,
    business_group_id int4,
    rule_id           int4,
    user_id           int8,
    PRIMARY KEY (id)
);

CREATE TABLE rule_business_group
(
    id                   int4    NOT NULL,
    enabled              BOOLEAN NOT NULL,
    notification_message VARCHAR,
    business_group_id    int4,
    rule_id              int4,
    PRIMARY KEY (id)
);

CREATE TABLE rule_condition
(
    id                     int4 NOT NULL,
    condition_rule_type_id int4,
    condition_type_id      int4,
    condition_value        VARCHAR(255),
    connector_id           int4,
    custom_list_id         int4,
    delta_type_id          int4,
    field_id               int4,
    group_id               int4,
    operator_id            int4,
    parent_group_id        int4,
    rule_id                int4,
    PRIMARY KEY (id)
);

CREATE TABLE rule_event_statement
(
    id        int4 NOT NULL,
    context   BOOLEAN,
    rule_id   int4,
    statement TEXT,
    PRIMARY KEY (id)
);

CREATE TABLE rule_item
(
    id                int4 NOT NULL,
    action_id         int2,
    channel_id        int4,
    critical_level_id int4,
    enabled           BOOLEAN,
    impact_id         int4,
    json_format       TEXT,
    name              VARCHAR(255),
    operation_type_id int4,
    position          int4,
    probability_id    int4,
    rule_type_id      int4,
    score             int4,
    PRIMARY KEY (id)
);

CREATE TABLE rule_matched_event
(
    id                     int4 NOT NULL,
    event_id               int8 NOT NULL,
    event_trace_id         uuid,
    triggered_date         TIMESTAMP,
    triggered_rule_item_id int4,
    PRIMARY KEY (id)
);

CREATE TABLE rule_matched_transaction
(
    transaction_id         int8 NOT NULL,
    triggered_rule_item_id int4,
    rule_name VARCHAR(255),
    rule_critical_level_id int4,
    PRIMARY KEY (transaction_id)
);

CREATE TABLE rule_update
(
    id                          int4 NOT NULL,
    rule_cep_last_update        TIMESTAMP,
    rule_classifier_last_update TIMESTAMP,
    rule_engine_last_update     TIMESTAMP,
    rule_parameter_business     TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE swift
(
    id         int4 NOT NULL,
    bank       VARCHAR(255),
    branch     VARCHAR(255),
    city       VARCHAR(255),
    code       VARCHAR(255),
    country_id int4,
    PRIMARY KEY (id)
);

CREATE TABLE system_parameter
(
    id             int4 NOT NULL,
    config_value   VARCHAR(255),
    parameter_name VARCHAR(255),
    CONSTRAINT system_parameter_parameter_name_unique UNIQUE (parameter_name)
);

CREATE TABLE transaction_entity
(
    id                int8 NOT NULL,
    action_id         int2,
    amount            float8,
    case_status_id    int2,
    channel_id        int4,
    credit_account    VARCHAR(255),
    debit_account     VARCHAR(255),
    event_date        TIMESTAMP,
    ip                VARCHAR(45),
    operation_type_id int4,
    time              TIME,
    user_id           VARCHAR(255),
    guid              VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE transaction_exception
(
    id                int8   NOT NULL,
    amount            float8 NOT NULL,
    channel_id        int4,
    credit_account    VARCHAR(255),
    debit_account     VARCHAR(255),
    operation_type_id int4,
    transaction_id    int8,
    user_id           VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE user_business_daily_stat
(
    id                    int8 NOT NULL,
    user_business_stat_id int8,
    stat_date             TIMESTAMP,
    stat_month            int4,
    stat_year             int4,
    amount_accumulated    float8,
    transaction_count     int4,
    day_of_month          int4,
    PRIMARY KEY (id)
);

CREATE TABLE user_business_group
(
    id                int4 NOT NULL,
    business_group_id int4,
    user_id           int8,
    PRIMARY KEY (id)
);

CREATE TABLE user_business_monthly_stat
(
    id                    int8 NOT NULL,
    user_business_stat_id int8,
    stat_date             TIMESTAMP,
    stat_month            int4,
    stat_year             int4,
    amount_accumulated    float8,
    transaction_count     int4,
    PRIMARY KEY (id)
);

CREATE TABLE user_business_stat
(
    id                  int8         NOT NULL,
    business_name       VARCHAR(255) NOT NULL,
    daily_max_count     int4,
    daily_next_update   TIMESTAMP,
    max_amount          float8,
    monthly_max_count   int4,
    monthly_next_update TIMESTAMP,
    new_daily_profile   BOOLEAN,
    new_monthly_profile BOOLEAN,
    new_weekly_profile  BOOLEAN,
    user_id             VARCHAR(255) NOT NULL,
    weekly_max_count    int4,
    weekly_next_update  TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE user_business_weekly_stat
(
    id                    int8 NOT NULL,
    user_business_stat_id int8,
    stat_date             TIMESTAMP,
    stat_month            int4,
    stat_year             int4,
    amount_accumulated    float8,
    transaction_count     int4,
    week_of_month         int4,
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_daily
(
    id                      int8 NOT NULL,
    channel_id              int8,
    last_transaction_date   TIMESTAMP,
    transaction_accumulated int8,
    transaction_date        TIMESTAMP,
    user_id                 VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_monthly
(
    id                      int8 NOT NULL,
    channel_id              int8,
    last_transaction_date   TIMESTAMP,
    transaction_accumulated int8,
    transaction_month       int8,
    transaction_year        int8,
    user_id                 VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_summary
(
    id                 int4   NOT NULL,
    average            float8 NOT NULL,
    max_amount         float8,
    min_amount         float8,
    standard_deviation float8,
    user_id            VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_summary_history
(
    id                    int4 NOT NULL,
    last_processed_date   TIMESTAMP,
    max_amount            float8,
    min_amount            float8,
    total_amount          float8,
    transactions_quantity int4,
    user_id               VARCHAR(255),
    week_number           int4,
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_weekly
(
    id                      int8 NOT NULL,
    channel_id              int8,
    last_transaction_date   TIMESTAMP,
    transaction_accumulated int8,
    transaction_weekly      int8,
    transaction_year        int8,
    user_id                 VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE audit_revision
(
    id         int4 NOT NULL,
    ip         VARCHAR(255),
    timestamp  int8 NOT NULL,
    user_agent VARCHAR(255),
    username   VARCHAR(255),
    PRIMARY KEY (id)
);
CREATE TABLE auth_fa_user_history
(
    id                   int8 NOT NULL,
    rev                  int4 NOT NULL,
    revtype              int2,
    email                VARCHAR(255),
    enabled              BOOLEAN,
    first_name           VARCHAR(255),
    force_reset_password BOOLEAN,
    last_name            VARCHAR(255),
    ldap_user            BOOLEAN,
    password             VARCHAR(60),
    role_changed         BOOLEAN,
    token_expired        BOOLEAN,
    app_user             BOOLEAN,
    user_name            VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE auth_ldap_role_history
(
    id        int8 NOT NULL,
    rev       int4 NOT NULL,
    revtype   int2,
    group_cn  VARCHAR(255),
    role_id   int8,
    role_name VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE auth_role_history
(
    id      int8 NOT NULL,
    rev     int4 NOT NULL,
    revtype int2,
    name    VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE auth_role_privilege_history
(
    rev          int4 NOT NULL,
    role_id      int8 NOT NULL,
    privilege_id int8 NOT NULL,
    revtype      int2,
    PRIMARY KEY (rev, role_id, privilege_id)
);

CREATE TABLE auth_user_role_history
(
    rev     int4 NOT NULL,
    user_id int8 NOT NULL,
    role_id int8 NOT NULL,
    revtype int2,
    PRIMARY KEY (rev, user_id, role_id)
);

CREATE TABLE channel_history
(
    id           int4 NOT NULL,
    rev          int4 NOT NULL,
    revtype      int2,
    description  VARCHAR(255),
    enabled      BOOLEAN,
    ignore_rules BOOLEAN,
    name         VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE operation_type_history
(
    id            int4 NOT NULL,
    rev           int4 NOT NULL,
    revtype       int2,
    description   VARCHAR(255),
    enabled       BOOLEAN,
    event_type_id int4,
    name          VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE parameter_channel_operation_history
(
    id                int4 NOT NULL,
    rev               int4 NOT NULL,
    revtype           int2,
    channel_id        int4,
    enabled           BOOLEAN,
    operation_type_id int4,
    parameter_id      int4,
    PRIMARY KEY (id, rev)
);

CREATE TABLE parameter_history
(
    id                int4 NOT NULL,
    rev               int4 NOT NULL,
    revtype           int2,
    column_name       VARCHAR(255),
    enabled           BOOLEAN,
    event_type_id     int4,
    name              VARCHAR(255),
    parameter_info_id int4,
    parameter_type_id int4,
    user_generated    BOOLEAN,
    visibility        int4,
    PRIMARY KEY (id, rev)
);

CREATE TABLE rule_condition_history
(
    id                     int4 NOT NULL,
    rev                    int4 NOT NULL,
    revtype                int2,
    condition_rule_type_id int4,
    condition_type_id      int4,
    condition_value        VARCHAR(255),
    connector_id           int4,
    custom_list_id         int4,
    delta_type_id          int4,
    field_id               int4,
    group_id               int4,
    operator_id            int4,
    parent_group_id        int4,
    rule_id                int4,
    PRIMARY KEY (id, rev)
);

CREATE TABLE rule_item_history
(
    id                int4 NOT NULL,
    rev               int4 NOT NULL,
    revtype           int2,
    action_id         int2,
    channel_id        int4,
    critical_level_id int4,
    enabled           BOOLEAN,
    impact_id         int4,
    json_format       TEXT,
    name              VARCHAR(255),
    operation_type_id int4,
    position          int4,
    probability_id    int4,
    rule_type_id      int4,
    score             int4,
    PRIMARY KEY (id, rev)
);

CREATE TABLE system_parameter_history
(
    id             int4 NOT NULL,
    rev            int4 NOT NULL,
    revtype        int2,
    config_value   VARCHAR(255),
    parameter_name VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE event_imports (
   id VARCHAR(36) PRIMARY KEY,
   status VARCHAR(255),
   event_type VARCHAR(255) NOT NULL,
   file_name VARCHAR(255),
   file_size BIGINT,
   created_at TIMESTAMP,
   updated_at TIMESTAMP
);

CREATE TABLE event_entity_import (
     id BIGINT PRIMARY KEY,
     import_id VARCHAR(36) NOT NULL,
     amount FLOAT,
     channel_id INTEGER,
     credit_account VARCHAR(255),
     debit_account VARCHAR(255),
     event_date TIMESTAMP,
     operation_type_id INTEGER,
     user_id VARCHAR(255),
     CONSTRAINT FK_EventEntityImport_EventImports FOREIGN KEY (import_id)
         REFERENCES event_imports (id)
         ON DELETE CASCADE
);

CREATE TABLE event_additional_fields_import (
    id BIGINT PRIMARY KEY,
    event_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    value TEXT,
    CONSTRAINT FK_EventAdditionalFieldsImport_EventEntityImport FOREIGN KEY (event_id)
        REFERENCES event_entity_import (id)
        ON DELETE CASCADE
);

CREATE TABLE fingerprint_agent_value_history (
    hash_id BIGINT NOT NULL PRIMARY KEY,
    browser_major VARCHAR(255),
    browser_name VARCHAR(255),
    browser_version VARCHAR(255),
    cpu_architecture VARCHAR(255),
    device_model VARCHAR(255),
    device_type VARCHAR(255),
    device_vendor VARCHAR(255),
    engine_name VARCHAR(255),
    engine_version VARCHAR(255),
    os_name VARCHAR(255),
    os_version VARCHAR(255),
    ua VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE TABLE fingerprint_hash_history (
    id BIGINT NOT NULL PRIMARY KEY,
    creation_date TIMESTAMP,
    fp_hash VARCHAR(255),
    primary_hash VARCHAR(255),
    secondary_hash VARCHAR(255),
    fingerprint_server_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE TABLE fingerprint_response_history (
    id BIGINT NOT NULL PRIMARY KEY,
    accuracy_percent FLOAT,
    creation_date TIMESTAMP,
    hash_known BOOLEAN,
    result_code INTEGER,
    user_hash_id BIGINT,
    user_id VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE TABLE fingerprint_unverified_value_history (
    id BIGINT NOT NULL PRIMARY KEY,
    creation_date TIMESTAMP,
    token uuid,
    user_hash_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE TABLE fingerprint_user_hash_history (
    id BIGINT NOT NULL PRIMARY KEY,
    accuracy_percent FLOAT,
    apikey_id INTEGER,
    count_used INTEGER,
    creation_date TIMESTAMP,
    fingerprint_collection_id BIGINT,
    hash_id BIGINT,
    last_check_date TIMESTAMP,
    status INTEGER,
    user_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE TABLE fingerprint_collection_history (
    id BIGINT NOT NULL PRIMARY KEY,
    collection_uuid uuid,
    creation_date TIMESTAMP,
    last_check_date TIMESTAMP,
    status INTEGER,
    type INTEGER,
    user_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE TABLE fingerprint_web_hash_history (
    id BIGINT NOT NULL PRIMARY KEY,
    incognito BOOLEAN,
    audio FLOAT,
    cookies_enabled BOOLEAN,
    crc_canvas VARCHAR(255),
    crc_geometry VARCHAR(255),
    crc_text VARCHAR(255),
    crc_webgl VARCHAR(255),
    do_not_track VARCHAR(255),
    dom_blocker VARCHAR(255),
    extended_fonts TEXT,
    fonts TEXT,
    font_pref_default FLOAT,
    font_pref_apple FLOAT,
    font_pref_min FLOAT,
    font_pref_mono FLOAT,
    font_pref_sans FLOAT,
    font_pref_serif FLOAT,
    font_pref_system FLOAT,
    gpu TEXT,
    logical_processors INTEGER,
    memory FLOAT,
    has_ad_block BOOLEAN,
    has_add_behavior BOOLEAN,
    has_indexed_db BOOLEAN,
    has_lied_resolution BOOLEAN,
    has_lied_languages BOOLEAN,
    has_lied_os BOOLEAN,
    has_local_storage BOOLEAN,
    has_open_database BOOLEAN,
    has_session_storage BOOLEAN,
    language VARCHAR(255),
    local_storage_id VARCHAR(255),
    maths_acos FLOAT,
    maths_acosh FLOAT,
    maths_acos_pf FLOAT,
    maths_asin FLOAT,
    maths_asinh FLOAT,
    maths_asin_pf FLOAT,
    maths_atan FLOAT,
    maths_atanh FLOAT,
    maths_atanh_pf FLOAT,
    maths_cos FLOAT,
    maths_cosh FLOAT,
    maths_cosh_pf FLOAT,
    maths_exp FLOAT,
    maths_expm1 FLOAT,
    maths_expm1_pf FLOAT,
    maths_log1p FLOAT,
    maths_log1p_pf FLOAT,
    maths_pow_pi FLOAT,
    maths_sin FLOAT,
    maths_sinh FLOAT,
    maths_sin_pf FLOAT,
    maths_tan FLOAT,
    maths_tanh FLOAT,
    maths_tanh_pf FLOAT,
    color_gamut VARCHAR(255),
    contrast INTEGER,
    forced_colors BOOLEAN,
    hdr BOOLEAN,
    inverted_colors VARCHAR(255),
    monochrome INTEGER,
    reduced_motion BOOLEAN,
    navigator_cpu_class VARCHAR(255),
    navigator_platform VARCHAR(255),
    plugins TEXT,
    random_seed VARCHAR(255),
    color_depth INTEGER,
    frame_size VARCHAR(255),
    available_height INTEGER,
    avaible_width INTEGER,
    screen_height INTEGER,
    screen_width INTEGER,
    timezone VARCHAR(255),
    timezone_offset INTEGER,
    max_touch_points INTEGER,
    touch_event BOOLEAN,
    touch_start BOOLEAN,
    vendor VARCHAR(255),
    vendor_flavors VARCHAR(255),
    type INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

CREATE TABLE fingerprint_device_value_history (
    id BIGINT NOT NULL PRIMARY KEY,
    api_level INTEGER,
    device_id VARCHAR(255),
    device_manufacturer VARCHAR(255),
    device_name VARCHAR(255),
    device_resolution VARCHAR(255),
    fingerprint_compatible BOOLEAN,
    gps_compatible BOOLEAN,
    rooted BOOLEAN,
    os_name VARCHAR(255),
    os_version VARCHAR(255),
    random_seed VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255)
);

-- SEQUENCES
CREATE SEQUENCE revision_id_seq;
CREATE SEQUENCE accumulator_id_seq;
CREATE SEQUENCE accumulator_parameter_id_seq;
CREATE SEQUENCE accumulator_rule_condition_id_seq;
CREATE SEQUENCE additional_parameter_event_id_seq;
CREATE SEQUENCE additional_parameter_transaction_id_seq INCREMENT BY 100;
CREATE SEQUENCE api_key_id_seq;
CREATE SEQUENCE assigned_case_id_seq;
CREATE SEQUENCE audit_log_id_seq;
CREATE SEQUENCE auth_fa_user_id_seq;
CREATE SEQUENCE auth_ldap_role_id_seq;
CREATE SEQUENCE auth_password_reset_token_id_seq;
CREATE SEQUENCE auth_privilege_id_seq;
CREATE SEQUENCE auth_role_id_seq;
CREATE SEQUENCE auth_verification_token_id_seq;
CREATE SEQUENCE background_batch_status_id_seq;
CREATE SEQUENCE blocked_ip_values_id_seq INCREMENT BY 100;
CREATE SEQUENCE business_group_id_seq;
CREATE SEQUENCE case_additional_info_id_seq;
CREATE SEQUENCE case_attachment_id_seq;
CREATE SEQUENCE case_business_auto_assign_id_seq;
CREATE SEQUENCE case_display_field_id_seq;
CREATE SEQUENCE case_status_id_seq;
CREATE SEQUENCE channel_id_seq;
CREATE SEQUENCE classifier_condition_id_seq;
CREATE SEQUENCE classifier_id_seq;
CREATE SEQUENCE country_id_seq;
CREATE SEQUENCE custom_list_id_seq;
CREATE SEQUENCE custom_list_value_id_seq INCREMENT BY 100;
CREATE SEQUENCE event_id_seq;
CREATE SEQUENCE event_with_error_id_seq;
CREATE SEQUENCE file_download_id_seq;
CREATE SEQUENCE file_entry_id_seq;
CREATE SEQUENCE fingerprint_collection_id_seq;
CREATE SEQUENCE fingerprint_hash_id_seq;
CREATE SEQUENCE fingerprint_response_id_seq INCREMENT BY 100;
CREATE SEQUENCE fingerprint_server_id_seq;
CREATE SEQUENCE fingerprint_unverified_value_id_seq;
CREATE SEQUENCE fingerprint_user_hash_id_seq;
CREATE SEQUENCE fingerprint_user_id_seq;
CREATE SEQUENCE geo_atm_data_id_seq;
CREATE SEQUENCE geodata_error_parameter_id_seq;
CREATE SEQUENCE history_score_id_seq INCREMENT BY 100;
CREATE SEQUENCE last_assigned_case_id_seq;
CREATE SEQUENCE ml_batch_id_seq;
CREATE SEQUENCE ml_experiment_id_seq;
CREATE SEQUENCE ml_feature_id_seq;
CREATE SEQUENCE ml_network_id_seq;
CREATE SEQUENCE ml_transaction_result_id_seq;
CREATE SEQUENCE notification_config_id_seq;
CREATE SEQUENCE notification_type_id_seq;
CREATE SEQUENCE operation_type_id_seq;
CREATE SEQUENCE parameter_business_id_seq;
CREATE SEQUENCE parameter_channel_operation_id_seq;
CREATE SEQUENCE parameter_id_seq;
CREATE SEQUENCE parameter_query_channel_operation_id_seq;
CREATE SEQUENCE profile_by_operation_id_seq;
CREATE SEQUENCE profile_day_counter_id_seq;
CREATE SEQUENCE profile_geo_data_id_seq;
CREATE SEQUENCE profile_period_info_by_credit_account_id_seq;
CREATE SEQUENCE profile_period_information_id_seq;
CREATE SEQUENCE report_aggregate_id_seq;
CREATE SEQUENCE report_display_field_id_seq;
CREATE SEQUENCE report_filter_channel_id_seq;
CREATE SEQUENCE report_filter_operation_type_id_seq;
CREATE SEQUENCE report_filter_rule_id_seq;
CREATE SEQUENCE report_group_id_seq;
CREATE SEQUENCE report_id_seq;
CREATE SEQUENCE rule_auto_assign_case_id_seq;
CREATE SEQUENCE rule_business_group_id_seq;
CREATE SEQUENCE rule_condition_id_seq;
CREATE SEQUENCE rule_event_statement_id_seq;
CREATE SEQUENCE rule_item_id_seq;
CREATE SEQUENCE rule_matched_event_id_seq;
CREATE SEQUENCE rule_update_id_seq;
CREATE SEQUENCE swift_id_seq;
CREATE SEQUENCE transaction_entity_id_seq INCREMENT BY 100;
CREATE SEQUENCE transaction_exception_id_seq;
CREATE SEQUENCE user_business_daily_stat_id_seq;
CREATE SEQUENCE user_business_group_id_seq;
CREATE SEQUENCE user_business_monthly_stat_id_seq;
CREATE SEQUENCE user_business_stat_id_seq;
CREATE SEQUENCE user_business_weekly_stat_id_seq;
CREATE SEQUENCE user_geo_profile_id_seq;
CREATE SEQUENCE user_transaction_daily_id_seq;
CREATE SEQUENCE user_transaction_monthly_id_seq;
CREATE SEQUENCE user_transaction_summary_history_id_seq;
CREATE SEQUENCE user_transaction_summary_id_seq;
CREATE SEQUENCE user_transaction_weekly_id_seq;
CREATE SEQUENCE event_entity_import_id_seq INCREMENT BY 100;
CREATE SEQUENCE event_additional_fields_import_id_seq;
CREATE SEQUENCE system_parameter_id_seq;
