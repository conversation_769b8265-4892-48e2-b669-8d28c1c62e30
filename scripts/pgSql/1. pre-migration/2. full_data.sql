/*
========================================================================
Version     : 5.33.1
Type        : FULL DATA
DB          : POSTGRES
========================================================================
*/

-- Custom List
INSERT INTO custom_list (id, name, description, creation_date, update_date)
VALUES (-2, 'IPs bloqueadas', 'Lista de ips bloqueadas', NOW(), NOW());

-- Auth Role
INSERT INTO auth_role (id, name)
VALUES (NEXTVAL('auth_role_id_seq'), 'ADMINISTRADOR')
     , (NEXTVAL('auth_role_id_seq'), 'OPERADOR')
     , (NEXTVAL('auth_role_id_seq'), 'AUDITOR')
     , (NEXTVAL('auth_role_id_seq'), 'CONFIG')
     , (NEXTVAL('auth_role_id_seq'), 'CASEMANAGEMENT');

-- Auth User
INSERT INTO auth_fa_user (id, email, enabled, first_name, force_reset_password, last_name, password,
                          token_expired, user_name, ldap_user, role_changed, app_user)
VALUES (NEXTVAL('auth_fa_user_id_seq'), '<EMAIL>', TRUE, 'Administrador', FALSE, 'VUSecurity',
        '$2a$11$wL3LI50Jw/p4Mkqgz9Le3e1bgZ0mC6lApkoSQUa2dP7.d8MbRDw56', FALSE, 'faadmin', FALSE, FALSE, FALSE);

-- Auth User Role
INSERT INTO auth_user_role(user_id, role_id) VALUES (1, 1);

-- Business Group
INSERT INTO business_group (id, name, supervisor_user_id)
VALUES (NEXTVAL('business_group_id_seq'), 'Administrador', (SELECT id FROM auth_fa_user WHERE user_name = 'faadmin'));

-- User Business Group
INSERT INTO user_business_group (id, business_group_id, user_id)
VALUES (NEXTVAL('user_business_group_id_seq'), 1, (SELECT id FROM auth_fa_user WHERE user_name = 'faadmin'));

-- Rule Update
INSERT INTO rule_update (id, rule_engine_last_update, rule_cep_last_update, rule_parameter_business, rule_classifier_last_update)
VALUES (1, '2003-10-09 00:00:00.000000 +00:00', '2003-10-09 00:00:00.000000 +00:00', '2003-10-09 00:00:00.000000 +00:00', '2003-10-09 00:00:00.000000 +00:00');

-- Case Status
INSERT INTO case_status (id, description)
VALUES (NEXTVAL('case_status_id_seq'), 'PENDING'),
       (NEXTVAL('case_status_id_seq'), 'LEGIT'),
       (NEXTVAL('case_status_id_seq'), 'FRAUD'),
       (NEXTVAL('case_status_id_seq'), 'INPROGRESS'),
       (NEXTVAL('case_status_id_seq'), 'ASSIGNED'),
       (NEXTVAL('case_status_id_seq'), 'NONE'),
       (NEXTVAL('case_status_id_seq'), 'REJECTED');

-- Channel
INSERT INTO channel (name, description, enabled, id, ignore_rules)
VALUES ('Fingerprint', 'Fingerprint', TRUE, -1, FALSE),
       ('CLASSIFIER_RULE_DEFAULT_CHANNEL', 'CLASSIFIER_RULE_DEFAULT_CHANNEL', TRUE, -2, FALSE);

-- Operation Type
INSERT INTO operation_type (name, description, enabled, id, event_type_id)
VALUES ('BrowserFingerprint V2', 'browser fingerprint', TRUE, -1, 1),
       ('DeviceFingerprint', 'Device Fingerprint', TRUE, -2, 1),
       ('CLASSIFIER_RULE_DEFAULT_OPERATION_TYPE', 'CLASSIFIER_RULE_DEFAULT_OPERATION_TYPE', TRUE, -3, 1),
       (N'BrowserFingerprint V1', N'BrowserFingerprint V1', TRUE, -4, 1);

-- File Download
INSERT INTO file_download (id, path_file, enabled)
VALUES (NEXTVAL('file_download_id_seq'), 'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_anonymous.netset', TRUE),
       (NEXTVAL('file_download_id_seq'), 'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_level1.netset', TRUE),
       (NEXTVAL('file_download_id_seq'), 'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_level3.netset', TRUE);

-- Notification Type
INSERT INTO notification_type (id, name, endpoint, enabled)
VALUES (NEXTVAL('notification_type_id_seq'), 'vucomm mail', NULL, FALSE),
       (NEXTVAL('notification_type_id_seq'), 'vucomm sms', NULL, FALSE);

-- Parameter   
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXTVAL('parameter_id_seq'), 1, FALSE, 'idCanal', 0, 'idChannel', 1, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'idTipoOperacion', 0, 'idOperationType', 1, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'idUsuario', 3, 'idUser', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'fechaHora', 4, 'date', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'cuentaDebito', 3, 'debitAccount', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'cuentaCredito', 3, 'creditAccount', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'monto', 2, 'amount', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'cantTransacciones', 0, NULL, 2, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'Fingerprint', 3, 'hash', 2, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'hora', 7, NULL, 1, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'Language', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'ColorDepth', 0, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'ScreenHeight', 0, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'ScreenWidth', 0, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'AvailableHeight', 0, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'AvailableWidth', 0, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'TimezoneOffset', 0, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasSessionStorage', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasLocalStorage', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasIndexedDb', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasAddedBehavior', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasOpenDatabase', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'NavigatorCpuClass', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'NavigatorPlatform', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'DoNotTrack', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'Plugins', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasAdBlock', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasLiedLanguages', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasLiedResolution', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'HasLiedOS', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'Fonts', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'CRCCanvas', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'CRCWebGL', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'BrowserMajorVersion', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'BrowserVersion', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'DeviceType', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'DeviceModel', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'DeviceVendor', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'EngineName', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 3, FALSE, 'EngineVersion', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'Calcular red', 8, NULL, 2, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'Score red', 2, NULL, 2, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 1, FALSE, 'kItems perfil', 0, NULL, 2, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'DeviceName', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'DeviceManufacturer', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'DeviceResolution', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'IsFingerprintCompatible', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'IsGPSCompatible', 5, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'OsName', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'OsVersion', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'ApiLevel', 0, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 4, FALSE, 'RandomSeed', 3, '', 3, 0, TRUE),
       (NEXTVAL('parameter_id_seq'), 2, FALSE, 'idCanal', 0, 'idChannel', 1, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 2, FALSE, 'idTipoOperacion', 0, 'idOperationType', 1, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 2, FALSE, 'idUsuario', 3, 'idUser', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 2, FALSE, 'Geolocalizacion', 10, '', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 2, FALSE, 'DeviceKnown', 5, '', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 2, FALSE, 'FingerprintResult', 0, '', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 2, FALSE, 'fechaHora', 4, 'date', 0, NULL, TRUE),
       (NEXTVAL('parameter_id_seq'), 2, FALSE, 'hora', 7, NULL, 1, NULL, TRUE);

INSERT INTO parameter (id, enabled, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id)
VALUES (-1, TRUE, 5, FALSE, 'Audio', 2, '', 3, 0),
       (-2, TRUE, 5, FALSE, 'CookiesEnabled', 5, '', 3, 0),
       (-3, TRUE, 5, FALSE, 'CRCCanvasGeometry', 3, '', 3, 0),
       (-4, TRUE, 5, FALSE, 'CRCCanvasText', 3, '', 3, 0),
       (-5, TRUE, 5, FALSE, 'CRCCanvasWebGL', 3, '', 3, 0),
       (-6, TRUE, 5, FALSE, 'DOMBlocker', 3, '', 3, 0),
       (-7, TRUE, 5, FALSE, 'ExtendedFonts', 3, '', 3, 0),
       (-8, TRUE, 5, FALSE, 'Fonts', 3, '', 3, 0),
       (-9, TRUE, 5, FALSE, 'FontsPreferences', 3, '', 3, 0),
       (-10, TRUE, 5, FALSE, 'GPU', 3, '', 3, 0),
       (-11, TRUE, 5, FALSE, 'Memory', 2, '', 3, 0),
       (-12, TRUE, 5, FALSE, 'Processors', 0, '', 3, 0),
       (-13, TRUE, 5, FALSE, 'SessionStorage', 5, '', 3, 0),
       (-14, TRUE, 5, FALSE, 'LocalStorage', 5, '', 3, 0),
       (-15, TRUE, 5, FALSE, 'IndexedDatabase', 5, '', 3, 0),
       (-16, TRUE, 5, FALSE, 'OpenDatabase', 5, '', 3, 0),
       (-17, TRUE, 5, FALSE, 'Language', 3, '', 3, 0),
       (-18, TRUE, 5, FALSE, 'Maths', 3, '', 3, 0),
       (-19, TRUE, 5, FALSE, 'MediaColorGamut', 3, '', 3, 0),
       (-20, TRUE, 5, FALSE, 'MediaContrast', 0, '', 3, 0),
       (-21, TRUE, 5, FALSE, 'MediaForcedColors', 5, '', 3, 0),
       (-22, TRUE, 5, FALSE, 'MediaHDR', 5, '', 3, 0),
       (-23, TRUE, 5, FALSE, 'MediaInvertedColors', 3, '', 3, 0),
       (-24, TRUE, 5, FALSE, 'MediaMonochrome', 0, '', 3, 0),
       (-25, TRUE, 5, FALSE, 'MediaReducedMotion', 5, '', 3, 0),
       (-26, TRUE, 5, FALSE, 'Platform', 3, '', 3, 0),
       (-27, TRUE, 5, FALSE, 'ColorDepth', 0, '', 3, 0),
       (-28, TRUE, 5, FALSE, 'Vendor', 3, '', 3, 0),
       (-29, TRUE, 5, FALSE, 'VendorFlavors', 3, '', 3, 0),
       (-30, TRUE, 5, FALSE, 'Timezone', 3, '', 3, 0),
       (-31, TRUE, 5, FALSE, 'TouchSupport', 3, '', 3, 0),
       (-32, TRUE, 5, FALSE, 'TLS', 3, '', 3, 0),
       (-33, TRUE, 5, FALSE, 'HEADER', 3, '', 3, 0),
       (-34, TRUE, 5, FALSE, 'HTTP2', 3, '', 3, 0),
       (-35, TRUE, 5, FALSE, 'TLSSSLGreased', 3, '', 3, 0),
       (-36, TRUE, 5, FALSE, 'BrowserMajor', 0, '', 3, 0),
       (-37, TRUE, 5, FALSE, 'BrowserVersion', 6, '', 3, 0),
       (-38, TRUE, 5, FALSE, 'DeviceType', 3, '', 3, 0),
       (-39, TRUE, 5, FALSE, 'DeviceModel', 3, '', 3, 0),
       (-40, TRUE, 5, FALSE, 'DeviceVendor', 3, '', 3, 0),
       (-41, TRUE, 5, FALSE, 'EngineName', 3, '', 3, 0),
       (-42, TRUE, 5, FALSE, 'EngineVersion', 6, '', 3, 0),
       (-43, TRUE, 5, FALSE, 'OSVersion', 6, '', 3, 0),
       (-44, TRUE, 5, FALSE, 'LocalstorageId', 3, '', 3, 0),
       (-45, TRUE, 5, FALSE, 'Cookie', 3, '', 3, 0),
       (-46, TRUE, 5, FALSE, 'Incognito', 5, '', 3, 0),
       (-47, TRUE, 5, FALSE, 'IP', 3, '', 3, 0);
INSERT INTO parameter (id, column_name, enabled, event_type_id, name, parameter_info_id, parameter_type_id, user_generated, visibility)
VALUES (-48, '', TRUE, 3, 'RandomSeed', 0, 3, FALSE, 3);
INSERT INTO parameter (id, column_name, enabled, event_type_id, name, parameter_info_id, parameter_type_id, user_generated, visibility)
VALUES (-49, '', TRUE, 3, 'OSName', 0, 3, FALSE, 3);
INSERT INTO parameter (id, column_name, enabled, event_type_id, name, parameter_info_id, parameter_type_id, user_generated, visibility)
VALUES (-50, '', TRUE, 3, 'OSVersion', 0, 3, FALSE, 3);


-- System Parameter
INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES 
       (NEXTVAL('system_parameter_id_seq'), 'API_MULTITENANT_API_KEY', ''),
       (NEXTVAL('system_parameter_id_seq'), 'API_MULTITENANT_TRANSIT_KEY', ''),
       (NEXTVAL('system_parameter_id_seq'), 'API_MULTITENANT_URL', ''),
       (NEXTVAL('system_parameter_id_seq'), 'BULK_TRANSACTION_LOAD_BATCH_ENABLED', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'BULK_TRANSACTION_LOAD_BATCH_ENABLED_RULE_EXECUTION', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'BULK_TRANSACTION_LOAD_BATCH_HOUR', '12'),
       (NEXTVAL('system_parameter_id_seq'), 'BULK_TRANSACTION_LOAD_BATCH_MINUTES', '45'),
       (NEXTVAL('system_parameter_id_seq'), 'CEP_ENABLED', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'CEP_RULE_ENGINE', ''),
       (NEXTVAL('system_parameter_id_seq'), 'CLASSIFIERS_ENABLED', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'CLEAN_FPS_HOUR', '3'),
       (NEXTVAL('system_parameter_id_seq'), 'CLEAN_FPS_MINUTES', '30'),
       (NEXTVAL('system_parameter_id_seq'), 'CLEAN_INVALID_FINGERPRINTS', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'CLEAN_OLD_FINGERPRINTS_ACTIVATE', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'CLEAN_OLD_FINGERPRINTS_DAY_OF_WEEK', '7'),
       (NEXTVAL('system_parameter_id_seq'), 'CLEAN_OLD_FINGERPRINTS_PARAM_DELETE', 0),
       (NEXTVAL('system_parameter_id_seq'), 'CLEAN_UNVERIFIED_FINGERPRINTS', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'CLEAN_VERIFIED_FINGERPRINTS', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'DISABLE_CLASSIC_AUTH', FALSE),
       (NEXTVAL('system_parameter_id_seq'), 'ENABLED_FINGERPRINT_BATCH', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'FINGERPRINT_AUTOMATIC_CLEANUP_DAYS', ''),
       (NEXTVAL('system_parameter_id_seq'), 'FINGERPRINT_AUTOMATIC_CLEANUP_ENABLED', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'FINGERPRINT_BROWSER_MAX_VERIFIED_THRESHOLD', 10),
       (NEXTVAL('system_parameter_id_seq'), 'FINGERPRINT_COLLECTION_THRESHOLD', 85),
       (NEXTVAL('system_parameter_id_seq'), 'FINGERPRINT_DEVICE_MAX_VERIFIED_THRESHOLD', 10),
       (NEXTVAL('system_parameter_id_seq'), 'FISCAL_YEAR_ENABLED', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'FISCAL_YEAR_END', '2024-01-01'),
       (NEXTVAL('system_parameter_id_seq'), 'FISCAL_YEAR_START', '2023-01-01'),
       (NEXTVAL('system_parameter_id_seq'), 'FP_TOKEN_VALID_TIME', 8),
       (NEXTVAL('system_parameter_id_seq'), 'FP_USE_TOKEN', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'FP_VALID_IF_USER_NOT_EXISTS', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'FRAUD_URL', ''),
       (NEXTVAL('system_parameter_id_seq'), 'GEO_ATM_PARAMETER_NAME', 'Codigo de atm'),
       (NEXTVAL('system_parameter_id_seq'), 'GEO_SERVICE_APIKEY', 'bbd6edb96ca202683e40f17f18f4abc73a98db96e0b1090f6929877728336c42'),
       (NEXTVAL('system_parameter_id_seq'), 'GEO_SERVICE_NAME', 'GOOGLE'),
       (NEXTVAL('system_parameter_id_seq'), 'IP_SYNC_ENABLED', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'IP_SYNC_HOUR', '10'),
       (NEXTVAL('system_parameter_id_seq'), 'IP_SYNC_MINUTES', '15'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_AD_DOMAIN', ''),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_AUTHENTICATION', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_BASE_DN', 'DC=domain,DC=com'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_BASE_USER_DN', ''),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_MULTIPLE_BASE_DN', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_OBJECT_CATEGORY', 'person'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_OBJECT_CLASS', 'user'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_PASS_USER', ''),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_PORT', '389'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_SERVER', 'ldap://domain.com'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_SSL', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_USERNAME_ATTRIBUTE', 'sAMAccountname'),
       (NEXTVAL('system_parameter_id_seq'), 'LDAP_USER_DN', 'fAdmin'),
       (NEXTVAL('system_parameter_id_seq'), 'LISTS_LOAD_BATCH_ENABLED', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'LISTS_LOAD_BATCH_HOUR', '12'),
       (NEXTVAL('system_parameter_id_seq'), 'LISTS_LOAD_BATCH_IMPORT_TYPE', '1'),
       (NEXTVAL('system_parameter_id_seq'), 'LISTS_LOAD_BATCH_MINUTES', '45'),
       (NEXTVAL('system_parameter_id_seq'), 'MULTITENANT_MODE', FALSE),
       (NEXTVAL('system_parameter_id_seq'), 'NOTIFICATIONS_MESSAGE_LANGUAGE', 'en'),
       (NEXTVAL('system_parameter_id_seq'), 'OLD_FINGERPRINT_DAYS', '90'),
       (NEXTVAL('system_parameter_id_seq'), 'REFRESH_VIEW_ENABLED', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'REFRESH_VIEW_HOUR', '3'),
       (NEXTVAL('system_parameter_id_seq'), 'REFRESH_VIEW_MINUTES', '30'),
       (NEXTVAL('system_parameter_id_seq'), 'REPORT_MAX_TRANSACTIONS', '500000'),
       (NEXTVAL('system_parameter_id_seq'), 'SAVE_GEO_IP_DATA', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'serverurl', ''),
       (NEXTVAL('system_parameter_id_seq'), 'SMTP_FROM', ''),
       (NEXTVAL('system_parameter_id_seq'), 'SMTP_HOST', ''),
       (NEXTVAL('system_parameter_id_seq'), 'SMTP_PASSWORD', ''),
       (NEXTVAL('system_parameter_id_seq'), 'SMTP_PORT', '587'),
       (NEXTVAL('system_parameter_id_seq'), 'SMTP_PROTOCOL', ''),
       (NEXTVAL('system_parameter_id_seq'), 'SMTP_USE_TLS', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'SMTP_USERNAME', ''),
       (NEXTVAL('system_parameter_id_seq'), 'SPECIALTY_PARAMETER_NAME', 'idSpecialty'),
       (NEXTVAL('system_parameter_id_seq'), 'UPLOAD_ATTACHMENTS_LOCATION', '/opt/tomcat/attachments'),
       (NEXTVAL('system_parameter_id_seq'), 'USE_CASEMANAGEMENT_FILE_MASK', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'USE_CASEMANAGEMENT_MASK', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'USER_TRANSACTION_SUMMARY_DAYS', 0),
       (NEXTVAL('system_parameter_id_seq'), 'USER_TRANSACTION_SUMMARY_DAYOFMONTH', 0),
       (NEXTVAL('system_parameter_id_seq'), 'USER_TRANSACTION_SUMMARY_ENABLED_BATCH', FALSE),
       (NEXTVAL('system_parameter_id_seq'), 'USER_TRANSACTION_SUMMARY_HOUR', 0),
       (NEXTVAL('system_parameter_id_seq'), 'USER_TRANSACTION_SUMMARY_LATEST_PROCESS', ''),
       (NEXTVAL('system_parameter_id_seq'), 'USER_TRANSACTION_SUMMARY_MINUTES', 0),
       (NEXTVAL('system_parameter_id_seq'), 'USER_TRANSACTION_SUMMARY_MONTH', 0),
       (NEXTVAL('system_parameter_id_seq'), 'USER_TRANSACTION_SUMMARY_RUNNING', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'VALIDATE_OPERATION_TYPE', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'JWT_SIGNING_KEY', 'MTIzNDU2Nzg5MGFiY2RlZjEyMzQ1Njc4OTBhYmNkZWY='),
       (NEXTVAL('system_parameter_id_seq'), 'JWT_EXPIRING_IN_SECONDS', '3600'),
       (NEXTVAL('system_parameter_id_seq'), 'BULK_TRANSACTION_LOAD_BATCH_PATH', ''),
       (NEXTVAL('system_parameter_id_seq'), 'LICENSE', '31XL9C-H2YZV8-6T6YO9-PE1'),
       (NEXTVAL('system_parameter_id_seq'), 'USE_V5_RETURN_CODES', 'true'),
       (NEXTVAL('system_parameter_id_seq'), 'USE_SMTP', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'ENABLED_TWO_FA', 'false'),
       (NEXTVAL('system_parameter_id_seq'), 'TWO_FA_EXPIRING_IN_SECONDS', '60');

-- Country
INSERT INTO country (id, name)
VALUES (NEXTVAL('country_id_seq'), 'afghanistan'),
       (NEXTVAL('country_id_seq'), 'albania'),
       (NEXTVAL('country_id_seq'), 'algeria'),
       (NEXTVAL('country_id_seq'), 'american-samoa'),
       (NEXTVAL('country_id_seq'), 'andorra'),
       (NEXTVAL('country_id_seq'), 'angola'),
       (NEXTVAL('country_id_seq'), 'anguilla'),
       (NEXTVAL('country_id_seq'), 'antigua-and-barbuda'),
       (NEXTVAL('country_id_seq'), 'argentina'),
       (NEXTVAL('country_id_seq'), 'armenia'),
       (NEXTVAL('country_id_seq'), 'aruba'),
       (NEXTVAL('country_id_seq'), 'australia'),
       (NEXTVAL('country_id_seq'), 'austria'),
       (NEXTVAL('country_id_seq'), 'azerbaijan'),
       (NEXTVAL('country_id_seq'), 'bahamas'),
       (NEXTVAL('country_id_seq'), 'bahrain'),
       (NEXTVAL('country_id_seq'), 'bangladesh'),
       (NEXTVAL('country_id_seq'), 'barbados'),
       (NEXTVAL('country_id_seq'), 'belarus'),
       (NEXTVAL('country_id_seq'), 'belgium'),
       (NEXTVAL('country_id_seq'), 'belize'),
       (NEXTVAL('country_id_seq'), 'benin'),
       (NEXTVAL('country_id_seq'), 'bermuda'),
       (NEXTVAL('country_id_seq'), 'bhutan'),
       (NEXTVAL('country_id_seq'), 'bolivia'),
       (NEXTVAL('country_id_seq'), 'bonaire'),
       (NEXTVAL('country_id_seq'), 'bosnia-and-herzegovina'),
       (NEXTVAL('country_id_seq'), 'botswana'),
       (NEXTVAL('country_id_seq'), 'brazil'),
       (NEXTVAL('country_id_seq'), 'brunei'),
       (NEXTVAL('country_id_seq'), 'bulgaria'),
       (NEXTVAL('country_id_seq'), 'burkina-faso'),
       (NEXTVAL('country_id_seq'), 'burundi'),
       (NEXTVAL('country_id_seq'), 'cambodia'),
       (NEXTVAL('country_id_seq'), 'cameroon'),
       (NEXTVAL('country_id_seq'), 'canada'),
       (NEXTVAL('country_id_seq'), 'cape-verde'),
       (NEXTVAL('country_id_seq'), 'cayman-islands'),
       (NEXTVAL('country_id_seq'), 'central-african-republic'),
       (NEXTVAL('country_id_seq'), 'chad'),
       (NEXTVAL('country_id_seq'), 'chile'),
       (NEXTVAL('country_id_seq'), 'china'),
       (NEXTVAL('country_id_seq'), 'colombia'),
       (NEXTVAL('country_id_seq'), 'comoros'),
       (NEXTVAL('country_id_seq'), 'congo'),
       (NEXTVAL('country_id_seq'), 'congo-democratic-republic'),
       (NEXTVAL('country_id_seq'), 'cook-islands'),
       (NEXTVAL('country_id_seq'), 'costa-rica'),
       (NEXTVAL('country_id_seq'), 'cote-divoire'),
       (NEXTVAL('country_id_seq'), 'croatia'),
       (NEXTVAL('country_id_seq'), 'cuba'),
       (NEXTVAL('country_id_seq'), 'curacao'),
       (NEXTVAL('country_id_seq'), 'cyprus'),
       (NEXTVAL('country_id_seq'), 'czech-republic'),
       (NEXTVAL('country_id_seq'), 'denmark'),
       (NEXTVAL('country_id_seq'), 'djibouti'),
       (NEXTVAL('country_id_seq'), 'dominica'),
       (NEXTVAL('country_id_seq'), 'dominican-republic'),
       (NEXTVAL('country_id_seq'), 'ecuador'),
       (NEXTVAL('country_id_seq'), 'egypt'),
       (NEXTVAL('country_id_seq'), 'el-salvador'),
       (NEXTVAL('country_id_seq'), 'equatorial-guinea'),
       (NEXTVAL('country_id_seq'), 'eritrea'),
       (NEXTVAL('country_id_seq'), 'estonia'),
       (NEXTVAL('country_id_seq'), 'ethiopia'),
       (NEXTVAL('country_id_seq'), 'falkland-islands'),
       (NEXTVAL('country_id_seq'), 'faroe-islands'),
       (NEXTVAL('country_id_seq'), 'fiji'),
       (NEXTVAL('country_id_seq'), 'finland'),
       (NEXTVAL('country_id_seq'), 'france'),
       (NEXTVAL('country_id_seq'), 'french-guiana'),
       (NEXTVAL('country_id_seq'), 'french-polynesia'),
       (NEXTVAL('country_id_seq'), 'gabon'),
       (NEXTVAL('country_id_seq'), 'gambia'),
       (NEXTVAL('country_id_seq'), 'georgia'),
       (NEXTVAL('country_id_seq'), 'germany'),
       (NEXTVAL('country_id_seq'), 'ghana'),
       (NEXTVAL('country_id_seq'), 'gibraltar'),
       (NEXTVAL('country_id_seq'), 'greece'),
       (NEXTVAL('country_id_seq'), 'greenland'),
       (NEXTVAL('country_id_seq'), 'grenada'),
       (NEXTVAL('country_id_seq'), 'guadeloupe'),
       (NEXTVAL('country_id_seq'), 'guam'),
       (NEXTVAL('country_id_seq'), 'guatemala'),
       (NEXTVAL('country_id_seq'), 'guernsey'),
       (NEXTVAL('country_id_seq'), 'guinea'),
       (NEXTVAL('country_id_seq'), 'guinea-bissau'),
       (NEXTVAL('country_id_seq'), 'guyana'),
       (NEXTVAL('country_id_seq'), 'haiti'),
       (NEXTVAL('country_id_seq'), 'honduras'),
       (NEXTVAL('country_id_seq'), 'hong-kong'),
       (NEXTVAL('country_id_seq'), 'hungary'),
       (NEXTVAL('country_id_seq'), 'iceland'),
       (NEXTVAL('country_id_seq'), 'india'),
       (NEXTVAL('country_id_seq'), 'indonesia'),
       (NEXTVAL('country_id_seq'), 'iran'),
       (NEXTVAL('country_id_seq'), 'iraq'),
       (NEXTVAL('country_id_seq'), 'ireland'),
       (NEXTVAL('country_id_seq'), 'isle-of-man'),
       (NEXTVAL('country_id_seq'), 'israel'),
       (NEXTVAL('country_id_seq'), 'italy'),
       (NEXTVAL('country_id_seq'), 'jamaica'),
       (NEXTVAL('country_id_seq'), 'japan'),
       (NEXTVAL('country_id_seq'), 'jersey'),
       (NEXTVAL('country_id_seq'), 'jordan'),
       (NEXTVAL('country_id_seq'), 'kazakhstan'),
       (NEXTVAL('country_id_seq'), 'kenya'),
       (NEXTVAL('country_id_seq'), 'kiribati'),
       (NEXTVAL('country_id_seq'), 'kosovo'),
       (NEXTVAL('country_id_seq'), 'kuwait'),
       (NEXTVAL('country_id_seq'), 'kyrgyzstan'),
       (NEXTVAL('country_id_seq'), 'laos'),
       (NEXTVAL('country_id_seq'), 'latvia'),
       (NEXTVAL('country_id_seq'), 'lebanon'),
       (NEXTVAL('country_id_seq'), 'lesotho'),
       (NEXTVAL('country_id_seq'), 'liberia'),
       (NEXTVAL('country_id_seq'), 'libya'),
       (NEXTVAL('country_id_seq'), 'liechtenstein'),
       (NEXTVAL('country_id_seq'), 'lithuania'),
       (NEXTVAL('country_id_seq'), 'luxembourg'),
       (NEXTVAL('country_id_seq'), 'macao'),
       (NEXTVAL('country_id_seq'), 'macedonia'),
       (NEXTVAL('country_id_seq'), 'madagascar'),
       (NEXTVAL('country_id_seq'), 'malawi'),
       (NEXTVAL('country_id_seq'), 'malaysia'),
       (NEXTVAL('country_id_seq'), 'maldives'),
       (NEXTVAL('country_id_seq'), 'mali'),
       (NEXTVAL('country_id_seq'), 'malta'),
       (NEXTVAL('country_id_seq'), 'marshall-islands'),
       (NEXTVAL('country_id_seq'), 'martinique'),
       (NEXTVAL('country_id_seq'), 'mauritania'),
       (NEXTVAL('country_id_seq'), 'mauritius'),
       (NEXTVAL('country_id_seq'), 'mayotte'),
       (NEXTVAL('country_id_seq'), 'mexico'),
       (NEXTVAL('country_id_seq'), 'moldova'),
       (NEXTVAL('country_id_seq'), 'monaco'),
       (NEXTVAL('country_id_seq'), 'mongolia'),
       (NEXTVAL('country_id_seq'), 'montenegro'),
       (NEXTVAL('country_id_seq'), 'montserrat'),
       (NEXTVAL('country_id_seq'), 'morocco'),
       (NEXTVAL('country_id_seq'), 'mozambique'),
       (NEXTVAL('country_id_seq'), 'myanmar'),
       (NEXTVAL('country_id_seq'), 'namibia'),
       (NEXTVAL('country_id_seq'), 'nepal'),
       (NEXTVAL('country_id_seq'), 'netherlands'),
       (NEXTVAL('country_id_seq'), 'new-caledonia'),
       (NEXTVAL('country_id_seq'), 'new-zealand'),
       (NEXTVAL('country_id_seq'), 'nicaragua'),
       (NEXTVAL('country_id_seq'), 'niger'),
       (NEXTVAL('country_id_seq'), 'nigeria'),
       (NEXTVAL('country_id_seq'), 'north-korea'),
       (NEXTVAL('country_id_seq'), 'norway'),
       (NEXTVAL('country_id_seq'), 'oman'),
       (NEXTVAL('country_id_seq'), 'pakistan'),
       (NEXTVAL('country_id_seq'), 'palestine'),
       (NEXTVAL('country_id_seq'), 'panama'),
       (NEXTVAL('country_id_seq'), 'papua-new-guinea'),
       (NEXTVAL('country_id_seq'), 'paraguay'),
       (NEXTVAL('country_id_seq'), 'peru'),
       (NEXTVAL('country_id_seq'), 'philippines'),
       (NEXTVAL('country_id_seq'), 'poland'),
       (NEXTVAL('country_id_seq'), 'portugal'),
       (NEXTVAL('country_id_seq'), 'puerto-rico'),
       (NEXTVAL('country_id_seq'), 'qatar'),
       (NEXTVAL('country_id_seq'), 'reunion'),
       (NEXTVAL('country_id_seq'), 'romania'),
       (NEXTVAL('country_id_seq'), 'russia'),
       (NEXTVAL('country_id_seq'), 'rwanda'),
       (NEXTVAL('country_id_seq'), 'saint-helena'),
       (NEXTVAL('country_id_seq'), 'saint-kitts-and-nevis'),
       (NEXTVAL('country_id_seq'), 'saint-lucia'),
       (NEXTVAL('country_id_seq'), 'saint-pierre-and-miquelon'),
       (NEXTVAL('country_id_seq'), 'samoa'),
       (NEXTVAL('country_id_seq'), 'san-marino'),
       (NEXTVAL('country_id_seq'), 'sao-tome-and-principe'),
       (NEXTVAL('country_id_seq'), 'saudi-arabia'),
       (NEXTVAL('country_id_seq'), 'senegal'),
       (NEXTVAL('country_id_seq'), 'serbia'),
       (NEXTVAL('country_id_seq'), 'seychelles'),
       (NEXTVAL('country_id_seq'), 'sierra-leone'),
       (NEXTVAL('country_id_seq'), 'singapore'),
       (NEXTVAL('country_id_seq'), 'sint-maarten'),
       (NEXTVAL('country_id_seq'), 'slovakia'),
       (NEXTVAL('country_id_seq'), 'slovenia'),
       (NEXTVAL('country_id_seq'), 'solomon-islands'),
       (NEXTVAL('country_id_seq'), 'somalia'),
       (NEXTVAL('country_id_seq'), 'south-africa'),
       (NEXTVAL('country_id_seq'), 'south-korea'),
       (NEXTVAL('country_id_seq'), 'south-sudan'),
       (NEXTVAL('country_id_seq'), 'spain'),
       (NEXTVAL('country_id_seq'), 'sri-lanka'),
       (NEXTVAL('country_id_seq'), 'st-vincent-and-grenadines'),
       (NEXTVAL('country_id_seq'), 'sudan'),
       (NEXTVAL('country_id_seq'), 'suriname'),
       (NEXTVAL('country_id_seq'), 'swaziland'),
       (NEXTVAL('country_id_seq'), 'sweden'),
       (NEXTVAL('country_id_seq'), 'switzerland'),
       (NEXTVAL('country_id_seq'), 'syria'),
       (NEXTVAL('country_id_seq'), 'taiwan'),
       (NEXTVAL('country_id_seq'), 'tajikistan'),
       (NEXTVAL('country_id_seq'), 'tanzania'),
       (NEXTVAL('country_id_seq'), 'thailand'),
       (NEXTVAL('country_id_seq'), 'timor-leste'),
       (NEXTVAL('country_id_seq'), 'togo'),
       (NEXTVAL('country_id_seq'), 'tonga'),
       (NEXTVAL('country_id_seq'), 'trinidad-and-tobago'),
       (NEXTVAL('country_id_seq'), 'tunisia'),
       (NEXTVAL('country_id_seq'), 'turkey'),
       (NEXTVAL('country_id_seq'), 'turkmenistan'),
       (NEXTVAL('country_id_seq'), 'turks-and-caicos-islands'),
       (NEXTVAL('country_id_seq'), 'tuvalu'),
       (NEXTVAL('country_id_seq'), 'uganda'),
       (NEXTVAL('country_id_seq'), 'ukraine'),
       (NEXTVAL('country_id_seq'), 'united-arab-emirates'),
       (NEXTVAL('country_id_seq'), 'united-kingdom'),
       (NEXTVAL('country_id_seq'), 'united-states'),
       (NEXTVAL('country_id_seq'), 'uruguay'),
       (NEXTVAL('country_id_seq'), 'uzbekistan'),
       (NEXTVAL('country_id_seq'), 'vanuatu'),
       (NEXTVAL('country_id_seq'), 'vatican-city'),
       (NEXTVAL('country_id_seq'), 'venezuela'),
       (NEXTVAL('country_id_seq'), 'vietnam'),
       (NEXTVAL('country_id_seq'), 'virgin-islands-uk'),
       (NEXTVAL('country_id_seq'), 'virgin-islands-us'),
       (NEXTVAL('country_id_seq'), 'wallis-and-futuna-islands'),
       (NEXTVAL('country_id_seq'), 'yemen'),
       (NEXTVAL('country_id_seq'), 'zambia'),
       (NEXTVAL('country_id_seq'), 'zimbabwe');

-- Auth Privilege
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXTVAL('auth_privilege_id_seq'), 'ADMINISTRATION', NULL, 'menu.titleAdministration'),
       (NEXTVAL('auth_privilege_id_seq'), 'DATA_CONFIG', 1, 'channel.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'CHANNEL_MANAGEMENT', 2, 'channel.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_CHANNEL', 3, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_CHANNEL', 3, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_CHANNEL', 3, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_CHANNEL', 3, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'OPERATION_TYPE_MANAGEMENT', 2, 'operations.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_OPERATION_TYPE', 8, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_OPERATION_TYPE', 8, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_OPERATION_TYPE', 8, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_OPERATION_TYPE', 8, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'PARAMETER_TYPE_MANAGEMENT', 2, 'wsParameters.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_PARAMETER_TYPE', 13, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_PARAMETER_TYPE', 13, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_PARAMETER_TYPE', 13, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_PARAMETER_TYPE', 13, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'LISTS', 1, 'customLists.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'LIST_MANAGEMENT', 18, 'customLists.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_LIST', 19, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_LIST', 19, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_LIST', 19, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_LIST', 19, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'LIST_MASIVE_LOAD', 18, 'customListUpload.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'PROCESS_LIST', 24, 'general.button.process'),
       (NEXTVAL('auth_privilege_id_seq'), 'LIST_ATM_LOAD', 18, 'atmListUpload.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ATM_ADD_LIST', 26, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'ATM_PROCESS_LIST', 26, 'general.button.process'),
       (NEXTVAL('auth_privilege_id_seq'), 'PARAMETER_BY_BUSINESS', 18, 'parametersByBusiness.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'PARAMETER_BY_BUSINESS_SAVE', 29, 'general.save'),
       (NEXTVAL('auth_privilege_id_seq'), 'SYSTEM_PARAMETERS_CONFIG', 1, 'systemAdmin.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'USER_ROLES', 1, 'admin.roleTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_USER', 32, 'account.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ROLES_MANAGEMENT', 32, 'admin.roles'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_ROLE', 34, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_ROLE', 34, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_ROLE', 34, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_ROLE', 34, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'USER_MANAGEMENT', 32, 'admin.roleSubTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_USERS', 39, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_USER', 39, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_USER', 39, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'APIKEY_MANAGEMENT', 1, 'apikey.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'NEW_APIKEY', 43, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_APIKEY', 43, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'FRAUD', NULL, 'menu.title.fraud'),
       (NEXTVAL('auth_privilege_id_seq'), 'RULES_MANAGEMENT', 46, 'fraudRule.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'COMMON_RULES_MANAGEMENT', 47, 'fraudRule.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_COMMON_RULE', 48, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_COMMON_RULE', 48, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_COMMON_RULE', 48, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_COMMON_RULE', 48, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'RISK_RULES_MANAGEMENT', 47, 'fraudRule.subTitleRisk'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_RISK_RULE', 53, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_RISK_RULE', 53, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_RISK_RULE', 53, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_RISK_RULE', 53, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'CEP_RULES_MANAGEMENT', 47, 'fraudRule.subTitleCep'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_CEP_RULE', 58, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_CEP_RULE', 58, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_CEP_RULE', 58, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_CEP_RULE', 58, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'CROSS_RULE_MANAGEMENT', 47, 'fraudRule.subTitleCross'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_CROSS_RULE', 63, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_CROSS_RULE', 63, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_CROSS_RULE', 63, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_CROSS_RULE', 63, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'HISTORY', 46, 'batchLoad.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'MASSIVE_TRANSACTION_LOAD', 68, 'batchLoad.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'MASSIVE_EVENT_LOAD', 68, 'batchEventLoad.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'CASE_MANAGEMENT', 46, 'caseManagement.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_CASE_MANAGEMENT', 71, 'caseManagement.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_ALL_CASES', 71, 'viewAllCases.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_PROCESSED_CASE', 71, 'processedCases.editStatus'),
       (NEXTVAL('auth_privilege_id_seq'), 'CUSTOMIZE_CASE_MANAGEMENT', 71, 'caseManagement.customizeColumns'),
       (NEXTVAL('auth_privilege_id_seq'), 'DOWNLOAD_TABLE_CASE_MANAGEMENT', 71, 'general.download'),
       (NEXTVAL('auth_privilege_id_seq'), 'FINGERPRINT', NULL, 'menu.title.fingerprint'),
       (NEXTVAL('auth_privilege_id_seq'), 'FINGERPRINT_RULES', 77, 'fingerprintDeviceRules.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADMIN_BROWSER_FP', 78, 'fingerprintRules.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_RULE_BROWSER_FP', 79, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_RULE_BROWSER_FP', 79, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_RULE_BROWSER_FP', 79, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADMIN_DEVICE_FP', 78, 'fingerprintDeviceRules.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_RULE_DEVICE_FP', 83, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_RULE_DEVICE_FP', 83, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_RULE_DEVICE_FP', 83, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'FINGERPRINT_QUERY', 77, 'fingerprintQuery.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'DASHBOARD_FP', 87, 'fingerprintQuery.subTitle1'),
       (NEXTVAL('auth_privilege_id_seq'), 'QUERY_FP', 87, 'fingerprintQuery.subTitle2'),
       (NEXTVAL('auth_privilege_id_seq'), 'REPORTS', NULL, 'menu.titleReport'),
       (NEXTVAL('auth_privilege_id_seq'), 'STATIC_REPORTS', 90, 'staticReports.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'USERS_AND_PROFILES', 90, 'profile.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'PROFILES', 92, 'profile.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'USER_COUNT', 92, 'dashboard.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'DYNAMIC_REPORTS', 90, 'reports.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_DYNAMIC_REPORTS', 95, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_DYNAMIC_REPORTS', 95, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_DYNAMIC_REPORTS', 95, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'RENDER_DYNAMIC_REPORTS', 95, 'general.button.render'),
       (NEXTVAL('auth_privilege_id_seq'), 'NOTIFICATIONS', NULL, 'menu.titleNotification'),
       (NEXTVAL('auth_privilege_id_seq'), 'NOTIFICATION_TYPES', 100, 'notificationType.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'BUSINESS_GROUP', 100, 'businessGroup.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'BUSINESS_GROUP_USERS', 102, 'menu.subTitleBusinessGroup'),
       (NEXTVAL('auth_privilege_id_seq'), 'BUSINESS_GROUP_CONFIG', 102, 'menu.subTitleNotificationsConfig'),
       (NEXTVAL('auth_privilege_id_seq'), 'NOTIFICATION_RULES', 100, 'notificationRules.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'BUSINESS_GROUP_AUTO_ASSIGN', 100, 'autoAssignBusiness.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'MACHINE_LEARNING', NULL, 'menu.title.machineLearning'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_TRANSACTIONS', 107, 'machineLearning.transaction.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_COPY_DELETE', 108, 'machineLearning.copyAndDelete.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_FEATURES', 108, 'machineLearning.Feature.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_NEURAL_NETWORK', 107, 'machineLearning.networks.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_MANAGEMENT', 111, 'machineLearning.networks.manage.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_PROFILES', 111, 'machineLearning.networks.profiles.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_BATCH_PROCESS', 111, 'machineLearning.networks.batchs.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_EXPERIMENT', 107, 'machineLearning.experiments.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_NEW_EXPERIMENT', 115, 'machineLearning.experiments.new.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'ML_EXPERIMENT_RESULT', 115, 'machineLearning.experiments.results.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'TRACEABILITY', NULL, 'menu.title.traceability'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_TRACE', 118, 'machineLearning.traceability.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'FISCAL_YEAR_PARAMETERS', 1, 'fiscalYear.title'),
       (NEXTVAL('auth_privilege_id_seq'), 'CLASSIFIERS', 46, 'fraudRule.titleClassifiers'),
       (NEXTVAL('auth_privilege_id_seq'), 'CLASSIFIERS_MANAGEMENT', 121, 'fraudRule.subTitleClassifiers'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_CLASSIFIERS_RULE', 122, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_CLASSIFIERS_RULE', 122, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_CLASSIFIERS_RULE', 122, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_CLASSIFIERS_RULE', 122, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'ACCUMULATORS', 46, 'fraudRule.titleAccumulators'),
       (NEXTVAL('auth_privilege_id_seq'), 'ACCUMULATORS_MANAGEMENT', 127, 'fraudRule.subTitleAccumulators'),
       (NEXTVAL('auth_privilege_id_seq'), 'ADD_ACCUMULATORS_RULE', 128, 'general.button.new'),
       (NEXTVAL('auth_privilege_id_seq'), 'VIEW_ACCUMULATORS_RULE', 128, 'general.button.view'),
       (NEXTVAL('auth_privilege_id_seq'), 'EDIT_ACCUMULATORS_RULE', 128, 'general.button.edit'),
       (NEXTVAL('auth_privilege_id_seq'), 'DELETE_ACCUMULATORS_RULE', 128, 'general.button.delete'),
       (NEXTVAL('auth_privilege_id_seq'), 'ACTIVE_DIRECTORY', NULL, 'menu.title.ldap'),
       (NEXTVAL('auth_privilege_id_seq'), 'ACTIVE_DIRECTORY_CONFIG', 133, 'ldap.connection.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ACTIVE_DIRECTORY_GROUPS', 133, 'ldap.grupo.subTitle'),
       (NEXTVAL('auth_privilege_id_seq'), 'ASSIGN_CASES', 71, 'assignCases.editStatus'),
       (NEXTVAL('auth_privilege_id_seq'), 'REJECT_PROCESSED_CASE', 71, 'processedCases.rejectedStatus'),
       (NEXTVAL('auth_privilege_id_seq'), 'CHANGE_PROCESSED_CASE', 71, 'processedCases.changedStatus');

-- Auth Role Privilege
INSERT INTO auth_role_privilege (role_id, privilege_id)
VALUES (5, 46),
       (5, 47),
       (5, 63),
       (5, 65),
       (5, 66),
       (5, 67),
       (5, 68),
       (5, 69),
       (5, 70),
       (5, 71),
       (5, 72),
       (5, 73),
       (1, 1),
       (1, 2),
       (1, 3),
       (1, 4),
       (1, 5),
       (1, 6),
       (1, 7),
       (1, 8),
       (1, 9),
       (1, 10),
       (1, 11),
       (1, 12),
       (1, 13),
       (1, 14),
       (1, 15),
       (1, 16),
       (1, 17),
       (1, 18),
       (1, 19),
       (1, 20),
       (1, 21),
       (1, 22),
       (1, 23),
       (1, 24),
       (1, 25),
       (1, 26),
       (1, 27),
       (1, 28),
       (1, 29),
       (1, 30),
       (1, 31),
       (1, 32),
       (1, 33),
       (1, 34),
       (1, 35),
       (1, 36),
       (1, 37),
       (1, 38),
       (1, 39),
       (1, 40),
       (1, 41),
       (1, 42),
       (1, 43),
       (1, 44),
       (1, 45),
       (1, 46),
       (1, 47),
       (1, 48),
       (1, 49),
       (1, 50),
       (1, 51),
       (1, 52),
       (1, 53),
       (1, 54),
       (1, 55),
       (1, 56),
       (1, 57),
       (1, 58),
       (1, 59),
       (1, 60),
       (1, 61),
       (1, 62),
       (1, 63),
       (1, 64),
       (1, 65),
       (1, 66),
       (1, 67),
       (1, 68),
       (1, 69),
       (1, 70),
       (1, 71),
       (1, 72),
       (1, 73),
       (1, 74),
       (1, 75),
       (1, 76),
       (1, 77),
       (1, 78),
       (1, 79),
       (1, 80),
       (1, 81),
       (1, 82),
       (1, 83),
       (1, 84),
       (1, 85),
       (1, 86),
       (1, 87),
       (1, 88),
       (1, 89),
       (1, 90),
       (1, 91),
       (1, 92),
       (1, 93),
       (1, 94),
       (1, 95),
       (1, 96),
       (1, 97),
       (1, 98),
       (1, 99),
       (1, 100),
       (1, 101),
       (1, 102),
       (1, 103),
       (1, 104),
       (1, 105),
       (1, 106),
       (1, 107),
       (1, 108),
       (1, 109),
       (1, 110),
       (1, 111),
       (1, 112),
       (1, 113),
       (1, 114),
       (1, 115),
       (1, 116),
       (1, 117),
       (1, 118),
       (1, 119),
       (1, 120),
       (1, 121),
       (1, 122),
       (1, 123),
       (1, 124),
       (1, 125),
       (1, 126),
       (1, 127),
       (1, 128),
       (1, 129),
       (1, 130),
       (1, 131),
       (1, 132),
       (1, 133),
       (1, 134),
       (1, 135),
       (1, 136),
       (1, 137),
       (1, 138);

-- ML Feature
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Hora del dia', 'true');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Dia del mes', 'true');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Dia de la semana', 'true');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Dia del año', 'true');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Importe sobre Promedio', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de sigmas del importe', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de dias al mismo destino', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de transacciones al mismo destino', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de dias a la misma IP', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de transacciones a la misma IP', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de dias a la última', 'false');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de transacciones', 'false');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Importe sobre Total', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de cuentas distintias desde la misma IP', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de transacciones del mismo mes', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Suma del los importes del mismo mes', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de dias a la misma IP Red', 'false');
INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXTVAL('ml_feature_id_seq'), 'Cantidad de transacciones ultimas 24hs', 'false');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Importe', 'false');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Dia del mes modulo 15', 'true');
INSERT INTO ml_feature (id, name, cyclic) VALUES (NEXTVAL('ml_feature_id_seq'), 'Features por calcular', 'false');