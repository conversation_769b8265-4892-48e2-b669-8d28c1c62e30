-- TYPES
CREATE TYPE IdList AS TABLE (
    Id bigint NOT NULL,
    PRIMARY KEY CLUSTERED (Id ASC)
    );
GO
-- END TYPES

-- STORED PROCEDURES

CREATE PROCEDURE [dbo].[ArchiveOldFingerprints]
    @Ids IdList READONLY
AS
BEGIN
    SET NOCOUNT ON;

BEGIN TRY
BEGIN TRANSACTION;

        DECLAR<PERSON> @DeletedCounts TABLE (
            EntityName NVARCHAR(255),
            DeletedCount INT
        );

        DECLARE @DeletedCount INT;

        -- Temporary tables to hold intermediate IDs
        DECLARE @CollectionIds TABLE (CollectionId BIGINT PRIMARY KEY);
        DECLARE @HashIds TABLE (HashId BIGINT PRIMARY KEY);

        -- Step 1: Retrieve necessary IDs

        -- 1.1 Retrieve Collection IDs associated with the user_hash records to be archived
INSERT INTO @CollectionIds (CollectionId)
SELECT DISTINCT fuh.fingerprint_collection_id
FROM fingerprint_user_hash fuh
         INNER JOIN @Ids i ON fuh.id = i.Id;

-- 1.2 Retrieve Hash IDs associated with the user_hash records to be archived
INSERT INTO @HashIds (HashId)
SELECT DISTINCT fuh.hash_id
FROM fingerprint_user_hash fuh
         INNER JOIN @Ids i ON fuh.id = i.Id;

-- Step 2: Archive Data in Correct Order

-- 2.1 Archive fingerprint_collection_history
-- Insert all referenced collections into history table if not already present
INSERT INTO fingerprint_collection_history (
    id, collection_uuid, creation_date, last_check_date, status,
    type, user_id, created_at, created_by
)
SELECT
    fc.id, fc.collection_uuid, fc.creation_date, fc.last_check_date,
    fc.status, fc.type, fc.user_id, GETDATE(), NULL
FROM fingerprint_collection fc
         INNER JOIN @CollectionIds ci ON fc.id = ci.CollectionId
         LEFT JOIN fingerprint_collection_history fch ON fch.id = fc.id
WHERE fch.id IS NULL;

-- 2.2 Archive fingerprint_hash_history
-- Insert all referenced hashes into history table if not already present
INSERT INTO fingerprint_hash_history (
    id, creation_date, fp_hash, primary_hash, secondary_hash,
    fingerprint_server_id, created_at, created_by
)
SELECT
    h.id, h.creation_date, h.fp_hash, h.primary_hash,
    h.secondary_hash, h.fingerprint_server_id, GETDATE(), NULL
FROM fingerprint_hash h
         INNER JOIN @HashIds hi ON h.id = hi.HashId
         LEFT JOIN fingerprint_hash_history fhh ON fhh.id = h.id
WHERE fhh.id IS NULL;

-- 2.3 Archive fingerprint_user_hash_history
-- Insert user_hash records into history table
INSERT INTO fingerprint_user_hash_history (
    id, accuracy_percent, apikey_id, count_used, creation_date,
    fingerprint_collection_id, hash_id, last_check_date, status,
    user_id, created_at, created_by
)
SELECT
    fuh.id, fuh.accuracy_percent, fuh.apikey_id, fuh.count_used, fuh.creation_date,
    fuh.fingerprint_collection_id, fuh.hash_id, fuh.last_check_date, fuh.status,
    fuh.user_id, GETDATE(), NULL
FROM fingerprint_user_hash fuh
         INNER JOIN @Ids i ON fuh.id = i.Id
         LEFT JOIN fingerprint_user_hash_history fuhh ON fuhh.id = fuh.id
WHERE fuhh.id IS NULL;

-- 2.4 Archive fingerprint_response_history
INSERT INTO fingerprint_response_history (
    id, accuracy_percent, creation_date, hash_known, result_code,
    user_hash_id, user_id, created_at, created_by
)
SELECT
    fr.id, fr.accuracy_percent, fr.creation_date, fr.hash_known, fr.result_code,
    fr.user_hash_id, fr.user_id, GETDATE(), NULL
FROM fingerprint_response fr
         INNER JOIN @Ids i ON fr.user_hash_id = i.Id
         LEFT JOIN fingerprint_response_history frh ON frh.id = fr.id
WHERE frh.id IS NULL;

-- 2.5 Archive fingerprint_unverified_value_history
INSERT INTO fingerprint_unverified_value_history (
    id, creation_date, token, user_hash_id, created_at, created_by
)
SELECT
    fu.id, fu.creation_date, fu.token, fu.user_hash_id,
    GETDATE(), NULL
FROM fingerprint_unverified_value fu
         INNER JOIN @Ids i ON fu.user_hash_id = i.Id
         LEFT JOIN fingerprint_unverified_value_history fuvh ON fuvh.id = fu.id
WHERE fuvh.id IS NULL;

-- 2.6 Archive fingerprint_agent_value_history
INSERT INTO fingerprint_agent_value_history (
    hash_id, browser_major, browser_name, browser_version,
    cpu_architecture, device_model, device_type, device_vendor,
    engine_name, engine_version, os_name, os_version, ua, created_at, created_by
)
SELECT
    fa.hash_id, fa.browser_major, fa.browser_name, fa.browser_version,
    fa.cpu_architecture, fa.device_model, fa.device_type, fa.device_vendor,
    fa.engine_name, fa.engine_version, fa.os_name, fa.os_version, fa.ua,
    GETDATE(), NULL
FROM fingerprint_agent_value fa
         INNER JOIN @HashIds hi ON fa.hash_id = hi.HashId
         LEFT JOIN fingerprint_agent_value_history fa_vh ON fa_vh.hash_id = fa.hash_id
WHERE fa_vh.hash_id IS NULL;

-- 2.7 Archive fingerprint_web_hash_history
INSERT INTO fingerprint_web_hash_history (
    audio, cookies_enabled, crc_canvas, crc_geometry, crc_text,
    crc_webgl, do_not_track, dom_blocker, extended_fonts, fonts,
    font_pref_default, font_pref_apple, font_pref_min, font_pref_mono,
    font_pref_sans, font_pref_serif, font_pref_system, gpu,
    logical_processors, memory, has_ad_block, has_add_behavior,
    has_indexed_db, has_lied_resolution, has_lied_languages,
    has_lied_os, has_local_storage, has_open_database,
    has_session_storage, language, local_storage_id, maths_acos,
    maths_acosh, maths_acos_pf, maths_asin, maths_asinh,
    maths_asin_pf, maths_atan, maths_atanh, maths_atanh_pf,
    maths_cos, maths_cosh, maths_cosh_pf, maths_exp, maths_expm1,
    maths_expm1_pf, maths_log1p, maths_log1p_pf, maths_pow_pi,
    maths_sin, maths_sinh, maths_sin_pf, maths_tan, maths_tanh,
    maths_tanh_pf, color_gamut, contrast, forced_colors, hdr,
    inverted_colors, monochrome, reduced_motion, navigator_cpu_class,
    navigator_platform, plugins, random_seed, color_depth,
    frame_size, available_height, avaible_width, screen_height,
    screen_width, timezone, timezone_offset, max_touch_points,
    touch_event, touch_start, vendor, vendor_flavors, type, id,
    created_at, created_by
)
SELECT
    fw.audio, fw.cookies_enabled, fw.crc_canvas, fw.crc_geometry,
    fw.crc_text, fw.crc_webgl, fw.do_not_track, fw.dom_blocker,
    fw.extended_fonts, fw.fonts, fw.font_pref_default, fw.font_pref_apple,
    fw.font_pref_min, fw.font_pref_mono, fw.font_pref_sans,
    fw.font_pref_serif, fw.font_pref_system, fw.gpu,
    fw.logical_processors, fw.memory, fw.has_ad_block,
    fw.has_add_behavior, fw.has_indexed_db, fw.has_lied_resolution,
    fw.has_lied_languages, fw.has_lied_os, fw.has_local_storage,
    fw.has_open_database, fw.has_session_storage, fw.language,
    fw.local_storage_id, fw.maths_acos, fw.maths_acosh,
    fw.maths_acos_pf, fw.maths_asin, fw.maths_asinh,
    fw.maths_asin_pf, fw.maths_atan, fw.maths_atanh,
    fw.maths_atanh_pf, fw.maths_cos, fw.maths_cosh,
    fw.maths_cosh_pf, fw.maths_exp, fw.maths_expm1,
    fw.maths_expm1_pf, fw.maths_log1p, fw.maths_log1p_pf,
    fw.maths_pow_pi, fw.maths_sin, fw.maths_sinh,
    fw.maths_sin_pf, fw.maths_tan, fw.maths_tanh,
    fw.maths_tanh_pf, fw.color_gamut, fw.contrast,
    fw.forced_colors, fw.hdr, fw.inverted_colors,
    fw.monochrome, fw.reduced_motion, fw.navigator_cpu_class,
    fw.navigator_platform, fw.plugins, fw.random_seed, fw.color_depth,
    fw.frame_size, fw.available_height, fw.avaible_width,
    fw.screen_height, fw.screen_width, fw.timezone, fw.timezone_offset,
    fw.max_touch_points, fw.touch_event, fw.touch_start, fw.vendor,
    fw.vendor_flavors, fw.type, fw.id,
    GETDATE(), NULL
FROM fingerprint_web_hash fw
         INNER JOIN @HashIds hi ON fw.id = hi.HashId
         LEFT JOIN fingerprint_web_hash_history fwhh ON fwhh.id = fw.id
WHERE fwhh.id IS NULL;

-- 2.8 Archive fingerprint_device_value_history
INSERT INTO fingerprint_device_value_history (
    api_level, device_id, device_manufacturer, device_name,
    device_resolution, fingerprint_compatible, gps_compatible,
    rooted, os_name, os_version, random_seed, id,
    created_at, created_by
)
SELECT
    fd.api_level, fd.device_id, fd.device_manufacturer, fd.device_name,
    fd.device_resolution, fd.fingerprint_compatible, fd.gps_compatible,
    fd.rooted, fd.os_name, fd.os_version, fd.random_seed, fd.id,
    GETDATE(), NULL
FROM fingerprint_device_value fd
         INNER JOIN @HashIds hi ON fd.id = hi.HashId
         LEFT JOIN fingerprint_device_value_history fdvh ON fdvh.id = fd.id
WHERE fdvh.id IS NULL;

-- Step 3: Delete Original Records in Reverse Order

-- 3.1 Delete from fingerprint_response
DELETE fr
        FROM fingerprint_response fr
        INNER JOIN @Ids i ON fr.user_hash_id = i.Id;
        SET @DeletedCount = @@ROWCOUNT;
INSERT INTO @DeletedCounts (EntityName, DeletedCount) VALUES ('fingerprint_response', @DeletedCount);

-- 3.2 Delete from fingerprint_unverified_value
DELETE fu
        FROM fingerprint_unverified_value fu
        INNER JOIN @Ids i ON fu.user_hash_id = i.Id;
        SET @DeletedCount = @@ROWCOUNT;
INSERT INTO @DeletedCounts (EntityName, DeletedCount) VALUES ('fingerprint_unverified_value', @DeletedCount);

-- 3.3 Delete from fingerprint_user_hash
DELETE fuh
        FROM fingerprint_user_hash fuh
        INNER JOIN @Ids i ON fuh.id = i.Id;
        SET @DeletedCount = @@ROWCOUNT;
INSERT INTO @DeletedCounts (EntityName, DeletedCount) VALUES ('fingerprint_user_hash', @DeletedCount);

-- 3.4 Delete from fingerprint_collection where no user_hash exists
DELETE fc
        FROM fingerprint_collection fc
        INNER JOIN @CollectionIds ci ON fc.id = ci.CollectionId
        WHERE NOT EXISTS (
            SELECT 1
            FROM fingerprint_user_hash fuh
            WHERE fuh.fingerprint_collection_id = fc.id
        );
        SET @DeletedCount = @@ROWCOUNT;
INSERT INTO @DeletedCounts (EntityName, DeletedCount) VALUES ('fingerprint_collection', @DeletedCount);

-- 3.5 Delete from fingerprint_agent_value, fingerprint_web_hash, fingerprint_device_value, and fingerprint_hash
DELETE fa
        FROM fingerprint_agent_value fa
        INNER JOIN @HashIds hi ON fa.hash_id = hi.HashId;
        SET @DeletedCount = @@ROWCOUNT;
INSERT INTO @DeletedCounts (EntityName, DeletedCount) VALUES ('fingerprint_agent_value', @DeletedCount);

DELETE fw
        FROM fingerprint_web_hash fw
        INNER JOIN @HashIds hi ON fw.id = hi.HashId;
        SET @DeletedCount = @@ROWCOUNT;
INSERT INTO @DeletedCounts (EntityName, DeletedCount) VALUES ('fingerprint_web_hash', @DeletedCount);

DELETE fd
        FROM fingerprint_device_value fd
        INNER JOIN @HashIds hi ON fd.id = hi.HashId;
        SET @DeletedCount = @@ROWCOUNT;
INSERT INTO @DeletedCounts (EntityName, DeletedCount) VALUES ('fingerprint_device_value', @DeletedCount);

DELETE h
        FROM fingerprint_hash h
        INNER JOIN @HashIds hi ON h.id = hi.HashId;
        SET @DeletedCount = @@ROWCOUNT;
INSERT INTO @DeletedCounts (EntityName, DeletedCount) VALUES ('fingerprint_hash', @DeletedCount);

-- Step 4: Commit Transaction
COMMIT TRANSACTION;

-- Step 5: Return the counts of deleted entities
SELECT * FROM @DeletedCounts;
END TRY
BEGIN CATCH
        -- Rollback transaction if any error occurs
IF XACT_STATE() <> 0
            ROLLBACK TRANSACTION;

        -- Raise the error
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        RAISERROR (@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH
END
GO


-- END STORED PROCEDURES

-- VIEWS
EXECUTE ('CREATE VIEW assigned_cases_view as
SELECT ac.id, ac.transaction_id, ac.assigned_to, ac.case_status_id, cs.description AS case_state, u.user_name, u.first_name, u.last_name
FROM  assigned_case AS ac
          INNER JOIN auth_fa_user AS u ON ac.assigned_to = u.id
          INNER JOIN case_status AS cs ON ac.case_status_id = cs.id
WHERE (ac.assigned_date =(SELECT MAX(assigned_date) AS max
    FROM  assigned_case AS a
    WHERE (transaction_id = ac.transaction_id)
    GROUP BY transaction_id))')

EXECUTE ('CREATE VIEW all_cases_view as
SELECT  t.id AS idPrimary, t.event_date, t.channel_id, t.operation_type_id,
        c.name AS channel, ot.name AS operationType, t.user_id AS [user],
			t.credit_account, t.debit_account, t.amount, t.case_status_id,
			t.ip,geo.country,geo.continent_name, geo.city, geo.postal_code,
			ctf.id,ctf.transaction_id,
		    ctf.field1,   ctf.field2, ctf.field3, ctf.field4, ctf.field5, ctf.field6, ctf.field7, ctf.field8, ctf.field9, ctf.field10,
            ctf.field11, ctf.field12, ctf.field13, ctf.field14, ctf.field15, ctf.field16, ctf.field17, ctf.field18, ctf.field19, ctf.field20,
            ctf.field21, ctf.field22, ctf.field23, ctf.field24, ctf.field25, ctf.field26, ctf.field27, ctf.field28, ctf.field29, ctf.field30,
            ctf.field31, ctf.field32, ctf.field33, ctf.field34, ctf.field35, ctf.field36, ctf.field37, ctf.field38, ctf.field39, ctf.field40,
            ctf.field41, ctf.field42, ctf.field43, ctf.field44, ctf.field45, ctf.field46, ctf.field47, ctf.field48, ctf.field49, ctf.field50,
            ctf.field51, ctf.field52, ctf.field53, ctf.field54, ctf.field55, ctf.field56, ctf.field57, ctf.field58, ctf.field59, ctf.field60,
            ctf.field61, ctf.field62, ctf.field63, ctf.field64, ctf.field65, ctf.field66, ctf.field67, ctf.field68, ctf.field69, ctf.field70,
            ctf.field71, ctf.field72, ctf.field73, ctf.field74, ctf.field75, ctf.field76, ctf.field77, ctf.field78, ctf.field79, ctf.field80,
            ctf.field81, ctf.field82, ctf.field83, ctf.field84, ctf.field85, ctf.field86, ctf.field87, ctf.field88, ctf.field89, ctf.field90,
            ctf.field91, ctf.field92, ctf.field93, ctf.field94, ctf.field95, ctf.field96, ctf.field97, ctf.field98, ctf.field99, ctf.field100
FROM transaction_entity t
    INNER JOIN channel AS c ON c.id = t.channel_id
    INNER JOIN operation_type AS ot ON ot.id = t.operation_type_id
    LEFT OUTER JOIN additional_parameter_transaction AS ctf ON ctf.transaction_id = t.id
    LEFT OUTER JOIN geo_ip_data AS geo ON geo.transaction_id = t.id');

EXECUTE ('CREATE VIEW transactions_triggered_rules_view as
SELECT
    t.id primary_id, t.event_date, t.time, t.channel_id, t.operation_type_id, t.user_id,
    t.credit_account, t.debit_account, t.amount, t.case_status_id, t.ip, c.name channel,
    o.name operation_type,  COALESCE(r.id, 0) rule_id,  r.name rule_name,
    ctf.id ,ctf.transaction_id, ctf.field1, ctf.field2,ctf.field3, ctf.field4,ctf.field5, ctf.field6,
    ctf.field7,ctf.field8,ctf.field9,
    ctf.field10,ctf.field11, ctf.field12,
    ctf.field13,ctf.field14, ctf.field15,
    ctf.field16,ctf.field17, ctf.field18,
    ctf.field19,ctf.field20,       ctf.field21, ctf.field22,
    ctf.field23,ctf.field24,       ctf.field25, ctf.field26,
    ctf.field27,       ctf.field28,       ctf.field29,       ctf.field30,
    ctf.field31,       ctf.field32,       ctf.field33,       ctf.field34,
    ctf.field35,       ctf.field36,       ctf.field37,       ctf.field38,
    ctf.field39,       ctf.field40,       ctf.field41,       ctf.field42,
    ctf.field43,       ctf.field44,       ctf.field45,       ctf.field46,
    ctf.field47,       ctf.field48,       ctf.field49,       ctf.field50,
    ctf.field51,       ctf.field52,       ctf.field53,       ctf.field54,
    ctf.field55,       ctf.field56,       ctf.field57,       ctf.field58,
    ctf.field59,       ctf.field60,       ctf.field61,       ctf.field62,
    ctf.field63,       ctf.field64,       ctf.field65,       ctf.field66,
    ctf.field67,       ctf.field68,       ctf.field69,       ctf.field70,
    ctf.field71,       ctf.field72,       ctf.field73,       ctf.field74,
    ctf.field75,       ctf.field76,       ctf.field77,       ctf.field78,
    ctf.field79,       ctf.field80,       ctf.field81,       ctf.field82,
    ctf.field83,       ctf.field84,       ctf.field85,       ctf.field86,
    ctf.field87,       ctf.field88,       ctf.field89,       ctf.field90,
    ctf.field91,       ctf.field92,       ctf.field93,       ctf.field94,
    ctf.field95,       ctf.field96,       ctf.field97,       ctf.field98,
    ctf.field99,       ctf.field100,       geo.country,       geo.continent_name,
    geo.city,       geo.postal_code
FROM transaction_entity t
         JOIN channel c ON c.id = t.channel_id
         JOIN operation_type o ON o.id = t.operation_type_id
         LEFT JOIN additional_parameter_transaction ctf ON ctf.transaction_id = t.id
         LEFT JOIN rule_matched_transaction te ON t.id = te.transaction_id
         LEFT JOIN rule_item r ON te.triggered_rule_item_id = r.id
         LEFT JOIN geo_ip_data geo ON geo.transaction_id = t.id');


EXECUTE('CREATE VIEW export_transaction_cases_view AS
SELECT
    t.id AS primary_id,
    t.event_date,
    c.name AS channel,
    ot.name AS operation_type,
    ot.event_type_id,
    t.user_id AS user_id,
    t.credit_account,
    t.debit_account,
    t.amount,
    t.action_id,
    cs.description AS case_status,
    t.ip,
    geo.country,
    geo.continent_name,
    geo.city,
    geo.postal_code,
    CASE
        WHEN cep_rule_matched.transaction_id IS NOT NULL THEN CAST(1 AS BIT)
        ELSE CAST(0 AS BIT)
    END AS cep_rule_matched,
    CASE
        WHEN rule_matched.transaction_id IS NOT NULL THEN CAST(1 AS BIT)
        ELSE CAST(0 AS BIT)
    END AS rule_matched,
    ri.rule_type_id,
    ri.id AS rule_id,
    CASE
		WHEN rule_matched.rule_name IS NOT NULL THEN rule_matched.rule_name
		ELSE ri.name
	END AS rule_name,
    cep_rule_matched.cep_rule_id,
    cep_rule_matched.rule_name AS cep_rule_name,
    additional_parameters.field1,
    additional_parameters.field2,
    additional_parameters.field3,
    additional_parameters.field4,
    additional_parameters.field5,
    additional_parameters.field6,
    additional_parameters.field7,
    additional_parameters.field8,
    additional_parameters.field9,
    additional_parameters.field10,
    additional_parameters.field11,
    additional_parameters.field12,
    additional_parameters.field13,
    additional_parameters.field14,
    additional_parameters.field15,
    additional_parameters.field16,
    additional_parameters.field17,
    additional_parameters.field18,
    additional_parameters.field19,
    additional_parameters.field20,
    additional_parameters.field21,
    additional_parameters.field22,
    additional_parameters.field23,
    additional_parameters.field24,
    additional_parameters.field25,
    additional_parameters.field26,
    additional_parameters.field27,
    additional_parameters.field28,
    additional_parameters.field29,
    additional_parameters.field30,
    additional_parameters.field31,
    additional_parameters.field32,
    additional_parameters.field33,
    additional_parameters.field34,
    additional_parameters.field35,
    additional_parameters.field36,
    additional_parameters.field37,
    additional_parameters.field38,
    additional_parameters.field39,
    additional_parameters.field40,
    additional_parameters.field41,
    additional_parameters.field42,
    additional_parameters.field43,
    additional_parameters.field44,
    additional_parameters.field45,
    additional_parameters.field46,
    additional_parameters.field47,
    additional_parameters.field48,
    additional_parameters.field49,
    additional_parameters.field50,
    additional_parameters.field51,
    additional_parameters.field52,
    additional_parameters.field53,
    additional_parameters.field54,
    additional_parameters.field55,
    additional_parameters.field56,
    additional_parameters.field57,
    additional_parameters.field58,
    additional_parameters.field59,
    additional_parameters.field60,
    additional_parameters.field61,
    additional_parameters.field62,
    additional_parameters.field63,
    additional_parameters.field64,
    additional_parameters.field65,
    additional_parameters.field66,
    additional_parameters.field67,
    additional_parameters.field68,
    additional_parameters.field69,
    additional_parameters.field70,
    additional_parameters.field71,
    additional_parameters.field72,
    additional_parameters.field73,
    additional_parameters.field74,
    additional_parameters.field75,
    additional_parameters.field76,
    additional_parameters.field77,
    additional_parameters.field78,
    additional_parameters.field79,
    additional_parameters.field80,
    additional_parameters.field81,
    additional_parameters.field82,
    additional_parameters.field83,
    additional_parameters.field84,
    additional_parameters.field85,
    additional_parameters.field86,
    additional_parameters.field87,
    additional_parameters.field88,
    additional_parameters.field89,
    additional_parameters.field90,
    additional_parameters.field91,
    additional_parameters.field92,
    additional_parameters.field93,
    additional_parameters.field94,
    additional_parameters.field95,
    additional_parameters.field96,
    additional_parameters.field97,
    additional_parameters.field98,
    additional_parameters.field99,
    additional_parameters.field100
FROM
    transaction_entity t
        INNER JOIN channel AS c ON c.id = t.channel_id
        INNER JOIN operation_type AS ot ON ot.id = t.operation_type_id
        LEFT OUTER JOIN additional_parameter_transaction AS additional_parameters ON additional_parameters.transaction_id = t.id
        LEFT OUTER JOIN geo_ip_data AS geo ON geo.transaction_id = t.id
        LEFT OUTER JOIN case_status AS cs ON cs.id = t.case_status_id
        LEFT OUTER JOIN cep_rule_matched_transaction AS cep_rule_matched ON cep_rule_matched.transaction_id = t.id
        LEFT OUTER JOIN rule_matched_transaction AS rule_matched ON rule_matched.transaction_id = t.id
        LEFT OUTER JOIN rule_item AS ri ON ri.id = rule_matched.triggered_rule_item_id')

    EXECUTE('CREATE VIEW export_event_cases_view AS
SELECT
    e.id AS primary_id,
    e.event_date,
    c.name AS channel,
    ot.name AS operation_type,
    e.user_id AS user_id,
    e.action_id,
    CASE
        WHEN rule_matched.event_id IS NOT NULL THEN CAST(1 AS BIT)
        ELSE CAST(0 AS BIT)
        END AS rule_matched,
    ri.rule_type_id,
    ri.id AS rule_id,
    ri.name AS rule_name,
    additional_parameters.field1,
    additional_parameters.field2,
    additional_parameters.field3,
    additional_parameters.field4,
    additional_parameters.field5,
    additional_parameters.field6,
    additional_parameters.field7,
    additional_parameters.field8,
    additional_parameters.field9,
    additional_parameters.field10,
    additional_parameters.field11,
    additional_parameters.field12,
    additional_parameters.field13,
    additional_parameters.field14,
    additional_parameters.field15,
    additional_parameters.field16,
    additional_parameters.field17,
    additional_parameters.field18,
    additional_parameters.field19,
    additional_parameters.field20,
    additional_parameters.field21,
    additional_parameters.field22,
    additional_parameters.field23,
    additional_parameters.field24,
    additional_parameters.field25,
    additional_parameters.field26,
    additional_parameters.field27,
    additional_parameters.field28,
    additional_parameters.field29,
    additional_parameters.field30,
    additional_parameters.field31,
    additional_parameters.field32,
    additional_parameters.field33,
    additional_parameters.field34,
    additional_parameters.field35,
    additional_parameters.field36,
    additional_parameters.field37,
    additional_parameters.field38,
    additional_parameters.field39,
    additional_parameters.field40,
    additional_parameters.field41,
    additional_parameters.field42,
    additional_parameters.field43,
    additional_parameters.field44,
    additional_parameters.field45,
    additional_parameters.field46,
    additional_parameters.field47,
    additional_parameters.field48,
    additional_parameters.field49,
    additional_parameters.field50,
    additional_parameters.field51,
    additional_parameters.field52,
    additional_parameters.field53,
    additional_parameters.field54,
    additional_parameters.field55,
    additional_parameters.field56,
    additional_parameters.field57,
    additional_parameters.field58,
    additional_parameters.field59,
    additional_parameters.field60,
    additional_parameters.field61,
    additional_parameters.field62,
    additional_parameters.field63,
    additional_parameters.field64,
    additional_parameters.field65,
    additional_parameters.field66,
    additional_parameters.field67,
    additional_parameters.field68,
    additional_parameters.field69,
    additional_parameters.field70,
    additional_parameters.field71,
    additional_parameters.field72,
    additional_parameters.field73,
    additional_parameters.field74,
    additional_parameters.field75,
    additional_parameters.field76,
    additional_parameters.field77,
    additional_parameters.field78,
    additional_parameters.field79,
    additional_parameters.field80,
    additional_parameters.field81,
    additional_parameters.field82,
    additional_parameters.field83,
    additional_parameters.field84,
    additional_parameters.field85,
    additional_parameters.field86,
    additional_parameters.field87,
    additional_parameters.field88,
    additional_parameters.field89,
    additional_parameters.field90,
    additional_parameters.field91,
    additional_parameters.field92,
    additional_parameters.field93,
    additional_parameters.field94,
    additional_parameters.field95,
    additional_parameters.field96,
    additional_parameters.field97,
    additional_parameters.field98,
    additional_parameters.field99,
    additional_parameters.field100
FROM
    [event] e
    INNER JOIN channel AS c ON c.id = e.channel_id
    INNER JOIN operation_type AS ot ON ot.id = e.operation_id
    LEFT OUTER JOIN additional_parameter_event AS additional_parameters ON additional_parameters.event_id = e.id
    LEFT OUTER JOIN rule_matched_event AS rule_matched ON rule_matched.event_id = e.id
    LEFT OUTER JOIN rule_item AS ri ON ri.id = rule_matched.triggered_rule_item_id')

EXECUTE('
CREATE VIEW [audit_log_view] AS
SELECT ar.*, afuh.app_user as afuh_app_user, afuh.email as afuh_email, afuh.enabled as afuh_enabled, afuh.first_name as afuh_first_name,
       afuh.force_reset_password as afuh_force_reset_password, afuh.id as afuh_id, afuh.last_name as afuh_last_name, afuh.ldap_user as afuh_ldap_user,
       afuh.rev as afuh_rev, afuh.revtype as afuh_revtype, afuh.role_changed as afuh_role_changed, afuh.token_expired as afuh_token_expired,
       afuh.user_name as afuh_user_name, alrh.group_cn as alrh_group_cn, alrh.id as alrh_id, alrh.rev as alrh_rev, alrh.revtype as alrh_revtype,
       alrh.role_id as alrh_role_id, alrh.role_name as alrh_role_name, arh.id as arh_id, arh.name as arh_name, arh.rev as arh_rev, arh.revtype as arh_revtype,
       arph.privilege_id as arph_privilege_id, arph.rev as arph_rev, arph.revtype as arph_revtype, arph.role_id as arph_role_id, aurh.rev as aurh_rev,
       aurh.revtype as aurh_revtype, aurh.role_id as aurh_role_id, aurh.user_id as aurh_user_id, ch.description as ch_description, ch.enabled as ch_enabled, ch.id as ch_id,
       ch.ignore_rules as ch_ignore_rules, ch.name as ch_name, ch.rev as ch_rev, ch.revtype as ch_revtype, oth.description as oth_description,
       oth.enabled as oth_enabled, oth.event_type_id as oth_event_type_id, oth.id as oth_id, oth.name as oth_name, oth.rev as oth_rev, oth.revtype as oth_revtype,
       pcoh.channel_id as pcoh_channel_id, pcoh.enabled as pcoh_enabled, pcoh.id as pcoh_id, pcoh.operation_type_id as pcoh_operation_type_id,
       pcoh.parameter_id as pcoh_parameter_id, pcoh.rev as pcoh_rev, pcoh.revtype as pcoh_revtype, ph.column_name as ph_column_name, ph.enabled as ph_enabled,
       ph.event_type_id as ph_event_type_id, ph.id as ph_id, ph.name as ph_name, ph.parameter_info_id as ph_parameter_info_id,
       ph.parameter_type_id as ph_parameter_type_id, ph.rev as ph_rev, ph.revtype as ph_revtype, ph.user_generated as ph_user_generated, ph.visibility as ph_visibility,
       rch.condition_rule_type_id as rch_condition_rule_type_id, rch.condition_type_id as rch_condition_type_id,
       rch.condition_value as rch_condition_value, rch.connector_id as rch_connector_id, rch.custom_list_id as rch_custom_list_id,
       rch.delta_type_id as rch_delta_type_id, rch.field_id as rch_field_id, rch.group_id as rch_group_id, rch.id as rch_id, rch.operator_id as rch_operator_id,
       rch.parent_group_id as rch_parent_group_id, rch.rev as rch_rev, rch.revtype as rch_revtype, rch.rule_id as rch_rule_id, rih.action_id as rih_action_id,
       rih.channel_id as rih_channel_id, rih.critical_level_id as rih_critical_level_id, rih.enabled as rih_enabled, rih.id as rih_id, rih.impact_id as rih_impact_id,
       rih.json_format as rih_json_format, rih.name as rih_name, rih.operation_type_id as rih_operation_type_id, rih.position as rih_position,
       rih.probability_id as rih_probability_id, rih.rev as rih_rev, rih.revtype as rih_revtype, rih.rule_type_id as rih_rule_type_id, rih.score as rih_score,
       sph.config_value as sph_config_value, sph.id as sph_id, sph.parameter_name as sph_parameter_name, sph.rev as sph_rev, sph.revtype as sph_revtype
FROM audit_revision ar
         LEFT JOIN auth_fa_user_history afuh on ar.id = afuh.rev
         LEFT JOIN auth_ldap_role_history alrh on ar.id = alrh.rev
         LEFT JOIN auth_role_history arh on ar.id = arh.rev
         LEFT JOIN auth_role_privilege_history arph on ar.id = arph.rev
         LEFT JOIN auth_user_role_history aurh on ar.id = aurh.rev
         LEFT JOIN channel_history ch on ar.id = ch.rev
         LEFT JOIN operation_type_history oth on ar.id = oth.rev
         LEFT JOIN parameter_channel_operation_history pcoh on ar.id = pcoh.rev
         LEFT JOIN parameter_history ph on ar.id = ph.rev
         LEFT JOIN rule_condition_history rch on ar.id = rch.rev
         LEFT JOIN rule_item_history rih on ar.id = rih.rev
         LEFT JOIN system_parameter_history sph on ar.id = sph.rev;
    ')
-- END VIEWS
