/****************************************************************
Update Sequence
****************************************************************/

-- Start Update Sequence
DECLARE @updateSequenceTableName NVARCHAR(128);
DECLARE @updateSequenceName NVARCHAR(128);
DECLARE @maxID BIGINT;
DECLARE @updateSequenceColumnName NVARCHAR(MAX);
DECLARE @updateSequenceValue BIGINT;
DECLARE @updateSequenceSql NVARCHAR(MAX);

PRINT 'Initializing cursor for sequence and table matching.'

DECLARE sequence_cursor CURSOR FOR
SELECT TABLE_NAME = t.name, SEQUENCE_NAME = s.name FROM sys.tables t JOIN sys.sequences s ON t.name + '_id_seq' = s.name
ORDER BY t.name

PRINT 'Opening cursor.'
OPEN sequence_cursor

FETCH NEXT FROM sequence_cursor INTO @updateSequenceTableName, @updateSequenceName;
WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT 'Processing table: ' + @updateSequenceTableName + ' with sequence: ' + @updateSequenceName
    IF @updateSequenceTableName != 'ml_transaction_result'
        SET @updateSequenceColumnName = 'id';
    ELSE
        SET @updateSequenceColumnName = 'transaction_id';

    PRINT 'Column name set to: ' + @updateSequenceColumnName

    SET @updateSequenceSql = N'SELECT @maxID = MAX(' + QUOTENAME(@updateSequenceColumnName) + ') FROM ' + @updateSequenceTableName;

    PRINT 'Fetching maximum ID from table.'
    EXEC sp_executesql @updateSequenceSql, N'@maxID BIGINT OUTPUT', @maxID = @maxID OUTPUT;

    PRINT 'Max ID in table: ' + CAST(@maxID AS NVARCHAR(50))

    SET @updateSequenceSql = N'SELECT @updateSequenceValue = CONVERT(BIGINT, current_value) FROM sys.sequences WHERE name = @updateSequenceName';

    PRINT 'Fetching current sequence value.'
    EXEC sp_executesql @updateSequenceSql, N'@updateSequenceValue BIGINT OUTPUT, @updateSequenceName SYSNAME', @updateSequenceValue OUTPUT, @updateSequenceName = @updateSequenceName;

    PRINT 'Current sequence value: ' + CAST(@updateSequenceValue AS NVARCHAR(50)) + ' Max ID value: '+ CAST(@maxID AS NVARCHAR(50))

    IF @maxID >= @updateSequenceValue
    BEGIN
        PRINT 'Max ID is greater or equal than current sequence value. Updating sequence.'
        SET @updateSequenceSql = N'ALTER SEQUENCE ' + @updateSequenceName + ' RESTART WITH ' + CAST(@maxID+1 AS NVARCHAR(50));
        EXEC sp_executesql @updateSequenceSql;
    END
    ELSE
    BEGIN
        PRINT 'Max ID is not greater than current sequence value. No update needed.'
    END

    FETCH NEXT FROM sequence_cursor INTO @updateSequenceTableName, @updateSequenceName;
END

PRINT 'Closing cursor.'
CLOSE sequence_cursor;
DEALLOCATE sequence_cursor;

PRINT 'Update Sequence script completed.'
GO
-- End Update Sequence