-- CONSTRAINTS
ALTER TABLE accumulator
    ADD CONSTRAINT fk_accumulator_from_classifier FOREIGN KEY (classifier_id) REFERENCES classifier;
ALTER TABLE accumulator_parameter
    ADD CONSTRAINT fk_accumulator_parameter_from_accumulator FOREI<PERSON>N KEY (accumulator_id) REFERENCES accumulator;
ALTER TABLE accumulator_parameter
    ADD CONSTRAINT fk_accumulator_parameter_from_parameter FOREIGN KEY (parameter_id) REFERENCES parameter;
ALTER TABLE accumulator_rule_condition
    ADD CONSTRAINT fk_accumulator_rule_condition_from_accumulator FOREIGN KEY (accumulator_id) REFERENCES accumulator;
ALTER TABLE accumulator_rule_condition
    ADD CONSTRAINT fk_accumulator_rule_condition_from_rule_condition FOREIGN KEY (rule_condition_id) REFERENCES rule_condition;
ALTER TABLE additional_parameter_event
    ADD CONSTRAINT fk_event_additional_field_from_event_entity FOREIGN KEY (event_id) REFERENCES event;
ALTER TABLE additional_parameter_transaction
    ADD CONSTRAINT fk_transaction_additional_field_from_transaction_entity FOREIGN KEY (transaction_id) REFERENCES transaction_entity;
ALTER TABLE assigned_case
    ADD CONSTRAINT fk_assigned_case_from_business_group FOREIGN KEY (business_group_id) REFERENCES business_group;
ALTER TABLE assigned_case
    ADD CONSTRAINT fk_assigned_case_from_transaction_entity FOREIGN KEY (transaction_id) REFERENCES transaction_entity;
ALTER TABLE assigned_case
    ADD CONSTRAINT fk_assigned_case_by_from_auth_fa_user FOREIGN KEY (assigned_by) REFERENCES auth_fa_user;
ALTER TABLE assigned_case
    ADD CONSTRAINT fk_assigned_case_to_from_auth_fa_user FOREIGN KEY (assigned_to) REFERENCES auth_fa_user;
ALTER TABLE auth_password_reset_token
    ADD CONSTRAINT fk_auth_password_reset_token_from_auth_fa_user FOREIGN KEY (user_id) REFERENCES auth_fa_user;
ALTER TABLE auth_role_privilege
    ADD CONSTRAINT FK_m4wroyfsiv14tlurah4qipagy FOREIGN KEY (privilege_id) REFERENCES auth_privilege;
ALTER TABLE auth_role_privilege
    ADD CONSTRAINT FK_chdye2i40ooa9d1da4y5oonkf FOREIGN KEY (role_id) REFERENCES auth_role;
ALTER TABLE auth_user_role
    ADD CONSTRAINT FK_asv6my78ndgs160mlq2r3ad7r FOREIGN KEY (role_id) REFERENCES auth_role;
ALTER TABLE auth_user_role
    ADD CONSTRAINT FK_49bop9tj6tt0scnnvvjmnji4c FOREIGN KEY (user_id) REFERENCES auth_fa_user;
ALTER TABLE auth_verification_token
    ADD CONSTRAINT fk_auth_verification_token_from_auth_fa_user FOREIGN KEY (user_id) REFERENCES auth_fa_user;
ALTER TABLE blocked_ip_values
    ADD CONSTRAINT fk_blocked_ip_values_from_file_entry FOREIGN KEY (file_id) REFERENCES file_entry;
ALTER TABLE case_business_auto_assign
    ADD CONSTRAINT fk_case_business_auto_assign_from_business_group FOREIGN KEY (business_group_id) REFERENCES business_group;
ALTER TABLE case_business_auto_assign
    ADD CONSTRAINT fk_case_business_auto_assign_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE case_business_auto_assign
    ADD CONSTRAINT fk_case_business_auto_assign_from_operation_type FOREIGN KEY (operation_type_id) REFERENCES operation_type;
ALTER TABLE classifier
    ADD CONSTRAINT fk_classifier_from_rule_item FOREIGN KEY (classifier_as_rule_id) REFERENCES rule_item;
ALTER TABLE classifier_condition
    ADD CONSTRAINT fk_classifier_condition_from_classifier FOREIGN KEY (classifier_id) REFERENCES classifier;
ALTER TABLE classifier_condition
    ADD CONSTRAINT fk_classifier_condition_from_parameter FOREIGN KEY (parameter_id) REFERENCES parameter;
ALTER TABLE classifier_rule
    ADD CONSTRAINT fk_classifier_rule_from_classifier FOREIGN KEY (classifier_id) REFERENCES classifier;
ALTER TABLE classifier_rule
    ADD CONSTRAINT fk_classifier_rule_from_rule_item FOREIGN KEY (rule_id) REFERENCES rule_item;
ALTER TABLE custom_list_value
    ADD CONSTRAINT fk_custom_list_values_from_custom_list FOREIGN KEY (custom_list_id) REFERENCES custom_list;
ALTER TABLE event
    ADD CONSTRAINT fk_event_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE event
    ADD CONSTRAINT fk_event_from_operation_type FOREIGN KEY (operation_id) REFERENCES operation_type;
ALTER TABLE event
    ADD CONSTRAINT fk_event_from_geo_profile_data FOREIGN KEY (geo_data_id) REFERENCES geo_profile_data;
ALTER TABLE fingerprint_agent_value
    ADD CONSTRAINT FK_hehhox5k9j2ac3nncsrjgpwj3 FOREIGN KEY (hash_id) REFERENCES fingerprint_web_hash;
ALTER TABLE fingerprint_collection
    ADD CONSTRAINT fk_fingerprint_collection_from_fingerprint_user FOREIGN KEY (user_id) REFERENCES fingerprint_user;
ALTER TABLE fingerprint_device_value
    ADD CONSTRAINT FK_301yx7qeqfor4wna2fmj5etjy FOREIGN KEY (id) REFERENCES fingerprint_hash;
ALTER TABLE fingerprint_hash
    ADD CONSTRAINT FK_syk5au1ke4qqmdssomh0paw6o FOREIGN KEY (fingerprint_server_id) REFERENCES fingerprint_server;
ALTER TABLE fingerprint_response
    ADD CONSTRAINT fk_fingerprint_response_from_fingerprint_user_hashes FOREIGN KEY (user_hash_id) REFERENCES fingerprint_user_hash;
ALTER TABLE fingerprint_unverified_value
    ADD CONSTRAINT fk_fingerprint_unverified_value_from_fingerprint_user_hashes FOREIGN KEY (user_hash_id) REFERENCES fingerprint_user_hash;
ALTER TABLE fingerprint_user_hash
    ADD CONSTRAINT fk_fingerprint_user_hash_from_fingerprint_collection FOREIGN KEY (fingerprint_collection_id) REFERENCES fingerprint_collection;
ALTER TABLE fingerprint_user_hash
    ADD CONSTRAINT fk_fingerprint_user_hash_from_fingerprint_hashes FOREIGN KEY (hash_id) REFERENCES fingerprint_hash;
ALTER TABLE fingerprint_user_hash
    ADD CONSTRAINT fk_fingerprint_user_hash_from_fingerprint_user FOREIGN KEY (user_id) REFERENCES fingerprint_user;
ALTER TABLE fingerprint_web_hash
    ADD CONSTRAINT FK_jiy7lvrb6cs9eu4lil1dkupuo FOREIGN KEY (id) REFERENCES fingerprint_hash;
ALTER TABLE geo_ip_data
    ADD CONSTRAINT FK_d7ehn7rusct8gody4cmvk7g0 FOREIGN KEY (transaction_id) REFERENCES transaction_entity;
ALTER TABLE geo_user_profile
    ADD CONSTRAINT fk_geo_user_profile_from_geo_profile_data FOREIGN KEY (geo_data_id) REFERENCES geo_profile_data;
ALTER TABLE last_assigned_case
    ADD CONSTRAINT fk_last_assigned_case_from_business_group FOREIGN KEY (business_group_id) REFERENCES business_group;
ALTER TABLE last_assigned_case
    ADD CONSTRAINT fk_last_assigned_case_from_transaction_entity FOREIGN KEY (transaction_id) REFERENCES transaction_entity;
ALTER TABLE last_assigned_case
    ADD CONSTRAINT fk_last_assigned_case_from_auth_fa_user FOREIGN KEY (user_id) REFERENCES auth_fa_user;
ALTER TABLE ml_network_log
    ADD CONSTRAINT fk_ml_network_log_from_ml_network FOREIGN KEY (network_id) REFERENCES ml_network;
ALTER TABLE ml_network_operation_type
    ADD CONSTRAINT fk_ml_network_operation_type_from_ml_network FOREIGN KEY (network_id) REFERENCES ml_network;
ALTER TABLE ml_network_operation_type
    ADD CONSTRAINT fk_ml_network_operation_type_from_operation_type FOREIGN KEY (operation_type_id) REFERENCES operation_type;
ALTER TABLE ml_transaction
    ADD CONSTRAINT fk_ml_transaction_from_channel FOREIGN KEY (channel_id) REFERENCES channel
ALTER TABLE ml_transaction
    ADD CONSTRAINT fk_ml_transaction_from_opeartion_type FOREIGN KEY (operation_type_id) REFERENCES operation_type
ALTER TABLE ml_transaction
    ADD CONSTRAINT fk_ml_transaction_from_transaction_entity FOREIGN KEY (transaction_id) REFERENCES transaction_entity
ALTER TABLE ml_transaction_feature
    ADD CONSTRAINT fk_ml_transaction_feature_from_ml_transaction FOREIGN KEY (transaction_id) REFERENCES ml_transaction
ALTER TABLE notification_config
    ADD CONSTRAINT fk_notification_config_from_business_group FOREIGN KEY (business_group_id) REFERENCES business_group;
ALTER TABLE notification_config
    ADD CONSTRAINT fk_notification_config_from_notification_types FOREIGN KEY (notification_type_id) REFERENCES notification_type;
ALTER TABLE parameter_channel_operation
    ADD CONSTRAINT fk_parameter_channel_operation_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE parameter_channel_operation
    ADD CONSTRAINT fk_parameter_channel_operation_from_operation_type FOREIGN KEY (operation_type_id) REFERENCES operation_type;
ALTER TABLE parameter_channel_operation
    ADD CONSTRAINT fk_parameter_channel_operation_from_parameter FOREIGN KEY (parameter_id) REFERENCES parameter;
ALTER TABLE parameter_query_channel_operation
    ADD CONSTRAINT fk_parameter_query_channel_operation_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE parameter_query_channel_operation
    ADD CONSTRAINT fk_parameter_query_channel_operation_from_operation_type FOREIGN KEY (operation_type_id) REFERENCES operation_type;
ALTER TABLE profile_by_operation
    ADD CONSTRAINT fk_profile_by_operation_from_operation_type FOREIGN KEY (operation_type_id) REFERENCES operation_type;
ALTER TABLE profile_by_operation
    ADD CONSTRAINT fk_profile_by_operation_from_profile_period_information FOREIGN KEY (profile_period_info_id) REFERENCES profile_period_information;
ALTER TABLE profile_day_counter
    ADD CONSTRAINT fk_profile_day_counter_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE profile_period_info_by_credit_account
    ADD CONSTRAINT fk_prof_period_info_by_credit_account_from_profile_period_info FOREIGN KEY (profile_period_info_id) REFERENCES profile_period_information;
ALTER TABLE profile_period_information
    ADD CONSTRAINT fk_profile_period_information_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE report_aggregate
    ADD CONSTRAINT fk_report_aggregates_from_report_groups FOREIGN KEY (report_group_id) REFERENCES report_group;
ALTER TABLE report_display_field
    ADD CONSTRAINT fk_report_display_fields_from_reports FOREIGN KEY (report_id) REFERENCES report;
ALTER TABLE report_filter_channel
    ADD CONSTRAINT fk_report_filter_channel_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE report_filter_channel
    ADD CONSTRAINT fk_report_filter_channel_from_reports FOREIGN KEY (report_id) REFERENCES report;
ALTER TABLE report_filter_channel
    ADD CONSTRAINT FK_8csuxvo63hhijm7tai7scg88h FOREIGN KEY (report_id) REFERENCES channel;
ALTER TABLE report_filter_operation_type
    ADD CONSTRAINT fk_report_filter_operation_type_from_operation_type FOREIGN KEY (operation_type_id) REFERENCES operation_type;
ALTER TABLE report_filter_operation_type
    ADD CONSTRAINT fk_report_filter_operation_type_from_reports FOREIGN KEY (report_id) REFERENCES report;
ALTER TABLE report_filter_rule
    ADD CONSTRAINT fk_report_filter_rules_from_reports FOREIGN KEY (report_id) REFERENCES report;
ALTER TABLE report_filter_rule
    ADD CONSTRAINT fk_report_filter_rules_from_rule_item FOREIGN KEY (rule_id) REFERENCES rule_item;
ALTER TABLE report_group
    ADD CONSTRAINT fk_report_groups_from_reports FOREIGN KEY (report_id) REFERENCES report;
ALTER TABLE rule_business_group
    ADD CONSTRAINT fk_rule_business_group_from_business_group FOREIGN KEY (business_group_id) REFERENCES business_group;
ALTER TABLE rule_business_group
    ADD CONSTRAINT fk_rule_business_group_from_rule_item FOREIGN KEY (rule_id) REFERENCES rule_item;
ALTER TABLE rule_condition
    ADD CONSTRAINT FK_nwatft68sxpvo64ljprv5jq73 FOREIGN KEY (field_id) REFERENCES parameter;
ALTER TABLE rule_condition
    ADD CONSTRAINT FK_n378tdtpr83io9nbswwvlggdc FOREIGN KEY (rule_id) REFERENCES rule_item;
ALTER TABLE rule_item
    ADD CONSTRAINT fk_rule_item_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE rule_item
    ADD CONSTRAINT fk_rule_item_from_operation_type FOREIGN KEY (operation_type_id) REFERENCES operation_type;
ALTER TABLE rule_matched_event
    ADD CONSTRAINT FK_90pt6xfgk703lc3tp5saflwp9 FOREIGN KEY (triggered_rule_item_id) REFERENCES rule_item;
ALTER TABLE rule_matched_transaction
    ADD CONSTRAINT fk_rule_matched_transaction_from_rule_item FOREIGN KEY (triggered_rule_item_id) REFERENCES rule_item;
ALTER TABLE swift
    ADD CONSTRAINT fk_swift_country_from_country FOREIGN KEY (country_id) REFERENCES country;
ALTER TABLE transaction_entity
    ADD CONSTRAINT fk_transaction_entity_from_case_status FOREIGN KEY (case_status_id) REFERENCES case_status;
ALTER TABLE transaction_entity
    ADD CONSTRAINT fk_transaction_entity_from_channel FOREIGN KEY (channel_id) REFERENCES channel;
ALTER TABLE transaction_entity
    ADD CONSTRAINT fk_transaction_entity_from_operation_type FOREIGN KEY (operation_type_id) REFERENCES operation_type;
ALTER TABLE user_business_group
    ADD CONSTRAINT fk_business_group_id FOREIGN KEY (business_group_id) REFERENCES business_group;
ALTER TABLE auth_fa_user_history
    ADD CONSTRAINT fk_auth_fa_user_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE auth_ldap_role_history
    ADD CONSTRAINT fk_auth_ldap_role_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE auth_role_history
    ADD CONSTRAINT fk_auth_role_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE auth_role_privilege_history
    ADD CONSTRAINT fk_auth_role_privilege_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE auth_user_role_history
    ADD CONSTRAINT fk_auth_user_role_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE channel_history
    ADD CONSTRAINT fk_channel_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE operation_type_history
    ADD CONSTRAINT fk_operation_type_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE parameter_channel_operation_history
    ADD CONSTRAINT fk_parameter_channel_operation_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE parameter_history
    ADD CONSTRAINT fk_parameter_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE rule_condition_history
    ADD CONSTRAINT fk_rule_condition_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE rule_item_history
    ADD CONSTRAINT fk_rule_item_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE system_parameter_history
    ADD CONSTRAINT fk_system_parameter_history_audit_revision FOREIGN KEY (rev) REFERENCES audit_revision;
ALTER TABLE fingerprint_unverified_value_history
    ADD CONSTRAINT fk_fingerprint_unverified_value_history_user_hash FOREIGN KEY (user_hash_id) REFERENCES fingerprint_user_hash_history (id);
ALTER TABLE fingerprint_response_history
    ADD CONSTRAINT fk_fingerprint_response_history_user_hash FOREIGN KEY (user_hash_id) REFERENCES fingerprint_user_hash_history (id);
ALTER TABLE fingerprint_user_hash_history
    ADD CONSTRAINT fk_fingerprint_user_hash_history_collection FOREIGN KEY (fingerprint_collection_id) REFERENCES fingerprint_collection_history (id);
ALTER TABLE fingerprint_user_hash_history
    ADD CONSTRAINT fk_fingerprint_user_hash_history_hash FOREIGN KEY (hash_id) REFERENCES fingerprint_hash_history (id);
ALTER TABLE fingerprint_user_hash_history
    ADD CONSTRAINT fk_fingerprint_user_hash_history_user FOREIGN KEY (user_id) REFERENCES fingerprint_user (id);
ALTER TABLE fingerprint_agent_value_history
    ADD CONSTRAINT fk_fingerprint_agent_value_history_hash FOREIGN KEY (hash_id) REFERENCES fingerprint_web_hash_history (id);
ALTER TABLE fingerprint_web_hash_history
    ADD CONSTRAINT fk_fingerprint_web_hash_history_hash FOREIGN KEY (id) REFERENCES fingerprint_hash_history (id);
ALTER TABLE fingerprint_device_value_history
    ADD CONSTRAINT fk_fingerprint_device_value_history_hash FOREIGN KEY (id) REFERENCES fingerprint_hash_history (id);
ALTER TABLE fingerprint_collection_history
    ADD CONSTRAINT fk_fingerprint_collection_history_user FOREIGN KEY (user_id) REFERENCES fingerprint_user (id);
ALTER TABLE fingerprint_hash_history
    ADD CONSTRAINT fk_fingerprint_hash_history_server FOREIGN KEY (fingerprint_server_id) REFERENCES fingerprint_server (id);

-- END CONSTRAINTS
