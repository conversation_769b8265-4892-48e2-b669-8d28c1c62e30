/*
========================================================================
Version     : 5.33.1
Type        : FULL DATA
DB          : MSSQL
========================================================================
*/

-- The ID for this entry must be hardcoded to -2
DELETE FROM custom_list WHERE id = -2;
INSERT INTO custom_list (id, name, description, creation_date, update_date)
VALUES (-2, 'IPs bloqueadas', 'Lista de ips bloqueadas', GETDATE(), GETDATE());

INSERT INTO auth_role (id, name) VALUES (NEXT VALUE FOR auth_role_id_seq, 'ADMINISTRADOR');
INSERT INTO auth_role (id, name) VALUES (NEXT VALUE FOR auth_role_id_seq, 'OPERADOR');
INSERT INTO auth_role (id, name) VALUES (NEXT VALUE FOR auth_role_id_seq, 'AUDITOR');
INSERT INTO auth_role (id, name) VALUES (NEXT VALUE FOR auth_role_id_seq, 'CONFIG');
INSERT INTO auth_role (id, name) VALUES (NEXT VALUE FOR auth_role_id_seq, 'CASEMANAGEMENT');

INSERT INTO auth_fa_user (id, email, enabled, first_name, force_reset_password, last_name, password, token_expired, user_name, ldap_user, role_changed, app_user)
VALUES (NEXT VALUE FOR auth_fa_user_id_seq, '<EMAIL>', 1, 'Administrador', 0, 'VUSecurity', '$2a$11$wL3LI50Jw/p4Mkqgz9Le3e1bgZ0mC6lApkoSQUa2dP7.d8MbRDw56', 0, 'faadmin', 0, 0, 0);

INSERT INTO auth_user_role(user_id, role_id) VALUES (1, 1);

INSERT INTO business_group (id, name, supervisor_user_id)
VALUES (NEXT VALUE FOR business_group_id_seq, 'Administrador', (SELECT id FROM auth_fa_user WHERE user_name = 'faadmin'));

INSERT INTO user_business_group (id, business_group_id, user_id)
VALUES (NEXT VALUE FOR user_business_group_id_seq, 1, (SELECT id FROM auth_fa_user WHERE user_name = 'faadmin'));

INSERT INTO rule_update (id, rule_engine_last_update, rule_cep_last_update, rule_parameter_business, rule_classifier_last_update)
VALUES (NEXT VALUE FOR rule_update_id_seq, '2003-10-09 00:00:00.000000 +00:00', '2003-10-09 00:00:00.000000 +00:00', '2003-10-09 00:00:00.000000 +00:00', '2003-10-09 00:00:00.000000 +00:00');

INSERT INTO case_status (id, description) VALUES (NEXT VALUE FOR case_status_id_seq, 'PENDING');
INSERT INTO case_status (id, description) VALUES (NEXT VALUE FOR case_status_id_seq, 'LEGIT');
INSERT INTO case_status (id, description) VALUES (NEXT VALUE FOR case_status_id_seq, 'FRAUD');
INSERT INTO case_status (id, description) VALUES (NEXT VALUE FOR case_status_id_seq, 'INPROGRESS');
INSERT INTO case_status (id, description) VALUES (NEXT VALUE FOR case_status_id_seq, 'ASSIGNED');
INSERT INTO case_status (id, description) VALUES (NEXT VALUE FOR case_status_id_seq, 'NONE');
INSERT INTO case_status (id, description) VALUES (NEXT VALUE FOR case_status_id_seq, 'REJECTED');

INSERT INTO channel (name, description, enabled, id, ignore_rules) VALUES ('Fingerprint', 'Fingerprint', 1, -1, 0);

INSERT INTO channel (name, description, enabled, id, ignore_rules)
VALUES ('CLASSIFIER_RULE_DEFAULT_CHANNEL', 'CLASSIFIER_RULE_DEFAULT_CHANNEL', 1, -2, 0);

INSERT INTO operation_type (name, description, enabled, id, event_type_id)
VALUES ('BrowserFingerprint V2', 'browser fingerprint', 1, -1, 1);
INSERT INTO operation_type (name, description, enabled, id, event_type_id)
VALUES ('DeviceFingerprint', 'Device Fingerprint', 1, -2, 1);
INSERT INTO operation_type (name, description, enabled, id, event_type_id)
VALUES ('CLASSIFIER_RULE_DEFAULT_OPERATION_TYPE', 'CLASSIFIER_RULE_DEFAULT_OPERATION_TYPE', 1, -3, 1);
INSERT INTO operation_type (name, description, enabled, id, event_type_id)
VALUES (N'BrowserFingerprint V1', N'BrowserFingerprint V1', 1, -4, 1)

INSERT INTO file_download (id, path_file, enabled)
VALUES (NEXT VALUE FOR file_download_id_seq, 'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_anonymous.netset', 1);
INSERT INTO file_download (id, path_file, enabled)
VALUES (NEXT VALUE FOR file_download_id_seq, 'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_level1.netset', 1);
INSERT INTO file_download (id, path_file, enabled)
VALUES (NEXT VALUE FOR file_download_id_seq, 'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_level3.netset', 1);

INSERT INTO notification_type(id, name, endpoint, enabled)
VALUES (NEXT VALUE FOR notification_type_id_seq, 'vucomm mail', NULL, 0);
INSERT INTO notification_type (id, name, endpoint, enabled)
VALUES (NEXT VALUE FOR notification_type_id_seq, 'vucomm sms', NULL, 0);

BEGIN TRANSACTION -- Using transactions speeds up insertions.

INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'idCanal', 0, 'idChannel', 1, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'idTipoOperacion', 0, 'idOperationType', 1, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'idUsuario', 3, 'idUser', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'fechaHora', 4, 'date', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'cuentaDebito', 3, 'debitAccount', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'cuentaCredito', 3, 'creditAccount', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'monto', 2, 'amount', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'cantTransacciones', 0, NULL, 2, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'Fingerprint', 3, 'hash', 2, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'hora', 7, NULL, 1, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'Language', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'ColorDepth', 0, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'ScreenHeight', 0, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'ScreenWidth', 0, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'AvailableHeight', 0, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'AvailableWidth', 0, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'TimezoneOffset', 0, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasSessionStorage', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasLocalStorage', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasIndexedDb', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasAddedBehavior', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasOpenDatabase', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'NavigatorCpuClass', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'NavigatorPlatform', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'DoNotTrack', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'Plugins', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasAdBlock', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasLiedLanguages', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasLiedResolution', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'HasLiedOS', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'Fonts', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'CRCCanvas', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'CRCWebGL', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'BrowserMajorVersion', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'BrowserVersion', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'DeviceType', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'DeviceModel', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'DeviceVendor', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'EngineName', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 3, 0, 'EngineVersion', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'Calcular red', 8, NULL, 2, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'Score red', 2, NULL, 2, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 1, 0, 'kItems perfil', 0, NULL, 2, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'DeviceName', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'DeviceManufacturer', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'DeviceResolution', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'IsFingerprintCompatible', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'IsGPSCompatible', 5, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'OsName', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'OsVersion', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'ApiLevel', 0, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 4, 0, 'RandomSeed', 3, '', 3, 0, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 2, 0, 'idCanal', 0, 'idChannel', 1, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 2, 0, 'idTipoOperacion', 0, 'idOperationType', 1, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 2, 0, 'idUsuario', 3, 'idUser', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 2, 0, 'Geolocalizacion', 10, '', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 2, 0, 'DeviceKnown', 5, '', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 2, 0, 'FingerprintResult', 0, '', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 2, 0, 'fechaHora', 4, 'date', 0, NULL, 1);
INSERT INTO parameter (id, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id, enabled)
VALUES (NEXT VALUE FOR parameter_id_seq, 2, 0, 'hora', 7, NULL, 1, NULL, 1);

COMMIT TRANSACTION

-- Fingerprint parameters
INSERT INTO parameter (id, enabled, event_type_id, user_generated, name, parameter_type_id, column_name, visibility, parameter_info_id)
VALUES (-1, 1, 5, 0, 'Audio', 2, '', 3, 0),
       (-2, 1, 5, 0, 'CookiesEnabled', 5, '', 3, 0),
       (-3, 1, 5, 0, 'CRCCanvasGeometry', 3, '', 3, 0),
       (-4, 1, 5, 0, 'CRCCanvasText', 3, '', 3, 0),
       (-5, 1, 5, 0, 'CRCCanvasWebGL', 3, '', 3, 0),
       (-6, 1, 5, 0, 'DOMBlocker', 3, '', 3, 0),
       (-7, 1, 5, 0, 'ExtendedFonts', 3, '', 3, 0),
       (-8, 1, 5, 0, 'Fonts', 3, '', 3, 0),
       (-9, 1, 5, 0, 'FontsPreferences', 3, '', 3, 0),
       (-10, 1, 5, 0, 'GPU', 3, '', 3, 0),
       (-11, 1, 5, 0, 'Memory', 2, '', 3, 0),
       (-12, 1, 5, 0, 'Processors', 0, '', 3, 0),
       (-13, 1, 5, 0, 'SessionStorage', 5, '', 3, 0),
       (-14, 1, 5, 0, 'LocalStorage', 5, '', 3, 0),
       (-15, 1, 5, 0, 'IndexedDatabase', 5, '', 3, 0),
       (-16, 1, 5, 0, 'OpenDatabase', 5, '', 3, 0),
       (-17, 1, 5, 0, 'Language', 3, '', 3, 0),
       (-18, 1, 5, 0, 'Maths', 3, '', 3, 0),
       (-19, 1, 5, 0, 'MediaColorGamut', 3, '', 3, 0),
       (-20, 1, 5, 0, 'MediaContrast', 0, '', 3, 0),
       (-21, 1, 5, 0, 'MediaForcedColors', 5, '', 3, 0),
       (-22, 1, 5, 0, 'MediaHDR', 5, '', 3, 0),
       (-23, 1, 5, 0, 'MediaInvertedColors', 3, '', 3, 0),
       (-24, 1, 5, 0, 'MediaMonochrome', 0, '', 3, 0),
       (-25, 1, 5, 0, 'MediaReducedMotion', 5, '', 3, 0),
       (-26, 1, 5, 0, 'Platform', 3, '', 3, 0),
       (-27, 1, 5, 0, 'ColorDepth', 0, '', 3, 0),
       (-28, 1, 5, 0, 'Vendor', 3, '', 3, 0),
       (-29, 1, 5, 0, 'VendorFlavors', 3, '', 3, 0),
       (-30, 1, 5, 0, 'Timezone', 3, '', 3, 0),
       (-31, 1, 5, 0, 'TouchSupport', 3, '', 3, 0),
       (-32, 1, 5, 0, 'TLS', 3, '', 3, 0),
       (-33, 1, 5, 0, 'HEADER', 3, '', 3, 0),
       (-34, 1, 5, 0, 'HTTP2', 3, '', 3, 0),
       (-35, 1, 5, 0, 'TLSSSLGreased', 3, '', 3, 0),
       (-36, 1, 5, 0, 'BrowserMajor', 0, '', 3, 0),
       (-37, 1, 5, 0, 'BrowserVersion', 6, '', 3, 0),
       (-38, 1, 5, 0, 'DeviceType', 3, '', 3, 0),
       (-39, 1, 5, 0, 'DeviceModel', 3, '', 3, 0),
       (-40, 1, 5, 0, 'DeviceVendor', 3, '', 3, 0),
       (-41, 1, 5, 0, 'EngineName', 3, '', 3, 0),
       (-42, 1, 5, 0, 'EngineVersion', 6, '', 3, 0),
       (-43, 1, 5, 0, 'OSVersion', 6, '', 3, 0),
       (-44, 1, 5, 0, 'LocalstorageId', 3, '', 3, 0),
       (-45, 1, 5, 0, 'Cookie', 3, '', 3, 0),
       (-47, 1, 5, 0, 'IP', 3, '', 3, 0);
INSERT INTO parameter (id, column_name, enabled, event_type_id, name, parameter_info_id, parameter_type_id, user_generated, visibility)
VALUES (-48, '', 1, 3, N'RandomSeed', 0, 3, 0, 3);
INSERT INTO parameter (id, column_name, enabled, event_type_id, name, parameter_info_id, parameter_type_id, user_generated, visibility)
VALUES (-49, '', 1, 3, N'OSName', 0, 3, 0, 3);
INSERT INTO parameter (id, column_name, enabled, event_type_id, name, parameter_info_id, parameter_type_id, user_generated, visibility)
VALUES (-50, '', 1, 3, N'OSVersion', 0, 3, 0, 3);


BEGIN TRANSACTION -- Using transactions speeds up insertions.

-- ========================================
-- General Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'vuserverurl', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USE_CASEMANAGEMENT_MASK', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USE_CASEMANAGEMENT_FILE_MASK', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SAVE_GEO_IP_DATA', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLASSIFIERS_ENABLED', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'MULTITENANT_MODE', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'VALIDATE_OPERATION_TYPE', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LICENSE', '31XL9C-H2YZV8-6T6YO9-PE1');

-- ========================================
-- SMTP Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USE_SMTP', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SMTP_HOST', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SMTP_PROTOCOL', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SMTP_USERNAME', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SMTP_FROM', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SMTP_PASSWORD', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SMTP_PORT', '587');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SMTP_USE_TLS', 'false');

-- ========================================
-- LDAP Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_AUTHENTICATION', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_SERVER', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_PORT', '389');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_SSL', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_BASE_DN', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_USER_DN', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_PASS_USER', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_USERNAME_ATTRIBUTE', 'sAMAccountname');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_MULTIPLE_BASE_DN', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_BASE_USER_DN', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_OBJECT_CLASS', 'user');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_OBJECT_CATEGORY', 'person');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LDAP_AD_DOMAIN', '');

-- ========================================
-- Geo Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'GEO_ATM_PARAMETER_NAME', 'Codigo de atm');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'GEO_SERVICE_NAME', 'GOOGLE');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'GEO_SERVICE_APIKEY', 'bbd6edb96ca202683e40f17f18f4abc73a98db96e0b1090f6929877728336c42');

-- ========================================
-- IP Sync Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'IP_SYNC_ENABLED', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'IP_SYNC_HOUR', '10');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'IP_SYNC_MINUTES', '15');

-- ========================================
-- Refresh View Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'REFRESH_VIEW_ENABLED', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'REFRESH_VIEW_HOUR', '3');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'REFRESH_VIEW_MINUTES', '30');

-- ========================================
-- Fingerprint Configuration
-- ========================================
INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FP_VALID_IF_USER_NOT_EXISTS', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FP_USE_TOKEN', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'OLD_FINGERPRINT_DAYS', '90');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'ENABLED_FINGERPRINT_BATCH', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLEAN_INVALID_FINGERPRINTS', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLEAN_VERIFIED_FINGERPRINTS', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLEAN_UNVERIFIED_FINGERPRINTS', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLEAN_FPS_HOUR', '3');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLEAN_FPS_MINUTES', '30');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FINGERPRINT_BROWSER_MAX_VERIFIED_THRESHOLD', '10');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FINGERPRINT_DEVICE_MAX_VERIFIED_THRESHOLD', '10');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FINGERPRINT_AUTOMATIC_CLEANUP_ENABLED', 'true');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLEAN_OLD_FINGERPRINTS_PARAM_DELETE', '0');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLEAN_OLD_FINGERPRINTS_ACTIVATE', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FINGERPRINT_AUTOMATIC_CLEANUP_DAYS', '7');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USE_V5_RETURN_CODES', 'true');

-- ========================================
-- Bulk Transaction Load Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'BULK_TRANSACTION_LOAD_BATCH_ENABLED', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'BULK_TRANSACTION_LOAD_BATCH_HOUR', '12');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'BULK_TRANSACTION_LOAD_BATCH_MINUTES', '45');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'BULK_TRANSACTION_LOAD_BATCH_ENABLED_RULE_EXECUTION', 'false');

-- ========================================
-- User Transaction Summary Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USER_TRANSACTION_SUMMARY_ENABLED_BATCH', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USER_TRANSACTION_SUMMARY_MONTH', '0');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USER_TRANSACTION_SUMMARY_DAYOFMONTH', '0');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USER_TRANSACTION_SUMMARY_HOUR', '0');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USER_TRANSACTION_SUMMARY_MINUTES', '0');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USER_TRANSACTION_SUMMARY_DAYS', '0');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USER_TRANSACTION_SUMMARY_LATEST_PROCESS', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'USER_TRANSACTION_SUMMARY_RUNNING', 'false');

-- ========================================
-- Lists Load Batch Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LISTS_LOAD_BATCH_ENABLED', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LISTS_LOAD_BATCH_HOUR', '12');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LISTS_LOAD_BATCH_MINUTES', '45');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'LISTS_LOAD_BATCH_IMPORT_TYPE', '1');

-- ========================================
-- Miscellaneous Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'UPLOAD_ATTACHMENTS_LOCATION', '/opt/tomcat/attachments');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'REPORT_MAX_TRANSACTIONS', '500000');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'SPECIALTY_PARAMETER_NAME', 'idSpecialty');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FINGERPRINT_COLLECTION_THRESHOLD', '85');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FP_TOKEN_VALID_TIME', '8');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'NOTIFICATIONS_MESSAGE_LANGUAGE', 'en');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'DISABLE_CLASSIC_AUTH', '0');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'API_MULTITENANT_TRANSIT_KEY', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'API_MULTITENANT_API_KEY', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'API_MULTITENANT_URL', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FRAUD_URL', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'JWT_SIGNING_KEY', 'MTIzNDU2Nzg5MGFiY2RlZjEyMzQ1Njc4OTBhYmNkZWY=');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'JWT_EXPIRING_IN_SECONDS', '3600');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'BULK_TRANSACTION_LOAD_BATCH_PATH', '');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'ENABLED_TWO_FA', 'false');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'TWO_FA_EXPIRING_IN_SECONDS', '60');

-- ========================================
-- Fiscal Year Configuration
-- ========================================

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FISCAL_YEAR_START', '2023-01-01');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FISCAL_YEAR_END', '2024-01-01');

INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'FISCAL_YEAR_ENABLED', 'false');

-- ========================================
-- CEP Rule Engine
-- ========================================


INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CLEAN_OLD_FINGERPRINTS_DAY_OF_WEEK', '7');
INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CEP_ENABLED', 'false');
INSERT INTO system_parameter (id, parameter_name, config_value)
VALUES (NEXT VALUE FOR system_parameter_id_seq, 'CEP_RULE_ENGINE', '');

COMMIT TRANSACTION


BEGIN TRANSACTION -- Using transactions speeds up insertions.

INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'afghanistan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'albania');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'algeria');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'american-samoa');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'andorra');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'angola');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'anguilla');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'antigua-and-barbuda');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'argentina');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'armenia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'aruba');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'australia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'austria');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'azerbaijan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bahamas');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bahrain');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bangladesh');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'barbados');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'belarus');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'belgium');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'belize');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'benin');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bermuda');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bhutan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bolivia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bonaire');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bosnia-and-herzegovina');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'botswana');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'brazil');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'brunei');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'bulgaria');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'burkina-faso');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'burundi');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'cambodia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'cameroon');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'canada');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'cape-verde');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'cayman-islands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'central-african-republic');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'chad');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'chile');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'china');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'colombia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'comoros');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'congo');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'congo-democratic-republic');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'cook-islands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'costa-rica');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'cote-divoire');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'croatia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'cuba');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'curacao');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'cyprus');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'czech-republic');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'denmark');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'djibouti');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'dominica');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'dominican-republic');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'ecuador');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'egypt');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'el-salvador');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'equatorial-guinea');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'eritrea');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'estonia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'ethiopia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'falkland-islands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'faroe-islands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'fiji');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'finland');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'france');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'french-guiana');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'french-polynesia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'gabon');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'gambia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'georgia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'germany');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'ghana');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'gibraltar');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'greece');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'greenland');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'grenada');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'guadeloupe');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'guam');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'guatemala');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'guernsey');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'guinea');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'guinea-bissau');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'guyana');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'haiti');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'honduras');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'hong-kong');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'hungary');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'iceland');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'india');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'indonesia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'iran');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'iraq');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'ireland');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'isle-of-man');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'israel');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'italy');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'jamaica');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'japan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'jersey');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'jordan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'kazakhstan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'kenya');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'kiribati');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'kosovo');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'kuwait');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'kyrgyzstan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'laos');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'latvia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'lebanon');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'lesotho');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'liberia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'libya');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'liechtenstein');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'lithuania');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'luxembourg');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'macao');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'macedonia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'madagascar');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'malawi');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'malaysia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'maldives');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'mali');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'malta');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'marshall-islands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'martinique');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'mauritania');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'mauritius');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'mayotte');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'mexico');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'moldova');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'monaco');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'mongolia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'montenegro');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'montserrat');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'morocco');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'mozambique');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'myanmar');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'namibia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'nepal');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'netherlands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'new-caledonia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'new-zealand');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'nicaragua');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'niger');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'nigeria');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'north-korea');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'norway');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'oman');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'pakistan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'palestine');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'panama');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'papua-new-guinea');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'paraguay');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'peru');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'philippines');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'poland');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'portugal');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'puerto-rico');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'qatar');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'reunion');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'romania');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'russia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'rwanda');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'saint-helena');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'saint-kitts-and-nevis');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'saint-lucia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'saint-pierre-and-miquelon');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'samoa');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'san-marino');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'sao-tome-and-principe');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'saudi-arabia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'senegal');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'serbia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'seychelles');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'sierra-leone');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'singapore');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'sint-maarten');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'slovakia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'slovenia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'solomon-islands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'somalia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'south-africa');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'south-korea');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'south-sudan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'spain');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'sri-lanka');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'st-vincent-and-grenadines');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'sudan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'suriname');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'swaziland');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'sweden');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'switzerland');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'syria');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'taiwan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'tajikistan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'tanzania');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'thailand');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'timor-leste');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'togo');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'tonga');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'trinidad-and-tobago');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'tunisia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'turkey');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'turkmenistan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'turks-and-caicos-islands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'tuvalu');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'uganda');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'ukraine');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'united-arab-emirates');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'united-kingdom');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'united-states');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'uruguay');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'uzbekistan');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'vanuatu');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'vatican-city');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'venezuela');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'vietnam');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'virgin-islands-uk');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'virgin-islands-us');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'wallis-and-futuna-islands');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'yemen');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'zambia');
INSERT INTO country (id, name) VALUES (NEXT VALUE FOR country_id_seq, 'zimbabwe');

COMMIT TRANSACTION

BEGIN TRANSACTION -- Using transactions speeds up insertions.

INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADMINISTRATION', NULL, 'menu.titleAdministration');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DATA_CONFIG', 1, 'channel.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'CHANNEL_MANAGEMENT', 2, 'channel.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_CHANNEL', 3, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_CHANNEL', 3, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_CHANNEL', 3, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_CHANNEL', 3, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'OPERATION_TYPE_MANAGEMENT', 2, 'operations.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_OPERATION_TYPE', 8, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_OPERATION_TYPE', 8, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_OPERATION_TYPE', 8, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_OPERATION_TYPE', 8, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'PARAMETER_TYPE_MANAGEMENT', 2, 'wsParameters.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_PARAMETER_TYPE', 13, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_PARAMETER_TYPE', 13, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_PARAMETER_TYPE', 13, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_PARAMETER_TYPE', 13, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'LISTS', 1, 'customLists.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'LIST_MANAGEMENT', 18, 'customLists.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_LIST', 19, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_LIST', 19, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_LIST', 19, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_LIST', 19, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'LIST_MASIVE_LOAD', 18, 'customListUpload.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'PROCESS_LIST', 24, 'general.button.process');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'LIST_ATM_LOAD', 18, 'atmListUpload.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ATM_ADD_LIST', 26, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ATM_PROCESS_LIST', 26, 'general.button.process');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'PARAMETER_BY_BUSINESS', 18, 'parametersByBusiness.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'PARAMETER_BY_BUSINESS_SAVE', 29, 'general.save');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'SYSTEM_PARAMETERS_CONFIG', 1, 'systemAdmin.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'USER_ROLES', 1, 'admin.roleTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_USER', 32, 'account.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ROLES_MANAGEMENT', 32, 'admin.roles');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_ROLE', 34, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_ROLE', 34, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_ROLE', 34, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_ROLE', 34, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'USER_MANAGEMENT', 32, 'admin.roleSubTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_USERS', 39, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_USER', 39, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_USER', 39, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'APIKEY_MANAGEMENT', 1, 'apikey.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'NEW_APIKEY', 43, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_APIKEY', 43, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'FRAUD', NULL, 'menu.title.fraud');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'RULES_MANAGEMENT', 46, 'fraudRule.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'COMMON_RULES_MANAGEMENT', 47, 'fraudRule.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_COMMON_RULE', 48, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_COMMON_RULE', 48, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_COMMON_RULE', 48, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_COMMON_RULE', 48, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'RISK_RULES_MANAGEMENT', 47, 'fraudRule.subTitleRisk');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_RISK_RULE', 53, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_RISK_RULE', 53, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_RISK_RULE', 53, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_RISK_RULE', 53, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'CEP_RULES_MANAGEMENT', 47, 'fraudRule.subTitleCep');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_CEP_RULE', 58, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_CEP_RULE', 58, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_CEP_RULE', 58, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_CEP_RULE', 58, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'CROSS_RULE_MANAGEMENT', 47, 'fraudRule.subTitleCross');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_CROSS_RULE', 63, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_CROSS_RULE', 63, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_CROSS_RULE', 63, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_CROSS_RULE', 63, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'HISTORY', 46, 'batchLoad.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'MASSIVE_TRANSACTION_LOAD', 68, 'batchLoad.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'MASSIVE_EVENT_LOAD', 68, 'batchEventLoad.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'CASE_MANAGEMENT', 46, 'caseManagement.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_CASE_MANAGEMENT', 71, 'caseManagement.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_ALL_CASES', 71, 'viewAllCases.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_PROCESSED_CASE', 71, 'processedCases.editStatus');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'CUSTOMIZE_CASE_MANAGEMENT', 71, 'caseManagement.customizeColumns');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DOWNLOAD_TABLE_CASE_MANAGEMENT', 71, 'general.download');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'FINGERPRINT', NULL, 'menu.title.fingerprint');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'FINGERPRINT_RULES', 77, 'fingerprintDeviceRules.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADMIN_BROWSER_FP', 78, 'fingerprintRules.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_RULE_BROWSER_FP', 79, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_RULE_BROWSER_FP', 79, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_RULE_BROWSER_FP', 79, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADMIN_DEVICE_FP', 78, 'fingerprintDeviceRules.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_RULE_DEVICE_FP', 83, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_RULE_DEVICE_FP', 83, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_RULE_DEVICE_FP', 83, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'FINGERPRINT_QUERY', 77, 'fingerprintQuery.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DASHBOARD_FP', 87, 'fingerprintQuery.subTitle1');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'QUERY_FP', 87, 'fingerprintQuery.subTitle2');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'REPORTS', NULL, 'menu.titleReport');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'STATIC_REPORTS', 90, 'staticReports.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'USERS_AND_PROFILES', 90, 'profile.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'PROFILES', 92, 'profile.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'USER_COUNT', 92, 'dashboard.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DYNAMIC_REPORTS', 90, 'reports.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_DYNAMIC_REPORTS', 95, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_DYNAMIC_REPORTS', 95, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_DYNAMIC_REPORTS', 95, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'RENDER_DYNAMIC_REPORTS', 95, 'general.button.render');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'NOTIFICATIONS', NULL, 'menu.titleNotification');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'NOTIFICATION_TYPES', 100, 'notificationType.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'BUSINESS_GROUP', 100, 'businessGroup.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'BUSINESS_GROUP_USERS', 102, 'menu.subTitleBusinessGroup');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'BUSINESS_GROUP_CONFIG', 102, 'menu.subTitleNotificationsConfig');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'NOTIFICATION_RULES', 100, 'notificationRules.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'BUSINESS_GROUP_AUTO_ASSIGN', 100, 'autoAssignBusiness.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'MACHINE_LEARNING', NULL, 'menu.title.machineLearning');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_TRANSACTIONS', 107, 'machineLearning.transaction.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_COPY_DELETE', 108, 'machineLearning.copyAndDelete.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_FEATURES', 108, 'machineLearning.Feature.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_NEURAL_NETWORK', 107, 'machineLearning.networks.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_MANAGEMENT', 111, 'machineLearning.networks.manage.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_PROFILES', 111, 'machineLearning.networks.profiles.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_BATCH_PROCESS', 111, 'machineLearning.networks.batchs.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_EXPERIMENT', 107, 'machineLearning.experiments.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_NEW_EXPERIMENT', 115, 'machineLearning.experiments.new.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ML_EXPERIMENT_RESULT', 115, 'machineLearning.experiments.results.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'TRACEABILITY', NULL, 'menu.title.traceability');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_TRACE', 118, 'machineLearning.traceability.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'FISCAL_YEAR_PARAMETERS', 1, 'fiscalYear.title');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'CLASSIFIERS', 46, 'fraudRule.titleClassifiers');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'CLASSIFIERS_MANAGEMENT', 121, 'fraudRule.subTitleClassifiers');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_CLASSIFIERS_RULE', 122, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_CLASSIFIERS_RULE', 122, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_CLASSIFIERS_RULE', 122, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_CLASSIFIERS_RULE', 122, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ACCUMULATORS', 46, 'fraudRule.titleAccumulators');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ACCUMULATORS_MANAGEMENT', 127, 'fraudRule.subTitleAccumulators');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ADD_ACCUMULATORS_RULE', 128, 'general.button.new');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'VIEW_ACCUMULATORS_RULE', 128, 'general.button.view');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'EDIT_ACCUMULATORS_RULE', 128, 'general.button.edit');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'DELETE_ACCUMULATORS_RULE', 128, 'general.button.delete');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ACTIVE_DIRECTORY', NULL, 'menu.title.ldap');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ACTIVE_DIRECTORY_CONFIG', 133, 'ldap.connection.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ACTIVE_DIRECTORY_GROUPS', 133, 'ldap.grupo.subTitle');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'ASSIGN_CASES', 71, 'assignCases.editStatus');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'REJECT_PROCESSED_CASE', 71, 'processedCases.rejectedStatus');
INSERT INTO auth_privilege (id, name, parent_id, description_key)
VALUES (NEXT VALUE FOR auth_privilege_id_seq, 'CHANGE_PROCESSED_CASE', 71, 'processedCases.changedStatus');


COMMIT TRANSACTION

BEGIN TRANSACTION -- Using transactions speeds up insertions.

INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 46);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 47);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 63);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 65);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 66);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 67);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 68);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 69);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 70);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 71);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 72);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (5, 73);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 1);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 2);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 3);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 4);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 5);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 6);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 7);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 8);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 9);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 10);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 11);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 12);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 13);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 14);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 15);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 16);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 17);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 18);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 19);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 20);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 21);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 22);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 23);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 24);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 25);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 26);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 27);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 28);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 29);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 30);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 31);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 32);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 33);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 34);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 35);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 36);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 37);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 38);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 39);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 40);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 41);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 42);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 43);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 44);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 45);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 46);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 47);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 48);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 49);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 50);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 51);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 52);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 53);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 54);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 55);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 56);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 57);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 58);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 59);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 60);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 61);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 62);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 63);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 64);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 65);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 66);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 67);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 68);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 69);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 70);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 71);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 72);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 73);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 74);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 75);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 76);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 77);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 78);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 79);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 80);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 81);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 82);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 83);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 84);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 85);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 86);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 87);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 88);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 89);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 90);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 91);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 92);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 93);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 94);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 95);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 96);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 97);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 98);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 99);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 100);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 101);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 102);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 103);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 104);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 105);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 106);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 107);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 108);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 109);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 110);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 111);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 112);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 113);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 114);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 115);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 116);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 117);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 118);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 119);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 120);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 121);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 122);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 123);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 124);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 125);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 126);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 127);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 128);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 129);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 130);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 131);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 132);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 133);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 134);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 135);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 136);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 137);
INSERT INTO auth_role_privilege (role_id, privilege_id) VALUES (1, 138);

INSERT INTO ml_feature (id, name, cyclic)
VALUES (NEXT VALUE FOR ml_feature_id_seq, 'Hora del dia', 1),
       (NEXT VALUE FOR ml_feature_id_seq, 'Dia del mes', 1),
       (NEXT VALUE FOR ml_feature_id_seq, 'Dia de la semana', 1),
       (NEXT VALUE FOR ml_feature_id_seq, 'Dia del año', 1),
       (NEXT VALUE FOR ml_feature_id_seq, 'Importe sobre Promedio', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de sigmas del importe', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de dias al mismo destino', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de transacciones al mismo destino', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de dias a la misma IP', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de transacciones a la misma IP', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de dias a la última', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de transacciones', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Importe sobre Total', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de cuentas distintias desde la misma IP', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de transacciones del mismo mes', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Suma del los importes del mismo mes', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de dias a la misma IP Red', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Cantidad de transacciones ultimas 24hs', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Importe', 0),
       (NEXT VALUE FOR ml_feature_id_seq, 'Dia del mes modulo 15', 1);

COMMIT TRANSACTION