/*
========================================================================
Version     : 5.33.1
Type        : FULL SCHEMA
DB          : MSSQL
========================================================================
*/

-- SEQUENCES
CREATE SEQUENCE accumulator_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE accumulator_parameter_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE accumulator_rule_condition_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE additional_parameter_event_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE additional_parameter_transaction_id_seq AS BIGINT START WITH 1 INCREMENT BY 100;
CREATE SEQUENCE api_key_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE assigned_case_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE audit_log_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE auth_fa_user_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE auth_ldap_role_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE auth_password_reset_token_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE auth_privilege_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE auth_role_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE auth_verification_token_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE background_batch_status_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE blocked_ip_values_id_seq AS INT START WITH 1 INCREMENT BY 100;
CREATE SEQUENCE business_group_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE case_additional_info_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE case_attachment_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE case_business_auto_assign_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE case_display_field_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE case_status_id_seq AS SMALLINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE channel_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE classifier_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE classifier_condition_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE country_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE custom_list_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE custom_list_value_id_seq AS INT START WITH 1 INCREMENT BY 100;
CREATE SEQUENCE event_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE event_with_error_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE file_download_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE file_entry_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE fingerprint_collection_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE fingerprint_hash_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE fingerprint_response_id_seq AS BIGINT START WITH 1 INCREMENT BY 100;
CREATE SEQUENCE fingerprint_server_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE fingerprint_unverified_value_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE fingerprint_user_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE fingerprint_user_hash_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE geo_atm_data_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE profile_geo_data_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_geo_profile_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE geodata_error_parameter_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE history_score_id_seq AS INT START WITH 1 INCREMENT BY 100;
CREATE SEQUENCE last_assigned_case_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE ml_batch_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE ml_experiment_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE ml_feature_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE ml_network_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE ml_transaction_result_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE notification_config_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE notification_type_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE operation_type_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE parameter_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE parameter_business_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE parameter_channel_operation_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE parameter_query_channel_operation_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE profile_by_operation_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE profile_day_counter_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE profile_period_info_by_credit_account_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE profile_period_information_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE report_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE report_aggregate_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE report_display_field_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE report_filter_channel_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE report_filter_operation_type_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE report_filter_rule_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE report_group_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE rule_auto_assign_case_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE rule_business_group_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE rule_condition_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE rule_event_statement_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE rule_item_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE rule_matched_event_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE rule_update_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE swift_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE system_parameter_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE transaction_entity_id_seq AS BIGINT START WITH 1 INCREMENT BY 100;
CREATE SEQUENCE transaction_entity_import_id_seq AS BIGINT START WITH 1 INCREMENT BY 100;
CREATE SEQUENCE transaction_exception_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_business_daily_stat_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_business_group_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_business_monthly_stat_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_business_stat_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_business_weekly_stat_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_transaction_daily_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_transaction_monthly_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_transaction_summary_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_transaction_summary_history_id_seq AS INT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE user_transaction_weekly_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE revision_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE event_entity_import_id_seq AS BIGINT START WITH 1 INCREMENT BY 100;
CREATE SEQUENCE event_additional_fields_import_id_seq AS BIGINT START WITH 1 INCREMENT BY 1;
GO
-- END SEQUENCES

-- TABLES
CREATE TABLE accumulator
(
    id            BIGINT NOT NULL,
    activated     datetime2,
    classifier_id INT,
    enabled       BIT,
    name          VARCHAR(255),
    range_end     INT,
    range_start   INT,
    unit_time     CHAR(1),
    PRIMARY KEY (id)
);

CREATE TABLE accumulator_parameter
(
    id             BIGINT NOT NULL,
    accumulator_id BIGINT,
    parameter_id   INT,
    PRIMARY KEY (id)
);

CREATE TABLE accumulator_rule_condition
(
    id                BIGINT NOT NULL,
    accumulator_id    BIGINT,
    rule_condition_id INT,
    PRIMARY KEY (id)
);

CREATE TABLE additional_parameter_event
(
    id       BIGINT NOT NULL,
    field1   VARCHAR(255),
    field10  VARCHAR(255),
    field100 VARCHAR(255),
    field11  VARCHAR(255),
    field12  VARCHAR(255),
    field13  VARCHAR(255),
    field14  VARCHAR(255),
    field15  VARCHAR(255),
    field16  VARCHAR(255),
    field17  VARCHAR(255),
    field18  VARCHAR(255),
    field19  VARCHAR(255),
    field2   VARCHAR(255),
    field20  VARCHAR(255),
    field21  VARCHAR(255),
    field22  VARCHAR(255),
    field23  VARCHAR(255),
    field24  VARCHAR(255),
    field25  VARCHAR(255),
    field26  VARCHAR(255),
    field27  VARCHAR(255),
    field28  VARCHAR(255),
    field29  VARCHAR(255),
    field3   VARCHAR(255),
    field30  VARCHAR(255),
    field31  VARCHAR(255),
    field32  VARCHAR(255),
    field33  VARCHAR(255),
    field34  VARCHAR(255),
    field35  VARCHAR(255),
    field36  VARCHAR(255),
    field37  VARCHAR(255),
    field38  VARCHAR(255),
    field39  VARCHAR(255),
    field4   VARCHAR(255),
    field40  VARCHAR(255),
    field41  VARCHAR(255),
    field42  VARCHAR(255),
    field43  VARCHAR(255),
    field44  VARCHAR(255),
    field45  VARCHAR(255),
    field46  VARCHAR(255),
    field47  VARCHAR(255),
    field48  VARCHAR(255),
    field49  VARCHAR(255),
    field5   VARCHAR(255),
    field50  VARCHAR(255),
    field51  VARCHAR(255),
    field52  VARCHAR(255),
    field53  VARCHAR(255),
    field54  VARCHAR(255),
    field55  VARCHAR(255),
    field56  VARCHAR(255),
    field57  VARCHAR(255),
    field58  VARCHAR(255),
    field59  VARCHAR(255),
    field6   VARCHAR(255),
    field60  VARCHAR(255),
    field61  VARCHAR(255),
    field62  VARCHAR(255),
    field63  VARCHAR(255),
    field64  VARCHAR(255),
    field65  VARCHAR(255),
    field66  VARCHAR(255),
    field67  VARCHAR(255),
    field68  VARCHAR(255),
    field69  VARCHAR(255),
    field7   VARCHAR(255),
    field70  VARCHAR(255),
    field71  VARCHAR(255),
    field72  VARCHAR(255),
    field73  VARCHAR(255),
    field74  VARCHAR(255),
    field75  VARCHAR(255),
    field76  VARCHAR(255),
    field77  VARCHAR(255),
    field78  VARCHAR(255),
    field79  VARCHAR(255),
    field8   VARCHAR(255),
    field80  VARCHAR(255),
    field81  VARCHAR(255),
    field82  VARCHAR(255),
    field83  VARCHAR(255),
    field84  VARCHAR(255),
    field85  VARCHAR(255),
    field86  VARCHAR(255),
    field87  VARCHAR(255),
    field88  VARCHAR(255),
    field89  VARCHAR(255),
    field9   VARCHAR(255),
    field90  VARCHAR(255),
    field91  VARCHAR(255),
    field92  VARCHAR(255),
    field93  VARCHAR(255),
    field94  VARCHAR(255),
    field95  VARCHAR(255),
    field96  VARCHAR(255),
    field97  VARCHAR(255),
    field98  VARCHAR(255),
    field99  VARCHAR(255),
    event_id BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE additional_parameter_transaction
(
    id             BIGINT NOT NULL,
    field1         VARCHAR(255),
    field10        VARCHAR(255),
    field100       VARCHAR(255),
    field11        VARCHAR(255),
    field12        VARCHAR(255),
    field13        VARCHAR(255),
    field14        VARCHAR(255),
    field15        VARCHAR(255),
    field16        VARCHAR(255),
    field17        VARCHAR(255),
    field18        VARCHAR(255),
    field19        VARCHAR(255),
    field2         VARCHAR(255),
    field20        VARCHAR(255),
    field21        VARCHAR(255),
    field22        VARCHAR(255),
    field23        VARCHAR(255),
    field24        VARCHAR(255),
    field25        VARCHAR(255),
    field26        VARCHAR(255),
    field27        VARCHAR(255),
    field28        VARCHAR(255),
    field29        VARCHAR(255),
    field3         VARCHAR(255),
    field30        VARCHAR(255),
    field31        VARCHAR(255),
    field32        VARCHAR(255),
    field33        VARCHAR(255),
    field34        VARCHAR(255),
    field35        VARCHAR(255),
    field36        VARCHAR(255),
    field37        VARCHAR(255),
    field38        VARCHAR(255),
    field39        VARCHAR(255),
    field4         VARCHAR(255),
    field40        VARCHAR(255),
    field41        VARCHAR(255),
    field42        VARCHAR(255),
    field43        VARCHAR(255),
    field44        VARCHAR(255),
    field45        VARCHAR(255),
    field46        VARCHAR(255),
    field47        VARCHAR(255),
    field48        VARCHAR(255),
    field49        VARCHAR(255),
    field5         VARCHAR(255),
    field50        VARCHAR(255),
    field51        VARCHAR(255),
    field52        VARCHAR(255),
    field53        VARCHAR(255),
    field54        VARCHAR(255),
    field55        VARCHAR(255),
    field56        VARCHAR(255),
    field57        VARCHAR(255),
    field58        VARCHAR(255),
    field59        VARCHAR(255),
    field6         VARCHAR(255),
    field60        VARCHAR(255),
    field61        VARCHAR(255),
    field62        VARCHAR(255),
    field63        VARCHAR(255),
    field64        VARCHAR(255),
    field65        VARCHAR(255),
    field66        VARCHAR(255),
    field67        VARCHAR(255),
    field68        VARCHAR(255),
    field69        VARCHAR(255),
    field7         VARCHAR(255),
    field70        VARCHAR(255),
    field71        VARCHAR(255),
    field72        VARCHAR(255),
    field73        VARCHAR(255),
    field74        VARCHAR(255),
    field75        VARCHAR(255),
    field76        VARCHAR(255),
    field77        VARCHAR(255),
    field78        VARCHAR(255),
    field79        VARCHAR(255),
    field8         VARCHAR(255),
    field80        VARCHAR(255),
    field81        VARCHAR(255),
    field82        VARCHAR(255),
    field83        VARCHAR(255),
    field84        VARCHAR(255),
    field85        VARCHAR(255),
    field86        VARCHAR(255),
    field87        VARCHAR(255),
    field88        VARCHAR(255),
    field89        VARCHAR(255),
    field9         VARCHAR(255),
    field90        VARCHAR(255),
    field91        VARCHAR(255),
    field92        VARCHAR(255),
    field93        VARCHAR(255),
    field94        VARCHAR(255),
    field95        VARCHAR(255),
    field96        VARCHAR(255),
    field97        VARCHAR(255),
    field98        VARCHAR(255),
    field99        VARCHAR(255),
    transaction_id BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE api_key
(
    id           INT NOT NULL,
    api_key      VARCHAR(36),
    api_key_name VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE assigned_case
(
    id                BIGINT NOT NULL,
    assigned_by       BIGINT,
    assigned_date     datetime2,
    assigned_to       BIGINT,
    business_group_id INT,
    case_status_id    INT,
    note              nvarchar(max),
    transaction_id    BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE audit_log
(
    id         BIGINT NOT NULL,
    action     VARCHAR(255),
    date_audit datetime2,
    field      nvarchar(max),
    role_name  VARCHAR(255),
    user_name  VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE auth_fa_user
(
    id                   BIGINT NOT NULL,
    email                VARCHAR(255),
    enabled              BIT,
    first_name           VARCHAR(255),
    force_reset_password BIT,
    last_name            VARCHAR(255),
    ldap_user            BIT,
    password             VARCHAR(60),
    role_changed         BIT,
    token_expired        BIT,
    user_name            VARCHAR(255),
    app_user             BIT,
    PRIMARY KEY (id)
);

CREATE TABLE auth_ldap_role
(
    id        BIGINT NOT NULL,
    group_cn  VARCHAR(255),
    role_id   BIGINT,
    role_name VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE auth_password_reset_token
(
    id          BIGINT NOT NULL,
    expiry_date datetime2,
    token       VARCHAR(255),
    user_id     BIGINT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE auth_privilege
(
    id              BIGINT NOT NULL,
    description_key VARCHAR(255),
    name            VARCHAR(255),
    parent_id       BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE auth_role
(
    id   BIGINT NOT NULL,
    name VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE auth_role_privilege
(
    role_id      BIGINT NOT NULL,
    privilege_id BIGINT NOT NULL,
    PRIMARY KEY (role_id, privilege_id)
);

CREATE TABLE auth_session
(
    primary_id            CHAR(36) NOT NULL,
    session_id            CHAR(36) NOT NULL,
    creation_time         BIGINT   NOT NULL,
    last_access_time      BIGINT   NOT NULL,
    max_inactive_interval INT      NOT NULL,
    expiry_time           BIGINT   NOT NULL,
    principal_name        VARCHAR(100),
    CONSTRAINT auth_session_pk PRIMARY KEY (primary_id)
);

CREATE TABLE auth_session_attributes
(
    session_primary_id CHAR(36)     NOT NULL,
    attribute_name     VARCHAR(200) NOT NULL,
    attribute_bytes    IMAGE        NOT NULL,
    CONSTRAINT auth_session_attributes_pk PRIMARY KEY (session_primary_id, attribute_name),
    CONSTRAINT auth_session_attributes_fk FOREIGN KEY (session_primary_id) REFERENCES auth_session (primary_id) ON DELETE CASCADE
);

CREATE TABLE auth_user_role
(
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL
);

CREATE TABLE auth_verification_token
(
    id          BIGINT NOT NULL,
    expiry_date datetime2,
    token       VARCHAR(255),
    user_id     BIGINT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE background_batch_status
(
    id            INT NOT NULL,
    batch_status  INT,
    end_date      datetime2,
    file_name     VARCHAR(255),
    path          VARCHAR(255),
    start_date    datetime2,
    status_detail nvarchar(max),
    PRIMARY KEY (id)
);

CREATE TABLE blocked_ip_values
(
    id       INT NOT NULL,
    file_id  INT,
    ip_value VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE business_group
(
    id                 INT NOT NULL,
    name               VARCHAR(255),
    supervisor_user_id BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE case_additional_info
(
    id             INT NOT NULL,
    justification  nvarchar(max),
    transaction_id BIGINT,
    PRIMARY KEY (id)
);
CREATE TABLE case_attachment
(
    id             INT NOT NULL,
    created_date   datetime2,
    file_size      DOUBLE PRECISION,
    file_name      VARCHAR(255),
    transaction_id BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE case_business_auto_assign
(
    id                INT NOT NULL,
    business_group_id INT,
    channel_id        INT,
    operation_type_id INT,
    PRIMARY KEY (id)
);

CREATE TABLE case_display_field
(
    id         INT NOT NULL,
    field_id   INT,
    user_id    BIGINT,
    event_type INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE case_status
(
    id          SMALLINT NOT NULL,
    Description VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE cep_rule_matched_transaction
(
    transaction_id         BIGINT NOT NULL,
    rule_action_id         INT,
    cep_rule_id            INT,
    rule_name              VARCHAR(255),
    rule_critical_level_id INT,
    PRIMARY KEY (transaction_id)
);

CREATE TABLE channel
(
    id           INT NOT NULL,
    description  VARCHAR(255),
    enabled      BIT,
    ignore_rules BIT,
    name         VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE classifier
(
    id                    INT NOT NULL,
    cep_engine_enable     BIT,
    classifier_as_rule_id INT,
    classifier_order      INT,
    enabled               BIT NOT NULL,
    name                  VARCHAR(255),
    rule_description      VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE classifier_condition
(
    id                  INT NOT NULL,
    classifier_id       INT,
    condition_type_id   INT,
    custom_list_id      INT,
    enabled             BIT NOT NULL,
    logical_operator_id INT,
    operator_id         INT,
    parameter_id        INT,
    value               VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE classifier_rule
(
    classifier_id INT,
    rule_id       INT
);

CREATE TABLE country
(
    id   INT NOT NULL,
    name VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE custom_list
(
    id            INT NOT NULL,
    creation_date datetime2,
    description   VARCHAR(255),
    name          VARCHAR(255),
    update_date   datetime2,
    PRIMARY KEY (id)
);

CREATE TABLE custom_list_value
(
    id             INT NOT NULL,
    custom_list_id INT,
    list_value     VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE event
(
    id           BIGINT NOT NULL,
    action_id    SMALLINT,
    channel_id   INT,
    event_date   datetime2,
    fingerprint  VARCHAR(255),
    operation_id INT,
    user_id      VARCHAR(255),
    geo_data_id  BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE event_with_error
(
    id            INT NOT NULL,
    creation_date datetime2,
    event_data    nvarchar(max),
    exception     nvarchar(MAX),
    PRIMARY KEY (id)
);

CREATE TABLE file_download
(
    id        INT NOT NULL,
    enabled   BIT,
    path_file VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE file_entry
(
    id                  INT NOT NULL,
    additional_field_id INT,
    auto_import         BIT,
    file_name           VARCHAR(255),
    file_path           VARCHAR(255),
    file_size_in_kb     DOUBLE PRECISION,
    file_status         INT,
    status_detail       VARCHAR(255),
    type                INT,
    upload_date         datetime2,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_agent_value
(
    hash_id          BIGINT NOT NULL,
    browser_major    VARCHAR(255),
    browser_name     VARCHAR(255),
    browser_version  VARCHAR(255),
    cpu_architecture VARCHAR(255),
    device_model     VARCHAR(255),
    device_type      VARCHAR(255),
    device_vendor    VARCHAR(255),
    engine_name      VARCHAR(255),
    engine_version   VARCHAR(255),
    os_name          VARCHAR(255),
    os_version       VARCHAR(255),
    ua               VARCHAR(500),
    PRIMARY KEY (hash_id)
);

CREATE TABLE fingerprint_agent_value_history
(
    hash_id          BIGINT NOT NULL,
    browser_major    VARCHAR(255),
    browser_name     VARCHAR(255),
    browser_version  VARCHAR(255),
    cpu_architecture VARCHAR(255),
    device_model     VARCHAR(255),
    device_type      VARCHAR(255),
    device_vendor    VARCHAR(255),
    engine_name      VARCHAR(255),
    engine_version   VARCHAR(255),
    os_name          VARCHAR(255),
    os_version       VARCHAR(255),
    ua               VARCHAR(500),
    created_at datetime2(7) NOT NULL DEFAULT GETDATE(),
    created_by varchar(255) NULL,
    PRIMARY KEY (hash_id)
);

CREATE TABLE fingerprint_collection
(
    id              BIGINT NOT NULL,
    collection_uuid BINARY (255),
    creation_date   datetime2,
    last_check_date datetime2,
    status          INT,
    type            INT,
    user_id         BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_collection_history
(
    id              BIGINT NOT NULL,
    collection_uuid BINARY (255),
    creation_date   datetime2,
    last_check_date datetime2,
    status          INT,
    type            INT,
    user_id         BIGINT,
    created_at datetime2(7) NOT NULL DEFAULT GETDATE(),
    created_by varchar(255) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_device_value
(
    api_level              INT,
    device_id              VARCHAR(255),
    device_manufacturer    VARCHAR(255),
    device_name            VARCHAR(255),
    device_resolution      VARCHAR(255),
    fingerprint_compatible BIT,
    gps_compatible         BIT,
    rooted                 BIT,
    os_name                VARCHAR(255),
    os_version             VARCHAR(255),
    random_seed            VARCHAR(255),
    id                     BIGINT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_device_value_history
(
    api_level              INT,
    device_id              VARCHAR(255),
    device_manufacturer    VARCHAR(255),
    device_name            VARCHAR(255),
    device_resolution      VARCHAR(255),
    fingerprint_compatible BIT,
    gps_compatible         BIT,
    rooted                 BIT,
    os_name                VARCHAR(255),
    os_version             VARCHAR(255),
    random_seed            VARCHAR(255),
    id                     BIGINT NOT NULL,
    created_at datetime2(7) NOT NULL DEFAULT GETDATE(),
    created_by varchar(255) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_hash
(
    id                    BIGINT NOT NULL,
    creation_date         datetime2,
    fp_hash               VARCHAR(255),
    primary_hash          VARCHAR(255),
    secondary_hash        VARCHAR(255),
    fingerprint_server_id BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_hash_history
(
    id                    BIGINT NOT NULL,
    creation_date         datetime2,
    fp_hash               VARCHAR(255),
    primary_hash          VARCHAR(255),
    secondary_hash        VARCHAR(255),
    fingerprint_server_id BIGINT,
    created_at datetime2(7) NOT NULL DEFAULT GETDATE(),
    created_by varchar(255) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_response
(
    id               BIGINT NOT NULL,
    accuracy_percent DOUBLE PRECISION,
    creation_date    datetime2,
    hash_known       BIT,
    result_code      INT,
    user_hash_id     BIGINT,
    user_id          VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_response_history
(
    id               BIGINT NOT NULL,
    accuracy_percent DOUBLE PRECISION,
    creation_date    datetime2,
    hash_known       BIT,
    result_code      INT,
    user_hash_id     BIGINT,
    user_id          VARCHAR(255),
    created_at datetime2(7) NOT NULL DEFAULT GETDATE(),
    created_by varchar(255) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_server
(
    id                          BIGINT           NOT NULL,
    cookie                      VARCHAR(60),
    etag                        VARCHAR(255),
    etag_first_request          VARCHAR(255),
    accuracy_radius             INT,
    city                        VARCHAR(255),
    country                     VARCHAR(255),
    latitude                    DOUBLE PRECISION NOT NULL,
    longitude                   DOUBLE PRECISION NOT NULL,
    subdivisions                VARCHAR(255),
    header_signature            VARCHAR(255),
    headers                     VARCHAR(MAX),
    http2                       VARCHAR(MAX),
    http2_fingerprint_hash      VARCHAR(255),
    ip                          VARCHAR(32),
    timestamp_utc               VARCHAR(255),
    tls_fingerprint             VARCHAR(MAX),
    tls_fingerprint_hash        VARCHAR(255),
    tls_fingerprint_ssl_greased VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_unverified_value
(
    id            BIGINT NOT NULL,
    creation_date datetime2,
    token BINARY (255),
    user_hash_id  BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_unverified_value_history
(
    id            BIGINT NOT NULL,
    creation_date datetime2,
    token BINARY (255),
    user_hash_id  BIGINT,
    created_at datetime2(7) NOT NULL DEFAULT GETDATE(),
    created_by varchar(255) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_user
(
    id      BIGINT NOT NULL,
    user_id VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_user_hash
(
    id                        BIGINT NOT NULL,
    accuracy_percent          DOUBLE PRECISION,
    apikey_id                 INT,
    count_used                INT,
    creation_date             datetime2,
    fingerprint_collection_id BIGINT,
    hash_id                   BIGINT,
    last_check_date           datetime2,
    status                    INT,
    user_id                   BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_user_hash_history
(
    id                        BIGINT NOT NULL,
    accuracy_percent          DOUBLE PRECISION,
    apikey_id                 INT,
    count_used                INT,
    creation_date             datetime2,
    fingerprint_collection_id BIGINT,
    hash_id                   BIGINT,
    last_check_date           datetime2,
    status                    INT,
    user_id                   BIGINT,
    created_at datetime2(7) NOT NULL DEFAULT GETDATE(),
    created_by varchar(255) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_web_hash
(
    incognito           BIT,
    audio               DOUBLE PRECISION,
    cookies_enabled     BIT,
    crc_canvas          VARCHAR(255),
    crc_geometry        VARCHAR(255),
    crc_text            VARCHAR(255),
    crc_webgl           VARCHAR(255),
    do_not_track        VARCHAR(255),
    dom_blocker         VARCHAR(255),
    extended_fonts      VARCHAR(MAX),
    fonts               VARCHAR(MAX),
    font_pref_default   DOUBLE PRECISION,
    font_pref_apple     DOUBLE PRECISION,
    font_pref_min       DOUBLE PRECISION,
    font_pref_mono      DOUBLE PRECISION,
    font_pref_sans      DOUBLE PRECISION,
    font_pref_serif     DOUBLE PRECISION,
    font_pref_system    DOUBLE PRECISION,
    gpu                 VARCHAR(MAX),
    logical_processors  INT,
    memory              DOUBLE PRECISION,
    has_ad_block        BIT,
    has_add_behavior    BIT,
    has_indexed_db      BIT,
    has_lied_resolution BIT,
    has_lied_languages  BIT,
    has_lied_os         BIT,
    has_local_storage   BIT,
    has_open_database   BIT,
    has_session_storage BIT,
    language            VARCHAR(255),
    local_storage_id    VARCHAR(255),
    maths_acos          DOUBLE PRECISION,
    maths_acosh         DOUBLE PRECISION,
    maths_acos_pf       DOUBLE PRECISION,
    maths_asin          DOUBLE PRECISION,
    maths_asinh         DOUBLE PRECISION,
    maths_asin_pf       DOUBLE PRECISION,
    maths_atan          DOUBLE PRECISION,
    maths_atanh         DOUBLE PRECISION,
    maths_atanh_pf      DOUBLE PRECISION,
    maths_cos           DOUBLE PRECISION,
    maths_cosh          DOUBLE PRECISION,
    maths_cosh_pf       DOUBLE PRECISION,
    maths_exp           DOUBLE PRECISION,
    maths_expm1         DOUBLE PRECISION,
    maths_expm1_pf      DOUBLE PRECISION,
    maths_log1p         DOUBLE PRECISION,
    maths_log1p_pf      DOUBLE PRECISION,
    maths_pow_pi        DOUBLE PRECISION,
    maths_sin           DOUBLE PRECISION,
    maths_sinh          DOUBLE PRECISION,
    maths_sin_pf        DOUBLE PRECISION,
    maths_tan           DOUBLE PRECISION,
    maths_tanh          DOUBLE PRECISION,
    maths_tanh_pf       DOUBLE PRECISION,
    color_gamut         VARCHAR(255),
    contrast            INT,
    forced_colors       BIT,
    hdr                 BIT,
    inverted_colors     VARCHAR(255),
    monochrome          INT,
    reduced_motion      BIT,
    navigator_cpu_class VARCHAR(255),
    navigator_platform  VARCHAR(255),
    plugins             VARCHAR(MAX),
    random_seed         VARCHAR(255),
    color_depth         INT,
    frame_size          VARCHAR(255),
    available_height    INT,
    avaible_width       INT,
    screen_height       INT,
    screen_width        INT,
    timezone            VARCHAR(255),
    timezone_offset     INT,
    max_touch_points    INT,
    touch_event         BIT,
    touch_start         BIT,
    vendor              VARCHAR(255),
    vendor_flavors      VARCHAR(255),
    type                INT,
    id                  BIGINT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE fingerprint_web_hash_history
(
    incognito           BIT,
    audio               DOUBLE PRECISION,
    cookies_enabled     BIT,
    crc_canvas          VARCHAR(255),
    crc_geometry        VARCHAR(255),
    crc_text            VARCHAR(255),
    crc_webgl           VARCHAR(255),
    do_not_track        VARCHAR(255),
    dom_blocker         VARCHAR(255),
    extended_fonts      VARCHAR(MAX),
    fonts               VARCHAR(MAX),
    font_pref_default   DOUBLE PRECISION,
    font_pref_apple     DOUBLE PRECISION,
    font_pref_min       DOUBLE PRECISION,
    font_pref_mono      DOUBLE PRECISION,
    font_pref_sans      DOUBLE PRECISION,
    font_pref_serif     DOUBLE PRECISION,
    font_pref_system    DOUBLE PRECISION,
    gpu                 VARCHAR(MAX),
    logical_processors  INT,
    memory              DOUBLE PRECISION,
    has_ad_block        BIT,
    has_add_behavior    BIT,
    has_indexed_db      BIT,
    has_lied_resolution BIT,
    has_lied_languages  BIT,
    has_lied_os         BIT,
    has_local_storage   BIT,
    has_open_database   BIT,
    has_session_storage BIT,
    language            VARCHAR(255),
    local_storage_id    VARCHAR(255),
    maths_acos          DOUBLE PRECISION,
    maths_acosh         DOUBLE PRECISION,
    maths_acos_pf       DOUBLE PRECISION,
    maths_asin          DOUBLE PRECISION,
    maths_asinh         DOUBLE PRECISION,
    maths_asin_pf       DOUBLE PRECISION,
    maths_atan          DOUBLE PRECISION,
    maths_atanh         DOUBLE PRECISION,
    maths_atanh_pf      DOUBLE PRECISION,
    maths_cos           DOUBLE PRECISION,
    maths_cosh          DOUBLE PRECISION,
    maths_cosh_pf       DOUBLE PRECISION,
    maths_exp           DOUBLE PRECISION,
    maths_expm1         DOUBLE PRECISION,
    maths_expm1_pf      DOUBLE PRECISION,
    maths_log1p         DOUBLE PRECISION,
    maths_log1p_pf      DOUBLE PRECISION,
    maths_pow_pi        DOUBLE PRECISION,
    maths_sin           DOUBLE PRECISION,
    maths_sinh          DOUBLE PRECISION,
    maths_sin_pf        DOUBLE PRECISION,
    maths_tan           DOUBLE PRECISION,
    maths_tanh          DOUBLE PRECISION,
    maths_tanh_pf       DOUBLE PRECISION,
    color_gamut         VARCHAR(255),
    contrast            INT,
    forced_colors       BIT,
    hdr                 BIT,
    inverted_colors     VARCHAR(255),
    monochrome          INT,
    reduced_motion      BIT,
    navigator_cpu_class VARCHAR(255),
    navigator_platform  VARCHAR(255),
    plugins             VARCHAR(MAX),
    random_seed         VARCHAR(255),
    color_depth         INT,
    frame_size          VARCHAR(255),
    available_height    INT,
    avaible_width       INT,
    screen_height       INT,
    screen_width        INT,
    timezone            VARCHAR(255),
    timezone_offset     INT,
    max_touch_points    INT,
    touch_event         BIT,
    touch_start         BIT,
    vendor              VARCHAR(255),
    vendor_flavors      VARCHAR(255),
    type                INT,
    id                  BIGINT NOT NULL,
    created_at datetime2(7) NOT NULL DEFAULT GETDATE(),
    created_by varchar(255) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE geo_atm_data
(
    id          BIGINT           NOT NULL,
    city        VARCHAR(255),
    country     VARCHAR(255),
    postal_code VARCHAR(255),
    code        VARCHAR(255),
    latitude    DOUBLE PRECISION NOT NULL,
    longitude   DOUBLE PRECISION NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE geo_ip_data
(
    transaction_id BIGINT NOT NULL,
    city           VARCHAR(255),
    country        VARCHAR(255),
    postal_code    VARCHAR(255),
    continent_name VARCHAR(255),
    user_id        VARCHAR(255),
    PRIMARY KEY (transaction_id)
);

CREATE TABLE geo_profile_data
(
    id             BIGINT NOT NULL,
    city           VARCHAR(255),
    country        VARCHAR(255),
    postal_code    VARCHAR(255),
    locality       VARCHAR(255),
    province_state VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE geo_user_profile
(
    id              BIGINT           NOT NULL,
    count           INT,
    last_check_date datetime2,
    latitude        DOUBLE PRECISION NOT NULL,
    longitude       DOUBLE PRECISION NOT NULL,
    user_id         VARCHAR(255),
    valid           BIT              NOT NULL,
    geo_data_id     BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE geodata_error_parameter
(
    id                INT              NOT NULL,
    amount            DOUBLE PRECISION NOT NULL,
    channel_id        INT,
    credit_account    VARCHAR(255),
    event_date        datetime2,
    date_ocurrence    datetime2,
    debit_account     VARCHAR(255),
    user_id           VARCHAR(255),
    operation_type_id INT,
    PRIMARY KEY (id)
);

CREATE TABLE history_score
(
    id             INT NOT NULL,
    creation_date  datetime2,
    score          INT NOT NULL,
    transaction_id BIGINT,
    user_id        VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE last_assigned_case
(
    id                INT NOT NULL,
    business_group_id INT,
    transaction_id    BIGINT,
    user_id           BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE ml_batch
(
    id                  INT NOT NULL,
    batch_state         VARCHAR(255),
    command             VARCHAR(255),
    description         VARCHAR(255),
    end_date            datetime2,
    parameter           VARCHAR(255),
    proc_item_quantity  INT,
    stage               VARCHAR(255),
    start_date          datetime2,
    total_item_quantity INT,
    type                VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE ml_experiment
(
    id                   BIGINT NOT NULL,
    date_from            datetime2,
    date_to              datetime2,
    description          VARCHAR(255),
    id_network           BIGINT,
    quantity_transaction BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE ml_feature
(
    id     INT NOT NULL,
    cyclic BIT NOT NULL,
    name   VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE ml_network
(
    id                          INT NOT NULL,
    date_from                   datetime2,
    date_to                     datetime2,
    end_radius                  FLOAT,
    end_rate                    FLOAT,
    feature_quantity            INT,
    grid_side                   INT,
    iteration                   INT NOT NULL,
    name                        VARCHAR(255),
    network_state               VARCHAR(255),
    network_values              nvarchar(max),
    percentage                  INT NOT NULL,
    profile_calculated_quantity INT,
    profile_date                datetime2,
    profile_day_quantity        INT,
    start_radius                FLOAT,
    start_rate                  FLOAT,
    transaction_quantity        INT,
    PRIMARY KEY (id)
);

CREATE TABLE ml_network_feature
(
    feature_id INT NOT NULL,
    network_id INT NOT NULL,
    PRIMARY KEY (feature_id, network_id)
);

CREATE TABLE ml_network_log
(
    network_id                   INT    NOT NULL,
    transaction_id               BIGINT NOT NULL,
    account_id                   VARCHAR(255),
    milis                        BIGINT NOT NULL,
    network_log_date             datetime2,
    neuron                       INT    NOT NULL,
    quantity_transaction_profile INT,
    score                        FLOAT  NOT NULL,
    PRIMARY KEY (network_id, transaction_id)
);

CREATE TABLE ml_network_operation_type
(
    network_id        INT NOT NULL,
    operation_type_id INT NOT NULL,
    PRIMARY KEY (network_id, operation_type_id)
);

CREATE TABLE ml_som_account_profile
(
    network_id           INT          NOT NULL,
    account_id           VARCHAR(255) NOT NULL,
    profile              nvarchar(max),
    som_date             datetime2,
    transaction_quantity INT,
    PRIMARY KEY (network_id, account_id)
);

CREATE TABLE ml_som_account_profile_usable
(
    network_id           INT NOT NULL,
    account_id           VARCHAR(255),
    profile              nvarchar(max),
    som_date             datetime2,
    transaction_quantity INT,
    PRIMARY KEY (network_id, account_id)
);

CREATE TABLE ml_transaction
(
    transaction_id    BIGINT NOT NULL,
    amount            DOUBLE PRECISION,
    channel_id        INT,
    credit_account    VARCHAR(255),
    debit_account     VARCHAR(255),
    event_date        datetime,
    ip                VARCHAR(255),
    operation_type_id INT,
    result            SMALLINT,
    PRIMARY KEY (transaction_id)
);

CREATE TABLE ml_transaction_feature
(
    feature_id     BIGINT           NOT NULL,
    transaction_id BIGINT           NOT NULL,
    value          DOUBLE PRECISION NOT NULL,
    PRIMARY KEY (feature_id, transaction_id)
);

CREATE TABLE ml_transaction_result
(
    transaction_id         BIGINT NOT NULL,
    experiment             INT    NOT NULL,
    neuron                 INT    NOT NULL,
    quantity_items_profile INT,
    result                 FLOAT  NOT NULL,
    PRIMARY KEY (transaction_id)
);

CREATE TABLE ml_usable_network
(
    id                          INT NOT NULL,
    date_from                   datetime2,
    date_to                     datetime2,
    end_radius                  FLOAT,
    end_rate                    FLOAT,
    feature_quantity            INT,
    grid_side                   INT,
    iteration                   INT NOT NULL,
    name                        VARCHAR(255),
    network_state               VARCHAR(255),
    network_values              nvarchar(max),
    percentage                  INT NOT NULL,
    profile_calculated_quantity INT,
    profile_date                datetime2,
    profile_day_quantity        INT,
    start_radius                FLOAT,
    start_rate                  FLOAT,
    transaction_quantity        INT,
    PRIMARY KEY (id)
);

CREATE TABLE ml_usable_network_feature
(
    feature_id INT NOT NULL,
    network_id INT NOT NULL,
    PRIMARY KEY (feature_id, network_id)
);

CREATE TABLE notification_config
(
    id                   INT NOT NULL,
    business_group_id    INT,
    enabled              BIT NOT NULL,
    notification_type_id INT,
    notification_value   VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE notification_type
(
    id       INT NOT NULL,
    api_key  VARCHAR(255),
    enabled  BIT,
    endpoint VARCHAR(255),
    name     VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE operation_type
(
    id            INT NOT NULL,
    description   VARCHAR(255),
    enabled       BIT,
    event_type_id INT,
    name          VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE parameter
(
    id                INT NOT NULL,
    column_name       VARCHAR(255),
    enabled           BIT NOT NULL,
    event_type_id     INT,
    name              VARCHAR(255),
    parameter_info_id INT,
    parameter_type_id INT,
    user_generated    BIT,
    visibility        INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE parameter_business
(
    id                 INT NOT NULL,
    business           VARCHAR(255),
    daily_max_amount   DOUBLE PRECISION,
    daily_max_count    INT,
    enabled            BIT NOT NULL,
    monthly_max_amount DOUBLE PRECISION,
    monthly_max_count  INT,
    weekly_max_amount  DOUBLE PRECISION,
    weekly_max_count   INT,
    CONSTRAINT UN_business UNIQUE (business),
    PRIMARY KEY (id)
);

CREATE TABLE parameter_channel_operation
(
    id                INT NOT NULL,
    channel_id        INT NOT NULL,
    enabled           BIT NOT NULL,
    operation_type_id INT NOT NULL,
    parameter_id      INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE parameter_query_channel_operation
(
    id                INT NOT NULL,
    channel_id        INT,
    insert_query      VARCHAR(255),
    operation_type_id INT,
    select_query      VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE profile_by_operation
(
    id                     INT NOT NULL,
    accumulated_amount     DOUBLE PRECISION,
    event_count            INT,
    max_amount             DOUBLE PRECISION,
    operation_type_id      INT,
    profile_period_info_id INT,
    PRIMARY KEY (id)
);

CREATE TABLE profile_day_counter
(
    id           INT NOT NULL,
    channel_id   INT,
    event_count  INT,
    profile_date datetime2,
    user_id      VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE profile_period_info_by_credit_account
(
    id                     INT NOT NULL,
    accumulated_amount     DOUBLE PRECISION,
    credit_account         VARCHAR(255),
    max_amount             DOUBLE PRECISION,
    profile_period_info_id INT,
    PRIMARY KEY (id)
);

CREATE TABLE profile_period_information
(
    id                 INT NOT NULL,
    accumulated_amount DOUBLE PRECISION,
    channel_id         INT,
    max_amount         DOUBLE PRECISION,
    period_month       INT,
    period_year        INT,
    transaction_count  INT,
    user_id            VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE report
(
    id          INT NOT NULL,
    json_format VARCHAR(MAX),
    name        VARCHAR(255),
    subtitle    VARCHAR(255),
    title       VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE report_aggregate
(
    id                    INT NOT NULL,
    aggregate_function_id INT,
    field_id              INT,
    report_group_id       INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_display_field
(
    id          INT NOT NULL,
    field_id    INT,
    field_order INT,
    report_id   INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_filter_channel
(
    id         INT NOT NULL,
    channel_id INT,
    report_id  INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_filter_operation_type
(
    id                INT NOT NULL,
    operation_type_id INT,
    report_id         INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_filter_rule
(
    id        INT NOT NULL,
    rule_id   INT,
    report_id INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE report_group
(
    id          INT NOT NULL,
    field_id    INT,
    group_order INT,
    report_id   INT NOT NULL,
    PRIMARY KEY (id)
);

CREATE TABLE rule_auto_assign_case
(
    id                INT NOT NULL,
    business_group_id INT,
    rule_id           INT,
    user_id           BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE rule_business_group
(
    id                   INT NOT NULL,
    enabled              BIT NOT NULL,
    notification_message VARCHAR(MAX),
    business_group_id    INT,
    rule_id              INT,
    PRIMARY KEY (id)
);

CREATE TABLE rule_condition
(
    id                     INT NOT NULL,
    condition_rule_type_id INT,
    condition_type_id      INT,
    condition_value        VARCHAR(255),
    connector_id           INT,
    custom_list_id         INT,
    delta_type_id          INT,
    field_id               INT,
    group_id               INT,
    operator_id            INT,
    parent_group_id        INT,
    rule_id                INT,
    PRIMARY KEY (id)
);

CREATE TABLE rule_event_statement
(
    id        INT NOT NULL,
    context   BIT,
    rule_id   INT,
    statement nvarchar(max),
    PRIMARY KEY (id)
);

CREATE TABLE rule_item
(
    id                INT NOT NULL,
    action_id         SMALLINT,
    channel_id        INT,
    critical_level_id INT,
    enabled           BIT,
    impact_id         INT,
    json_format       nvarchar(max),
    name              VARCHAR(255),
    operation_type_id INT,
    position          INT,
    probability_id    INT,
    rule_type_id      INT,
    score             INT,
    PRIMARY KEY (id)
);

CREATE TABLE rule_matched_event
(
    id                     INT    NOT NULL,
    event_id               BIGINT NOT NULL,
    event_trace_id BINARY (255),
    triggered_date         datetime2,
    triggered_rule_item_id INT,
    PRIMARY KEY (id)
);

CREATE TABLE rule_matched_transaction
(
    transaction_id         BIGINT NOT NULL,
    triggered_rule_item_id INT,
    rule_name VARCHAR(255),
    rule_critical_level_id INT,
    PRIMARY KEY (transaction_id)
);

CREATE TABLE rule_update
(
    id                          INT NOT NULL,
    rule_cep_last_update        datetime2,
    rule_classifier_last_update datetime2,
    rule_engine_last_update     datetime2,
    rule_parameter_business     datetime2,
    PRIMARY KEY (id)
);

CREATE TABLE swift
(
    id         INT NOT NULL,
    bank       VARCHAR(255),
    branch     VARCHAR(255),
    city       VARCHAR(255),
    code       VARCHAR(255),
    country_id INT,
    PRIMARY KEY (id)
);

CREATE TABLE system_parameter
(
    id             INT NOT NULL PRIMARY KEY,
    config_value   VARCHAR(255),
    parameter_name VARCHAR(255),
    CONSTRAINT unique_name_parameter UNIQUE (parameter_name)
);

CREATE TABLE transaction_entity
(
    id                BIGINT NOT NULL,
    action_id         SMALLINT,
    amount            DOUBLE PRECISION,
    case_status_id    SMALLINT,
    channel_id        INT,
    credit_account    VARCHAR(255),
    debit_account     VARCHAR(255),
    event_date        datetime2,
    ip                VARCHAR(45),
    operation_type_id INT,
    time              TIME,
    user_id           VARCHAR(255),
    guid              VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE transaction_exception
(
    id                BIGINT           NOT NULL,
    amount            DOUBLE PRECISION NOT NULL,
    channel_id        INT,
    credit_account    VARCHAR(255),
    debit_account     VARCHAR(255),
    operation_type_id INT,
    transaction_id    BIGINT,
    user_id           VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE user_business_daily_stat
(
    id                    BIGINT NOT NULL,
    user_business_stat_id BIGINT,
    stat_date             datetime2,
    stat_month            INT,
    stat_year             INT,
    amount_accumulated    DOUBLE PRECISION,
    transaction_count     INT,
    day_of_month          INT,
    PRIMARY KEY (id)
);

CREATE TABLE user_business_group
(
    id                INT NOT NULL,
    business_group_id INT,
    user_id           BIGINT,
    PRIMARY KEY (id)
);

CREATE TABLE user_business_monthly_stat
(
    id                    BIGINT NOT NULL,
    user_business_stat_id BIGINT,
    stat_date             datetime2,
    stat_month            INT,
    stat_year             INT,
    amount_accumulated    DOUBLE PRECISION,
    transaction_count     INT,
    PRIMARY KEY (id)
);

CREATE TABLE user_business_stat
(
    id                  BIGINT       NOT NULL,
    business_name       VARCHAR(255) NOT NULL,
    daily_max_count     INT,
    daily_next_update   datetime2,
    max_amount          DOUBLE PRECISION,
    monthly_max_count   INT,
    monthly_next_update datetime2,
    new_daily_profile   BIT,
    new_monthly_profile BIT,
    new_weekly_profile  BIT,
    user_id             VARCHAR(255) NOT NULL,
    weekly_max_count    INT,
    weekly_next_update  datetime2,
    PRIMARY KEY (id)
);

CREATE TABLE user_business_weekly_stat
(
    id                    BIGINT NOT NULL,
    user_business_stat_id BIGINT,
    stat_date             datetime2,
    stat_month            INT,
    stat_year             INT,
    amount_accumulated    DOUBLE PRECISION,
    transaction_count     INT,
    week_of_month         INT,
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_daily
(
    id                      BIGINT NOT NULL,
    channel_id              BIGINT,
    last_transaction_date   datetime2,
    transaction_accumulated BIGINT,
    transaction_date        datetime2,
    user_id                 VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_monthly
(
    id                      BIGINT NOT NULL,
    channel_id              BIGINT,
    last_transaction_date   datetime2,
    transaction_accumulated BIGINT,
    transaction_month       BIGINT,
    transaction_year        BIGINT,
    user_id                 VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_summary
(
    id                 INT              NOT NULL,
    average            DOUBLE PRECISION NOT NULL,
    max_amount         DOUBLE PRECISION,
    min_amount         DOUBLE PRECISION,
    standard_deviation DOUBLE PRECISION,
    user_id            VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_summary_history
(
    id                    INT NOT NULL,
    last_processed_date   datetime2,
    max_amount            DOUBLE PRECISION,
    min_amount            DOUBLE PRECISION,
    total_amount          DOUBLE PRECISION,
    transactions_quantity INT,
    user_id               VARCHAR(255),
    week_number           INT,
    PRIMARY KEY (id)
);

CREATE TABLE user_transaction_weekly
(
    id                      BIGINT NOT NULL,
    channel_id              BIGINT,
    last_transaction_date   datetime2,
    transaction_accumulated BIGINT,
    transaction_weekly      BIGINT,
    transaction_year        BIGINT,
    user_id                 VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE audit_revision
(
    id         INT    NOT NULL,
    ip         VARCHAR(255),
    timestamp  BIGINT NOT NULL,
    user_agent VARCHAR(255),
    username   VARCHAR(255),
    PRIMARY KEY (id)
);

CREATE TABLE auth_fa_user_history
(
    id                   BIGINT NOT NULL,
    rev                  INT    NOT NULL,
    revtype              SMALLINT,
    email                VARCHAR(255),
    enabled              BIT,
    first_name           VARCHAR(255),
    force_reset_password BIT,
    last_name            VARCHAR(255),
    ldap_user            BIT,
    password             VARCHAR(60),
    role_changed         BIT,
    token_expired        BIT,
    user_name            VARCHAR(255),
    app_user             BIT,
    PRIMARY KEY (id, rev)
);

CREATE TABLE auth_ldap_role_history
(
    id        BIGINT NOT NULL,
    rev       INT    NOT NULL,
    revtype   SMALLINT,
    group_cn  VARCHAR(255),
    role_id   BIGINT,
    role_name VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE auth_role_history
(
    id      BIGINT NOT NULL,
    rev     INT    NOT NULL,
    revtype SMALLINT,
    name    VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE auth_role_privilege_history
(
    rev          INT    NOT NULL,
    role_id      BIGINT NOT NULL,
    privilege_id BIGINT NOT NULL,
    revtype      SMALLINT,
    PRIMARY KEY (rev, role_id, privilege_id)
);

CREATE TABLE auth_user_role_history
(
    rev     INT    NOT NULL,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    revtype SMALLINT,
    PRIMARY KEY (rev, user_id, role_id)
);

CREATE TABLE channel_history
(
    id           INT NOT NULL,
    rev          INT NOT NULL,
    revtype      SMALLINT,
    description  VARCHAR(255),
    enabled      BIT,
    ignore_rules BIT,
    name         VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE operation_type_history
(
    id            INT NOT NULL,
    rev           INT NOT NULL,
    revtype       SMALLINT,
    description   VARCHAR(255),
    enabled       BIT,
    event_type_id INT,
    name          VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE parameter_channel_operation_history
(
    id                INT NOT NULL,
    rev               INT NOT NULL,
    revtype           SMALLINT,
    channel_id        INT,
    enabled           BIT,
    operation_type_id INT,
    parameter_id      INT,
    PRIMARY KEY (id, rev)
);

CREATE TABLE parameter_history
(
    id                INT NOT NULL,
    rev               INT NOT NULL,
    revtype           SMALLINT,
    column_name       VARCHAR(255),
    enabled           BIT,
    event_type_id     INT,
    name              VARCHAR(255),
    parameter_info_id INT,
    parameter_type_id INT,
    user_generated    BIT,
    visibility        INT,
    PRIMARY KEY (id, rev)
);

CREATE TABLE rule_condition_history
(
    id                     INT NOT NULL,
    rev                    INT NOT NULL,
    revtype                SMALLINT,
    condition_rule_type_id INT,
    condition_type_id      INT,
    condition_value        VARCHAR(255),
    connector_id           INT,
    custom_list_id         INT,
    delta_type_id          INT,
    field_id               INT,
    group_id               INT,
    operator_id            INT,
    parent_group_id        INT,
    rule_id                INT,
    PRIMARY KEY (id, rev)
);

CREATE TABLE rule_item_history
(
    id                INT NOT NULL,
    rev               INT NOT NULL,
    revtype           SMALLINT,
    action_id         SMALLINT,
    channel_id        INT,
    critical_level_id INT,
    enabled           BIT,
    impact_id         INT,
    json_format       VARCHAR(MAX),
    name              VARCHAR(255),
    operation_type_id INT,
    position          INT,
    probability_id    INT,
    rule_type_id      INT,
    score             INT,
    PRIMARY KEY (id, rev)
);

CREATE TABLE system_parameter_history
(
    id             INT NOT NULL,
    rev            INT NOT NULL,
    revtype        SMALLINT,
    config_value   VARCHAR(255),
    parameter_name VARCHAR(255),
    PRIMARY KEY (id, rev)
);

CREATE TABLE event_imports (
    id VARCHAR(36) NOT NULL,
    status NVARCHAR(255),
    event_type VARCHAR(255) NOT NULL,
    file_name VARCHAR(255),
    file_size BIGINT,
    created_at DATETIME2,
    updated_at DATETIME2,
    PRIMARY KEY (id)
);

CREATE TABLE event_entity_import
(
    id                BIGINT NOT NULL,
    import_id         VARCHAR(36) NOT NULL,
    amount            FLOAT,
    channel_id        INT,
    credit_account    VARCHAR(255),
    debit_account     VARCHAR(255),
    event_date        DATETIME2,
    operation_type_id INT,
    user_id           VARCHAR(255),
    PRIMARY KEY (id),
    CONSTRAINT FK_EventEntityImport_EventImports FOREIGN KEY (import_id)
        REFERENCES event_imports (id)
        ON DELETE CASCADE
);

CREATE TABLE event_additional_fields_import
(
    id              BIGINT NOT NULL,
    event_id  BIGINT NOT NULL,
    name            VARCHAR(255) NOT NULL,
    value           VARCHAR(MAX),
        PRIMARY KEY (id),
        CONSTRAINT FK_EventAdditionalFieldsImport_EventEntityImport FOREIGN KEY (event_id)
            REFERENCES event_entity_import (id)
            ON DELETE CASCADE
);

-- END TABLES