package vu.data.models.report.profile;

import lombok.*;

import jakarta.persistence.*;

@Entity
@NoArgsConstructor
@Getter
@Table(name = "profileperiodinfo")
public class SourceProfilePeriodInformation{

    @Id
    private int id;

    @Column(name = "iduser")
    private String userId;

    @Column(name = "idchannel")
    private int channelId;

    @Column(name = "year")
    private Integer periodYear;

    @Column(name = "month")
    private Integer periodMonth;

    @Column(name = "maxamount")
    private double maxAmount;

    @Column(name = "accumulatedamount")
    private double accumulatedAmount;

    @Column(name = "transactioncount")
    private int transactionCount;

}
