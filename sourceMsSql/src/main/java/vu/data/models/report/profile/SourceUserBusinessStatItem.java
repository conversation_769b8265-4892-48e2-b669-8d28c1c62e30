package vu.data.models.report.profile;

import  jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@NoArgsConstructor
@Getter
@Table(name = "user_business_stats")
public class SourceUserBusinessStatItem{

    @Id
    private Long id;

    @Column(name = "user_id")
    @NotNull
    private String userId;

    @Column(name = "business")
    @NotNull
    private String businessName;

    @Column(name = "max_amount")
    private Double maxAmount;

    @Column(name = "daily_max_count")
    private Integer dailyMaxCount;

    @Column(name = "weekly_max_count")
    private Integer weeklyMaxCount;

    @Column(name = "monthly_max_count")
    private Integer monthlyMaxCount;

    @Column(name = "daily_next_update")
    private Date dailyNextUpdate;

    @Column(name = "weekly_next_update")
    private Date weeklyNextUpdate;

    @Column(name = "monthly_next_update")
    private Date monthlyNextUpdate;

    @Column(name = "new_daily_profile")
    private Boolean newDailyProfile;

    @Column(name = "new_weekly_profile")
    private Boolean newWeeklyProfile;

    @Column(name = "new_monthly_profile")
    private Boolean newMonthlyProfile;
}
