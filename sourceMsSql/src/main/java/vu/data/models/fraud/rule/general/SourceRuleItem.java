package vu.data.models.fraud.rule.general;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.ToString;

import jakarta.persistence.*;
import java.util.Collection;

@Getter
@Entity
@Table(name = "\"rule\"")
public class SourceRuleItem{
    @Id
    private int id;

    @Column(name = "name")
    private String name;

    @Column(name = "idruletype")
    private int ruleTypeId;

    @Column(name = "score")
    private int score;

    @Column(name = "idchannel")
    private Integer channelId;

    @Column(name = "idoperationtype")
    private Integer operationTypeId;

    @Column(name = "jsonformat", length = 8000)
    private String jsonFormat;

    @Column(name = "idaction")
    private short actionId;

    @Column(name = "ruleorder")
    private int order;

    @Column(name = "enabled")
    private boolean enabled;

    @Column(name = "idprobability")
    private int probabilityId;

    @Column(name = "idimpact")
    private int impactId;

    @Column(name = "id_critical_level")
    private int criticalLevelId;
}
