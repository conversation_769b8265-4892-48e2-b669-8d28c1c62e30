package vu.data.models.fraud.rule.general;

public class SourceRuleActionEnumName{
        public enum Enum{
                NONE,
                ALLOW,
                AUTH,
                DENY,
                REVIEW,
                BLOCK,
                CASE
        }

        public static final int NONE=0;

        public static String getName(int actionId){
                return Enum.values()[actionId].name();
        }
}
