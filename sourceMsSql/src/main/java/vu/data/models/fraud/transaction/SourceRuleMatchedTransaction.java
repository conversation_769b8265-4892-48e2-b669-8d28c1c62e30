package vu.data.models.fraud.transaction;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Entity
@NoArgsConstructor
@Table(name = "eventtriggered")
public class SourceRuleMatchedTransaction{

    @Id
    private int id;

    @Column(name = "idevent")
    private Integer eventId;

    @Column(name = "idtriggeredrule")
    private Integer triggeredRuleItemId;

    @Column(name = "triggereddate")
    private Date triggeredDate;

    @Column(name = "ideventtype")
    private Integer eventType;
}
