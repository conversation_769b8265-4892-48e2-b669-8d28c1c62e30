package vu.data.models.fraud.cm;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

@Getter
@Entity
@Table(name = "case_additional_info")
public class SourceCaseAdditionalInfo{
    @Id
    private Integer id;

    @Column(name = "id_transaction")
    private Long transactionId;

    @Column(name = "justification")
    private String justification;

}
