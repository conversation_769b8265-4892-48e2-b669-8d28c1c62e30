package vu.data.models.fraud.rule.general;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

@Getter
@Entity
@Table(name = "rule_event_statements")
public class SourceRuleEventStatement{

    @Id
    private int id;

    @Column(name = "id_rule")
    private int ruleId;

    @Column(name = "statement")
    private String statement;

    @Column(name = "is_context")
    private boolean context;

}