package vu.data.models.fraud.event;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "eventtype")
public class SourceEventType{
    @Id
    private int id;
    private String name;
    private boolean enabled;
    @Column(name = "\"descriptionTranslate\"")
    private String descriptionTranslate;

    public int getId(){
        return id;
    }

    public String getName(){
        return name;
    }

    public boolean isEnabled(){
        return enabled;
    }

    public String getDescriptionTranslate(){
        return descriptionTranslate;
    }
}
