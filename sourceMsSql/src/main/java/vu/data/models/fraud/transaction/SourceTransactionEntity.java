package vu.data.models.fraud.transaction;

import lombok.Getter;
import vu.data.models.fraud.event.CaseStatusEnumName;
import vu.data.models.fraud.rule.general.SourceRuleActionEnumName;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Entity
@Table(name = "eventtransaction")
public class SourceTransactionEntity{
    @Id
    private long id;

    @Column(name = "idchannel")
    private Integer channelId;

    @Column(name = "idoperationtype")
    private int operationTypeId;

    @Column(name = "eventdate")
    private Date eventDate;

    @Column(name = "creditaccount")
    private String creditAccount;

    @Column(name = "debitaccount")
    private String debitAccount;

    @Column(name = "amount")
    private double amount;

    @Column(name = "iduser")
    private String userId;

    @Column(name = "idcasestate")
    private Short caseStatusId = (short) CaseStatusEnumName.Enum.NONE.ordinal();

    @Column(name = "ip")
    private String ip;

    @Column(name = "idaction")
    private short actionId = (short) SourceRuleActionEnumName.Enum.NONE.ordinal();
}
