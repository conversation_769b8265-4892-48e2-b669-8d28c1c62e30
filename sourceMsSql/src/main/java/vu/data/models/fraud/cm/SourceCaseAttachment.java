package vu.data.models.fraud.cm;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

import java.util.Date;

@Getter
@Entity
@Table(name = "case_attachments")
public class SourceCaseAttachment{
    @Id
    private int id;

    @Column(name = "id_transaction")
    private long transactionId;

    @Column(name = "filename")
    private String filename;

    @Column(name = "created_date")
    private Date createdDate;

    @Column(name = "size")
    private double fileSize;
}
