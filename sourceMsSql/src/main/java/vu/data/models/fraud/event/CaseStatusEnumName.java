package vu.data.models.fraud.event;

public class CaseStatusEnumName {
    public enum Enum {
        INVALID,        // 0
        PENDING,        // 1
        LEGIT,          // 2
        FRAUD,          // 3
        IN_PROGRESS,    // 4
        ASSIGNED,       // 5
        NONE            // 6
    }

    public static String getName(int actionId) {
        return Enum.values()[actionId].name();
    }
}