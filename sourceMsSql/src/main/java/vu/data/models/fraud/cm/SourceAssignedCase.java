package vu.data.models.fraud.cm;

import lombok.Getter;
import lombok.ToString;
import vu.data.models.fraud.event.CaseStatusEnumName;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Entity
@Table(name = "assigned_cases")
public class SourceAssignedCase{
    @Id
    private long id;

    @Column(name="id_transaction")
    private long transactionId;

    @Column(name="id_user")
    private Long assignedTo;

    @Column(name="id_business_group")
    private Integer businessGroupId;

    @Column(name="id_case_state")
    @ToString.Exclude
    private CaseStatusEnumName.Enum caseStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="assigned_date")
    private Date assignedDate;

    @Column(name="assigned_by")
    private Long assignedBy;

    private String note;
}
