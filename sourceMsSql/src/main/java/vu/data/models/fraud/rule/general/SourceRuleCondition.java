package vu.data.models.fraud.rule.general;

import jakarta.persistence.*;
import lombok.Getter;

@Getter
@Entity
@Table(name = "rulecondition")
public class SourceRuleCondition{
    @Id
    private int id;

    @Column(name = "idrule")
    private int ruleId;

    @Column(name = "idfield")
    private Integer fieldId;

    @Column(name = "idoperator")
    private int operatorId;

    @Column(name = "idconditiontype")
    private int conditionTypeId;

    @Column(name = "value")
    private String conditionValue;

    @Column(name = "idconnector")
    private int connectorId;

    @Column(name = "idgroup")
    private int groupId;

    @Column(name = "idparentgroup")
    private int parentGroupId;

    @Column(name = "idcustomlist")
    private int customListId;

    @Column(name = "iddeltatype")
    private int deltaTypeId;

    @Column(name = "idconditionruletype")
    private int conditionRuleTypeId;
}
