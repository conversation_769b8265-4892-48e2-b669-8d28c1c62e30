package vu.data.models.fraud.event;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Getter
@Entity
@Table(name = "events")
public class SourceEvents{
    @Id
    private long id;

    @Column(name = "idchannel")
    private Integer channelId;

    @Column(name = "idoperationtype")
    private int operationTypeId;

    @Column(name = "datetime")
    private Date eventDate;

    @Column(name = "iduser")
    private String userId;
}
