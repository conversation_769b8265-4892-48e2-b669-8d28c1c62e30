package vu.data.models.administration.settings.parameter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

@Getter
@Entity
@Table(name = "wsparameterbychanneloperation")
public class SourceParameterChannelOperation{

    @Id
    private Integer id;

    @Column(name = "idchannel")
    private Integer channelId;

    @Column(name = "idparameter")
    private Integer parameterId;

    @Column(name = "idoperationtype")
    private Integer operationTypeId;

    private Boolean enabled;
}
