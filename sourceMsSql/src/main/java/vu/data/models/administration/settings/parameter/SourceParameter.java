package vu.data.models.administration.settings.parameter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

@Getter
@Entity
@Table(name="wsparameterall")
public class SourceParameter{
    @Id
    private int id;

    @Column(name = "ideventtype")
    private int eventTypeId;

    @Column(name = "usergenerated")
    private boolean userGenerated;

    private String name;

    @Column(name = "parametertype")
    private int parameterTypeId;

    @Column(name = "columnname")
    private String columnName;

    private int visibility;

    @Column(name = "idparameterinfo")
    private Integer parameterInfoId;
}
