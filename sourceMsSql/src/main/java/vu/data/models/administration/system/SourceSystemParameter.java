package vu.data.models.administration.system;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Table(name = "systemparameters")
public class SourceSystemParameter{
    @Id
    private int id;

    @Column(name = "parametername")
    private String parameterName;

    @Column(name = "value")
    private String configValue;

    public String getParameterName(){
        return parameterName;
    }

    public String getConfigValue(){
        return configValue;
    }
}
