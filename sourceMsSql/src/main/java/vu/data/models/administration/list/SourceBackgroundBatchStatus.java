package vu.data.models.administration.list;

import lombok.Getter;

import jakarta.persistence.*;
import java.util.Date;


@Getter
@Entity
@Table(name = "background_batch_status")
public class SourceBackgroundBatchStatus{

    public enum StatusType {
        processing,
        processedOk,
        processedError
    }

    @Id
    @Column(name = "id", nullable = false)
    private int id;

    private String path;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "start_date")
    private Date startDate;

    @Column(name = "end_date")
    private Date endDate;

    @Column(name = "status")
    private int batchStatus;

    @Column(name = "status_detail")
    private String statusDetail;

}
