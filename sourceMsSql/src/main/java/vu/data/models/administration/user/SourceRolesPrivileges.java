package vu.data.models.administration.user;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "roles_privileges")
public class SourceRolesPrivileges implements Serializable{

    @Id
    @Column(name = "role_id")
    private long roleId;

    @Id
    @Column(name = "privilege_id")
    private long privilegeId;
}
