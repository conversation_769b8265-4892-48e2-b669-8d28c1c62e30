package vu.data.models.administration.list;

import lombok.Getter;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Entity
@Table(name = "customlists")
public class SourceCustomList{
    @Id
    private int id;

    private String name;

    private String description;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "creationdate")
    private Date creationDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updatedate")
    private Date updateDate;
}
