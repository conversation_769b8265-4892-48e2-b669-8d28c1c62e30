package vu.data.models.administration.user;

import jakarta.persistence.*;
import lombok.Getter;

import java.util.Collection;

@Getter
@Entity
@Table(name = "\"User\"")
public class SourceUser{
    @Id
    private Long id;

    private String email;

    @Column(nullable = true)
    private boolean enabled;

    @Column(name = "\"firstName\"")
    private String firstName;

    @Column(name = "\"forceResetPassword\"")
    private boolean forceResetPassword;

    @Column(name = "\"lastName\"")
    private String lastName;

    @Column(length = 60)
    private String password;

    @Column(name = "\"tokenExpired\"")
    private boolean tokenExpired;

    @Column(name = "\"userName\"")
    private String userName;

    @Column(name = "role_changed")
    private boolean roleChanged;

    @Column(name = "\"isLdapUser\"")
    private boolean ldapUser;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "users_roles", joinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id"))
    private Collection<SourceRole> sourceRoles;
}
