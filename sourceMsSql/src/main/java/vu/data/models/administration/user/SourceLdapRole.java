package vu.data.models.administration.user;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Getter
@Table(name = "ldaproles")
public class SourceLdapRole{
    @Id
    private Long id;

    @Column(name = "role_id")
    private Long roleId;

    @Column(name = "role_name")
    private String roleName;

    @Column(name = "group_cn")
    private String groupCn;
}
