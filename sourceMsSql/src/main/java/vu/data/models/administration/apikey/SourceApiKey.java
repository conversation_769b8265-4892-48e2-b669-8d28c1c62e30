package vu.data.models.administration.apikey;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;

@Getter
@Entity
@Table(name = "api_key")
public class SourceApiKey{

    @Id
    private int id;

    @Column(name = "system")
    private String system;

    @Column(name = "apikey")
    private String apiKey;

}
