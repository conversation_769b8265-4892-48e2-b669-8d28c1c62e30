package vu.data.models.fingerprint.v2;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;
import java.util.UUID;

@Entity
@Getter
@Table(name = "fingerprint_unverified_values")
public class SourceFingerprintUnverifiedValues{

    @Id
    private long id;
    @Column(name = "id_user_hash")
    private long userHashId;
    private UUID token;
    @Column(name = "creation_date")
    private Date creationDate;

}