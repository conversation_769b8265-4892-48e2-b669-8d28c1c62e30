package vu.data.models.fingerprint.v2;

import lombok.Getter;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Getter
@Table(name = "fingerprint_client_hashes")
public class SourceFingerprintClientHash{

    @Id
    private long id;

    @Column(name = "hash")
    private String hash;

    @Column(name = "creation_date")
    private Date creationDate;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "id", referencedColumnName="id",updatable = false, insertable=false)
    protected SourceFingerprintServerValues sourceFingerprintServerValues;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "id", referencedColumnName="id",updatable = false, insertable=false)
    protected SourceFingerprintClientValues sourceFingerprintClientValues;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "id", referencedColumnName="id",updatable = false, insertable=false)
    protected SourceFingerprintV2AgentValue sourceFingerprintV2AgentValue;
}