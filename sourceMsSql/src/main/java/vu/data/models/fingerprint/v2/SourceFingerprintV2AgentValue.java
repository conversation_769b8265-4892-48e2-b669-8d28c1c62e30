package vu.data.models.fingerprint.v2;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Getter
@Table(name = "fingerprint_agent_values")
public class SourceFingerprintV2AgentValue{

    @Id
    private long id;

    @Column(name = "id_client_hash")
    private long idClientHash;

    private String ua;

    @Column(name = "browser_name")
    private String browserName;

    @Column(name = "browser_major")
    private String browserMajor;

    @Column(name = "browser_version")
    private String browserVersion;

    @Column(name = "cpu_architecture")
    private String cpuArchitecture;

    @Column(name = "device_type")
    private String deviceType;

    @Column(name = "device_model")
    private String deviceModel;

    @Column(name = "device_vendor")
    private String deviceVendor;

    @Column(name = "engine_name")
    private String engineName;

    @Column(name = "os_name")
    private String osName;

    @Column(name = "os_version")
    private String osVersion;

    @Column(name = "engine_version")
    private String engineVersion;
}
