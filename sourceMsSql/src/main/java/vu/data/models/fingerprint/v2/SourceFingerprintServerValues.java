package vu.data.models.fingerprint.v2;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Getter
@Table(name = "fingerprint_server_values")
public class SourceFingerprintServerValues{

    @Id
    private long id;
    private String hash;
    @Column(name = "timestamp_utc")
    private String timeStampUtc;
    @Column(name = "http2", length = 4000)
    private String http2Fingerprint;
    @Column(name = "http2_hash")
    private String http2FingerprintHash;
    @Column(name = "tls", length = 4000)
    private String tlsFingerprint;
    @Column(name = "tls_hash")
    private String tlsFingerprintHash;
    @Column(name = "tls_ssl_greased")
    private String tlsFingerprintSslGreased;
    @Column(name = "headers", length = 4000)
    private String headers;
    @Column(name = "header_hash")
    private String headerSignature;
    private String etag;
    @Column(name = "etag_first_request")
    private String etagFirstRequest;
    @Column(name = "cookie", length = 60)
    String cookie;
    @Column(name = "ip", length = 32)
    private String ip;

}