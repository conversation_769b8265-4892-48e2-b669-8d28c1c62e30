package vu.data.models.fingerprint;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
@Getter
@Entity
@Table(name = "fingerprintdevicevalues")
public class SourceFingerprintDeviceValue{

    @Id
    private long id;

    @Column(name = "idhash")
    private Long hashId;

    @Column(name = "randomseed")
    private String randomSeed;

    @Column(name = "deviceid")
    private String deviceId;

    @Column(name = "devicename")
    private String deviceName;

    @Column(name = "devicemanufacturer")
    private String deviceManufacturer;

    @Column(name = "deviceresolution")
    private String deviceResolution;

    @Column(name = "isrooted")
    private boolean isRooted;

    @Column(name = "isfingerprintcompatible")
    private boolean isFingerprintCompatible;

    @Column(name = "isgpscompatible")
    private boolean isGPSCompatible;

    @Column(name = "osname")
    private String osName;

    @Column(name = "osversion")
    private String osVersion;

    @Column(name = "apilevel")
    private int apiLevel;

}