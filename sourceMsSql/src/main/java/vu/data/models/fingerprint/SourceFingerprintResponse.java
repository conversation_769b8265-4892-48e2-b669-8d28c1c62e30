package vu.data.models.fingerprint;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Getter
@Entity
@Table(name = "fingerprintresponses")
public class SourceFingerprintResponse{
    @Id
    private long id;

    @Column(name = "idUser")
    private String userId;

    @Column(name = "resultCode")
    private int resultCode;

    @Column(name = "accuracyPercent")
    private double accuracyPercent;

    @Column(name = "isHashKnown")
    private boolean hashKnown;

    @Column(name = "creationdate")
    private Date creationDate;
}
