package vu.data.models.fingerprint.v2;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Getter
@Table(name = "fingerprint_ip_geo_data")
public class SourceFingerprintIpGeoData{

    @Id
    private long id;

    @Column(name = "idUser")
    private String userId;

    @Column(name = "ip", length = 32)
    private String ip;

    @Column(name = "accuracy_radius")
    private int accuracyRadius;
    private double latitude;
    private double longitude;
    private String country;
    private String city;
    private String subdivisions;
}