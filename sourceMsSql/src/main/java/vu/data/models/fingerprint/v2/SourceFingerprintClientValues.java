package vu.data.models.fingerprint.v2;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Getter
@Table(name = "fingerprint_client_values")
public class SourceFingerprintClientValues{

    @Id
    private long id;

    @Column(name = "id_hash")
    private long hashId;

    @Column(name = "localstorageid")
    private String localStorageId;

    private String language;

    private String timezone;

    private String vendor;

    @Column(name = "vendor_flavors")
    private String vendorFlavors;

    @Column(name = "has_session_storage")
    private boolean hasSessionStorage;

    @Column(name = "has_local_storage")
    private boolean hasLocalStorage;

    @Column(name = "has_indexed_db")
    private boolean hasIndexedDb;

    @Column(name = "has_open_database")
    private boolean hasOpenDatabase;

    @Column(name = "cookies_enabled")
    private boolean cookiesEnabled;

    @Column(name = "navigator_platform")
    private String navigatorPlatform;

    @Column(name = "extended_fonts", length = 4000)
    private String extendedFonts;

    @Column(length = 4000)
    private String fonts;

    @Column(name = "fonts_pref_default")
    private double _default;

    @Column(name = "font_pref_apple")
    private double apple;
    @Column(name = "font_pref_serif")
    private double serif;
    @Column(name = "font_pref_sans")
    private double sans;
    @Column(name = "font_pref_mono")
    private double mono;
    @Column(name = "font_pref_min")
    private double min;
    @Column(name = "font_pref_system")
    private double system;

    private double audio;

    @Column(name = "dom_blocker")
    private String domBlocker;

    @Column(name = "maths_acos")
    private double acos;
    @Column(name = "maths_acosh")
    private double acosh;
    @Column(name = "maths_acosh_pf")
    private double acoshPf;
    @Column(name = "maths_asin")
    private double asin;
    @Column(name = "maths_asinh")
    private double asinh;
    @Column(name = "maths_asin_pf")
    private double asinhPf;
    @Column(name = "maths_atanh")
    private double atanh;
    @Column(name = "maths_atanh_pf")
    private double atanhPf;
    @Column(name = "maths_atan")
    private double atan;
    @Column(name = "maths_sin")
    private double sin;
    @Column(name = "maths_sinh")
    private double sinh;
    @Column(name = "maths_sinh_pf")
    private double sinhPf;
    @Column(name = "maths_cos")
    private double cos;
    @Column(name = "maths_cosh")
    private double cosh;
    @Column(name = "maths_cosh_pf")
    private double coshPf;
    @Column(name = "maths_tan")
    private double tan;
    @Column(name = "maths_tanh")
    private double tanh;
    @Column(name = "maths_tanh_pf")
    private double tanhPf;
    @Column(name = "maths_exp")
    private double exp;
    @Column(name = "maths_expm1")
    private double expm1;
    @Column(name = "maths_expm1_pf")
    private double expm1Pf;
    @Column(name = "maths_log1p")
    private double log1p;
    @Column(name = "maths_log1p_pf")
    private double log1pPf;
    @Column(name = "maths_pow_pi")
    private double powPI;

    @Column(name = "max_touch_points")
    private int maxTouchPoints;
    @Column(name = "touch_event")
    private boolean touchEvent;
    @Column(name = "touch_start")
    private boolean touchStart;

    @Column(name = "logical_processors")
    private int logicalProcessors;
    @Column(name = "memory")
    private double memory;
    @Column(name = "gpu", length = 4000)
    private String gpu;

    @Column(name = "color_depth")
    private int colorDepth;
    @Column(name = "screen_width")
    private int screenWidth;

    @Column(name = "screen_height")
    private int screenHeight;
    @Column(name = "frame_size")
    private String frameSize;

    @Column(name = "crc_geometry")
    private String geometry;
    @Column(name = "crc_text")
    private String text;
    @Column(name = "crc_webgl")
    private String webgl;

    @Column(name = "color_gamut")
    private String colorGamut;
    @Column(name = "inverted_colors")
    private String invertedColors;
    @Column(name = "forced_colors")
    private boolean forcedColors;
    @Column(name = "monochrome")
    private int monochrome;
    @Column(name = "contrast")
    private int contrast;
    @Column(name = "hdr")
    private boolean hdr;
    @Column(name = "reduced_motion")
    private boolean reducedMotion;
}