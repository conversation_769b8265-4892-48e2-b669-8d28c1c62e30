package vu.data.models.fingerprint.v2;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Table(name = "fingerprintAdditionalData")
public class SourceFingerprintAdditionalData{

    @Id
    private long id;

    @Column(name = "iduser")
    private String userId;

    @Column(name = "idhash")
    private long hashId;

    @Column(name = "ip4")
    private String ip4;

    @Column(name = "timestamp")
    private Date creationDate;

    @Column(name = "idapikey")
    private Integer apikeyId;
}