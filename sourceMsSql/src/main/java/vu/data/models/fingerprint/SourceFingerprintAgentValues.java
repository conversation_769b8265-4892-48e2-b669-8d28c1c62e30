package vu.data.models.fingerprint;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Getter
@Table(name = "fingerprintagentvalues")
public class SourceFingerprintAgentValues{

    @Id
    private long id;

    @Column(name = "idhash")
    private Long hashId;

    private String ua;

    @Column(name = "\"browserName\"")
    private String browserName;

    @Column(name = "\"browserMajor\"")
    private int browserMajor;

    @Column(name = "\"browserVersion\"")
    private String browserVersion;

    @Column(name = "\"cpuArchitecture\"")
    private String cpuArchitecture;

    @Column(name = "\"deviceType\"")
    private String deviceType;

    @Column(name = "\"deviceModel\"")
    private String deviceModel;

    @Column(name = "\"deviceVendor\"")
    private String deviceVendor;

    @Column(name = "\"engineName\"")
    private String engineName;

    @Column(name = "\"osName\"")
    private String osName;

    @Column(name = "\"engineVersion\"")
    private String engineVersion;

    @Column(name = "\"osVersion\"")
    private String osVersion;
}
