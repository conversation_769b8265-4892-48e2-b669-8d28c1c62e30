ARG MAVEN_PROFILE

FROM maven:3.8.6-openjdk-11 AS build

WORKDIR /app

# Copy source directories for local modules and main application
COPY sourceMsSql /app/sourceMsSql
COPY sourcePgSql /app/sourcePgSql
COPY src /app/src

# Build and install the required modules from local directories
RUN mvn -f sourceMsSql/pom.xml clean install -DskipTests
RUN mvn -f sourcePgSql/pom.xml clean install -DskipTests

COPY pom.xml .
RUN mvn dependency:go-offline

# Compile and package the main application using the Maven profile passed in via build argument
RUN mvn clean package -P "${MAVEN_PROFILE}" -DskipTests

FROM vusecuritydev.azurecr.io/alpine-java11-springboot:108

COPY --from=build /app/target/*.jar app.jar

ENTRYPOINT ["java", "-jar", "app.jar"]
