spring:
  application:
    name: migration
  jpa:
    properties:
      hibernate:
        naming:
          physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
          implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
        order_inserts: true
        order_updates: true
        jdbc:
          batch_versioned_data: true
          batch_size: ${BATCH_SIZE:1000}
    hibernate:
      ddl-auto: none
    show-sql: ${SHOW_SQL:false}
  source:
    url: ${SOURCE_URL_DB}
    username: ${SOURCE_USER_DB}
    password: ${SOURCE_PASSWORD_DB}
    dialect: ${SOURCE_DIALECT:org.hibernate.dialect.PostgreSQLDialect}
    extraParams: ${SOURCE_EXTRA_PARAMS:}
    read-only: true
    hikari:
      maximum-pool-size: ${SOURCE_CONNECTION_POOL_DB:10}
      idle-timeout: ${SOURCE_IDLE_TIMEOUT_DB:600000}
      minimum-idle: ${SOURCE_IDLE_CONNECTION_DB:2}

  target:
    url: ${TARGET_URL_DB}
    username: ${TARGET_USER_DB}
    password: ${TARGET_PASSWORD_DB}
    dialect: ${TARGET_DIALECT:org.hibernate.dialect.SQLServerDialect}
    extraParams: ${TARGET_EXTRA_PARAMS:}
    hikari:
      maximum-pool-size: ${TARGET_CONNECTION_POOL_DB:10}
      idle-timeout: ${TARGET_IDLE_TIMEOUT_DB:600000}
      minimum-idle: ${TARGET_IDLE_CONNECTION_DB:2}

  cep:
    url: ${CEP_URL_DB}
    username: ${CEP_USER_DB}
    password: ${CEP_PASSWORD_DB}
    dialect: ${CEP_DIALECT:org.hibernate.dialect.SQLServerDialect}
    extraParams: ${CEP_EXTRA_PARAMS:}
    hikari:
      maximum-pool-size: ${CEP_CONNECTION_POOL_DB:10}
      idle-timeout: ${CEP_IDLE_TIMEOUT_DB:600000}
      minimum-idle: ${CEP_IDLE_CONNECTION_DB:2}

app:
  version: @app.version@
  build:
    date: @build.date@
    version: @build.version@
  migration:
    chunk-size: ${CHUNK_SIZE:10000}
    parallelism: ${PARALLELISM:1}
    source-fetch-size: ${SOURCE_FETCH_SIZE:1000}

logging:
  level:
    root: INFO
