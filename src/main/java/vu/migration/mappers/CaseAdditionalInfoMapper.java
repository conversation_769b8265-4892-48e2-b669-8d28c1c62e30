package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.fraud.cm.SourceCaseAdditionalInfo;
import vu.migration.target.entities.fraud.cm.CaseAdditionalInfo;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CaseAdditionalInfoMapper{
    CaseAdditionalInfo toTargetCaseAdditionalInfo(SourceCaseAdditionalInfo source);

    List<CaseAdditionalInfo> toTargetCaseAdditionalInfoList(List<SourceCaseAdditionalInfo> sourceList);
}
