package vu.migration.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import vu.data.models.fraud.transaction.SourceTransactionEntity;
import vu.migration.target.entities.fraud.transaction.TransactionEntity;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TransactionMapper{
    @Mapping(target = "caseStatusId", expression = "java(source.getCaseStatusId() == 0 ? 6 : source.getCaseStatusId())")
    TransactionEntity toTargetTransaction(SourceTransactionEntity source);

    List<TransactionEntity> toTargetTransactionList(List<SourceTransactionEntity> sourceList);

}
