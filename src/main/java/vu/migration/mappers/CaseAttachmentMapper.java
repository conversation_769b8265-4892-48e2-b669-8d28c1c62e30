package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.fraud.cm.SourceCaseAttachment;
import vu.migration.target.entities.fraud.cm.CaseAttachment;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CaseAttachmentMapper{
    CaseAttachment toTargetCaseAttachment(SourceCaseAttachment source);

    List<CaseAttachment> toTargetCaseAttachmentList(List<SourceCaseAttachment> sourceList);
}
