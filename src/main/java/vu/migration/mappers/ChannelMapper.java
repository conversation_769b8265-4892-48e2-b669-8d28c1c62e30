package vu.migration.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import vu.data.models.administration.settings.channel.SourceChannel;
import vu.migration.target.entities.administration.settings.channel.Channel;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ChannelMapper{
    @Mapping(target = "ignoreRules", constant = "false")
    Channel toChannel(SourceChannel source);

    List<Channel> toChannelList(List<SourceChannel> sources);

    @Mapping(target = "ignoreRules", constant = "false")
    void updateChannelFromSource(@MappingTarget Channel target,SourceChannel source);
}
