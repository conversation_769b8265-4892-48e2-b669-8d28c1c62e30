package vu.migration.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import vu.data.models.fraud.transaction.SourceAdditionalParametersTransaction;
import vu.migration.target.entities.fraud.transaction.AdditionalParametersTransaction;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AdditionalParametersTransactionMapper{
    @Mapping(source = "field1", target = "field1", qualifiedByName = "truncateTo250")
    @Mapping(source = "field2", target = "field2", qualifiedByName = "truncateTo250")
    @Mapping(source = "field3", target = "field3", qualifiedByName = "truncateTo250")
    @Mapping(source = "field4", target = "field4", qualifiedByName = "truncateTo250")
    @Mapping(source = "field5", target = "field5", qualifiedByName = "truncateTo250")
    @Mapping(source = "field6", target = "field6", qualifiedByName = "truncateTo250")
    @Mapping(source = "field7", target = "field7", qualifiedByName = "truncateTo250")
    @Mapping(source = "field8", target = "field8", qualifiedByName = "truncateTo250")
    @Mapping(source = "field9", target = "field9", qualifiedByName = "truncateTo250")
    @Mapping(source = "field10", target = "field10", qualifiedByName = "truncateTo250")
    @Mapping(source = "field11", target = "field11", qualifiedByName = "truncateTo250")
    @Mapping(source = "field12", target = "field12", qualifiedByName = "truncateTo250")
    @Mapping(source = "field13", target = "field13", qualifiedByName = "truncateTo250")
    @Mapping(source = "field14", target = "field14", qualifiedByName = "truncateTo250")
    @Mapping(source = "field15", target = "field15", qualifiedByName = "truncateTo250")
    @Mapping(source = "field16", target = "field16", qualifiedByName = "truncateTo250")
    @Mapping(source = "field17", target = "field17", qualifiedByName = "truncateTo250")
    @Mapping(source = "field18", target = "field18", qualifiedByName = "truncateTo250")
    @Mapping(source = "field19", target = "field19", qualifiedByName = "truncateTo250")
    @Mapping(source = "field20", target = "field20", qualifiedByName = "truncateTo250")
    @Mapping(source = "field21", target = "field21", qualifiedByName = "truncateTo250")
    @Mapping(source = "field22", target = "field22", qualifiedByName = "truncateTo250")
    @Mapping(source = "field23", target = "field23", qualifiedByName = "truncateTo250")
    @Mapping(source = "field24", target = "field24", qualifiedByName = "truncateTo250")
    @Mapping(source = "field25", target = "field25", qualifiedByName = "truncateTo250")
    @Mapping(source = "field26", target = "field26", qualifiedByName = "truncateTo250")
    @Mapping(source = "field27", target = "field27", qualifiedByName = "truncateTo250")
    @Mapping(source = "field28", target = "field28", qualifiedByName = "truncateTo250")
    @Mapping(source = "field29", target = "field29", qualifiedByName = "truncateTo250")
    @Mapping(source = "field30", target = "field30", qualifiedByName = "truncateTo250")
    @Mapping(source = "field31", target = "field31", qualifiedByName = "truncateTo250")
    @Mapping(source = "field32", target = "field32", qualifiedByName = "truncateTo250")
    @Mapping(source = "field33", target = "field33", qualifiedByName = "truncateTo250")
    @Mapping(source = "field34", target = "field34", qualifiedByName = "truncateTo250")
    @Mapping(source = "field35", target = "field35", qualifiedByName = "truncateTo250")
    @Mapping(source = "field36", target = "field36", qualifiedByName = "truncateTo250")
    @Mapping(source = "field37", target = "field37", qualifiedByName = "truncateTo250")
    @Mapping(source = "field38", target = "field38", qualifiedByName = "truncateTo250")
    @Mapping(source = "field39", target = "field39", qualifiedByName = "truncateTo250")
    @Mapping(source = "field40", target = "field40", qualifiedByName = "truncateTo250")
    @Mapping(source = "field41", target = "field41", qualifiedByName = "truncateTo250")
    @Mapping(source = "field42", target = "field42", qualifiedByName = "truncateTo250")
    @Mapping(source = "field43", target = "field43", qualifiedByName = "truncateTo250")
    @Mapping(source = "field44", target = "field44", qualifiedByName = "truncateTo250")
    @Mapping(source = "field45", target = "field45", qualifiedByName = "truncateTo250")
    @Mapping(source = "field46", target = "field46", qualifiedByName = "truncateTo250")
    @Mapping(source = "field47", target = "field47", qualifiedByName = "truncateTo250")
    @Mapping(source = "field48", target = "field48", qualifiedByName = "truncateTo250")
    @Mapping(source = "field49", target = "field49", qualifiedByName = "truncateTo250")
    @Mapping(source = "field50", target = "field50", qualifiedByName = "truncateTo250")
    @Mapping(source = "field51", target = "field51", qualifiedByName = "truncateTo250")
    @Mapping(source = "field52", target = "field52", qualifiedByName = "truncateTo250")
    @Mapping(source = "field53", target = "field53", qualifiedByName = "truncateTo250")
    @Mapping(source = "field54", target = "field54", qualifiedByName = "truncateTo250")
    @Mapping(source = "field55", target = "field55", qualifiedByName = "truncateTo250")
    @Mapping(source = "field56", target = "field56", qualifiedByName = "truncateTo250")
    @Mapping(source = "field57", target = "field57", qualifiedByName = "truncateTo250")
    @Mapping(source = "field58", target = "field58", qualifiedByName = "truncateTo250")
    @Mapping(source = "field59", target = "field59", qualifiedByName = "truncateTo250")
    @Mapping(source = "field60", target = "field60", qualifiedByName = "truncateTo250")
    @Mapping(source = "field61", target = "field61", qualifiedByName = "truncateTo250")
    @Mapping(source = "field62", target = "field62", qualifiedByName = "truncateTo250")
    @Mapping(source = "field63", target = "field63", qualifiedByName = "truncateTo250")
    @Mapping(source = "field64", target = "field64", qualifiedByName = "truncateTo250")
    @Mapping(source = "field65", target = "field65", qualifiedByName = "truncateTo250")
    @Mapping(source = "field66", target = "field66", qualifiedByName = "truncateTo250")
    @Mapping(source = "field67", target = "field67", qualifiedByName = "truncateTo250")
    @Mapping(source = "field68", target = "field68", qualifiedByName = "truncateTo250")
    @Mapping(source = "field69", target = "field69", qualifiedByName = "truncateTo250")
    @Mapping(source = "field70", target = "field70", qualifiedByName = "truncateTo250")
    @Mapping(source = "field71", target = "field71", qualifiedByName = "truncateTo250")
    @Mapping(source = "field72", target = "field72", qualifiedByName = "truncateTo250")
    @Mapping(source = "field73", target = "field73", qualifiedByName = "truncateTo250")
    @Mapping(source = "field74", target = "field74", qualifiedByName = "truncateTo250")
    @Mapping(source = "field75", target = "field75", qualifiedByName = "truncateTo250")
    @Mapping(source = "field76", target = "field76", qualifiedByName = "truncateTo250")
    @Mapping(source = "field77", target = "field77", qualifiedByName = "truncateTo250")
    @Mapping(source = "field78", target = "field78", qualifiedByName = "truncateTo250")
    @Mapping(source = "field79", target = "field79", qualifiedByName = "truncateTo250")
    @Mapping(source = "field80", target = "field80", qualifiedByName = "truncateTo250")
    @Mapping(source = "field81", target = "field81", qualifiedByName = "truncateTo250")
    @Mapping(source = "field82", target = "field82", qualifiedByName = "truncateTo250")
    @Mapping(source = "field83", target = "field83", qualifiedByName = "truncateTo250")
    @Mapping(source = "field84", target = "field84", qualifiedByName = "truncateTo250")
    @Mapping(source = "field85", target = "field85", qualifiedByName = "truncateTo250")
    @Mapping(source = "field86", target = "field86", qualifiedByName = "truncateTo250")
    @Mapping(source = "field87", target = "field87", qualifiedByName = "truncateTo250")
    @Mapping(source = "field88", target = "field88", qualifiedByName = "truncateTo250")
    @Mapping(source = "field89", target = "field89", qualifiedByName = "truncateTo250")
    @Mapping(source = "field90", target = "field90", qualifiedByName = "truncateTo250")
    @Mapping(source = "field91", target = "field91", qualifiedByName = "truncateTo250")
    @Mapping(source = "field92", target = "field92", qualifiedByName = "truncateTo250")
    @Mapping(source = "field93", target = "field93", qualifiedByName = "truncateTo250")
    @Mapping(source = "field94", target = "field94", qualifiedByName = "truncateTo250")
    @Mapping(source = "field95", target = "field95", qualifiedByName = "truncateTo250")
    @Mapping(source = "field96", target = "field96", qualifiedByName = "truncateTo250")
    @Mapping(source = "field97", target = "field97", qualifiedByName = "truncateTo250")
    @Mapping(source = "field98", target = "field98", qualifiedByName = "truncateTo250")
    @Mapping(source = "field99", target = "field99", qualifiedByName = "truncateTo250")
    @Mapping(source = "field100", target = "field100", qualifiedByName = "truncateTo250")
    AdditionalParametersTransaction toTargetAdditionalParametersTransaction(SourceAdditionalParametersTransaction source);

    List<AdditionalParametersTransaction> toTargetAdditionalParametersTransactionList(List<SourceAdditionalParametersTransaction> sourceList);

    @Named("truncateTo250")
    static String truncateTo250(String value) {
        if (value == null) return null;
        return value.length() > 250 ? value.substring(0, 250) : value;
    }
}
