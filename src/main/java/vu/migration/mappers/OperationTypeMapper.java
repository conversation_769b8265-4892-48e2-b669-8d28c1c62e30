package vu.migration.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import vu.data.models.administration.settings.operation.SourceOperationType;
import vu.migration.service.fraud.event.EventTypeEnumName;
import vu.migration.target.entities.administration.settings.operation.OperationType;

@Mapper(componentModel = "spring")
public abstract class OperationTypeMapper{
    public void updateOperationTypeFromSource(@MappingTarget OperationType target,SourceOperationType source) {
        if (target.getId() == null) {
            target.setId(source.getId());
        }

        String name = normalizedName(source.getName());
        target.setName(name);
        target.setDescription(normalizedDescription(source.getName(), source.getDescription()));
        target.setEnabled(source.getEnabled());
        target.setEventTypeId(getEventTypeId(source.getEventTypeId()));
    }

    public String normalizedName(String name) {
        return switch (name) {
            case "BrowserFingerprint", "Fingerprint" -> "BrowserFingerprint V1";
            case "BrowserFingerprintV2" -> "BrowserFingerprint V2";
            default -> name;
        };
    }

    private String normalizedDescription(String name, String originalDescription) {
        return switch (name) {
            case "BrowserFingerprint", "Fingerprint" -> "BrowserFingerprint V1";
            case "BrowserFingerprintV2" -> "browser fingerprint";
            default -> originalDescription;
        };
    }

    private int getEventTypeId(int eventTypeName) {
        return switch (eventTypeName) {
            case 2 -> EventTypeEnumName.EVENT;
            case 1 -> EventTypeEnumName.TRANSACTION;
            default -> 0;
        };
    }
}
