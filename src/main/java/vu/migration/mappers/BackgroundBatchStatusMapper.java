package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.administration.list.SourceBackgroundBatchStatus;
import vu.migration.target.entities.administration.list.BackgroundBatchStatus;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BackgroundBatchStatusMapper{
    BackgroundBatchStatus toTarget(SourceBackgroundBatchStatus source);

    List<BackgroundBatchStatus> toTargetList(List<SourceBackgroundBatchStatus> sources);
}
