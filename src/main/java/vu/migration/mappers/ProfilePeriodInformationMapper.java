package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.report.profile.SourceProfilePeriodInformation;
import vu.migration.target.entities.profile.ProfilePeriodInformation;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProfilePeriodInformationMapper{
    ProfilePeriodInformation toTargetProfilePeriodInformation(SourceProfilePeriodInformation source);

    List<ProfilePeriodInformation> toTargetProfilePeriodInformationList(List<SourceProfilePeriodInformation> sourceList);
}
