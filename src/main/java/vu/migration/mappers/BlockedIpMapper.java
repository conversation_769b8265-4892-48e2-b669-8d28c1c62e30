package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.administration.list.SourceBlockedIpValues;
import vu.migration.target.entities.administration.list.BlockedIpValues;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BlockedIpMapper{
    BlockedIpValues toEntity(SourceBlockedIpValues source);

    List<BlockedIpValues> toEntityList(List<SourceBlockedIpValues> sources);
}
