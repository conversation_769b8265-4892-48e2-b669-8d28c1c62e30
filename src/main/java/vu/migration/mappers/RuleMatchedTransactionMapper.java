package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.fraud.transaction.SourceRuleMatchedTransaction;
import vu.migration.target.entities.fraud.transaction.RuleMatchedTransaction;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RuleMatchedTransactionMapper{
    RuleMatchedTransaction toTargetRuleMatchedTransaction(SourceRuleMatchedTransaction source);

    List<RuleMatchedTransaction> toTargetRuleMatchedTransactionList(List<SourceRuleMatchedTransaction> sourceList);
}
