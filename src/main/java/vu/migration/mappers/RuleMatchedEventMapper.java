package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.fraud.event.SourceRuleMatchedEvent;
import vu.migration.target.entities.fraud.event.RuleMatchedEvent;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RuleMatchedEventMapper{
    RuleMatchedEvent toTargetRuleMatchedEvent(SourceRuleMatchedEvent source);

    List<RuleMatchedEvent> toTargetRuleMatchedEventList(List<SourceRuleMatchedEvent> sourceList);
}
