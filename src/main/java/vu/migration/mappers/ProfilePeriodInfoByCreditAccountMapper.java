package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.report.profile.SourceProfilePeriodInfoByCreditAccount;
import vu.migration.target.entities.profile.ProfilePeriodInfoByCreditAccount;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProfilePeriodInfoByCreditAccountMapper{
    ProfilePeriodInfoByCreditAccount toTargetProfilePeriodInfoByCreditAccount(SourceProfilePeriodInfoByCreditAccount source);

    List<ProfilePeriodInfoByCreditAccount> toTargetProfilePeriodInfoByCreditAccountList(List<SourceProfilePeriodInfoByCreditAccount> sourceList);
}
