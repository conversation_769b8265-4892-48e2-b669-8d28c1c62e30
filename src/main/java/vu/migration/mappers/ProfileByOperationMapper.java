package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.report.profile.SourceProfileByOperation;
import vu.migration.target.entities.profile.ProfileByOperation;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProfileByOperationMapper{
    ProfileByOperation toTargetProfileByOperation(SourceProfileByOperation source);

    List<ProfileByOperation> toTargetProfileByOperationList(List<SourceProfileByOperation> sourceList);
}
