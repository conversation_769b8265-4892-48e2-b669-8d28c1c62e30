package vu.migration.mappers;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import vu.data.models.administration.settings.parameter.SourceParameterChannelOperation;
import vu.migration.service.administration.settings.parameter.IParameterService;
import vu.migration.target.entities.administration.settings.parameter.ParameterChannelOperation;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ParameterChannelOperationMapper{
    @Mapping(target = "parameterId", expression = "java(parameterService.getParameterBySourceParameterId(source.getParameterId()).getId())")
    @Mapping(target = "channelId", source = "channelId")
    @Mapping(target = "operationTypeId", source = "operationTypeId")
    @Mapping(target = "enabled", source = "enabled")
    ParameterChannelOperation toTargetParameterChannelOperation(SourceParameterChannelOperation source, @Context IParameterService parameterService);

    List<ParameterChannelOperation> toTargetParameterChannelOperationList(List<SourceParameterChannelOperation> sourceList,@Context IParameterService parameterService);
}
