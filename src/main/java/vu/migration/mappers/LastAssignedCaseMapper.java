package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.fraud.cm.SourceLastAssignedCase;
import vu.migration.target.entities.fraud.cm.LastAssignedCase;

import java.util.List;

@Mapper(componentModel = "spring")
public interface LastAssignedCaseMapper{
    LastAssignedCase toTargetLastAssignedCase(SourceLastAssignedCase source);

    List<LastAssignedCase> toTargetLastAssignedCaseList(List<SourceLastAssignedCase> sourceList);
}
