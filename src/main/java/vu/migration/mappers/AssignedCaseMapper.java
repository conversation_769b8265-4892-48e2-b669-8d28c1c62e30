package vu.migration.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import vu.data.models.fraud.cm.SourceAssignedCase;
import vu.migration.target.entities.fraud.cm.AssignedCase;
import vu.migration.target.entities.fraud.event.CaseStatusEnumName;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AssignedCaseMapper{
    @Mapping(target = "assignedTo", expression = "java(source.getAssignedTo() == null ? 1L : source.getAssignedTo())")
    @Mapping(target = "assignedBy", expression = "java(source.getAssignedBy() == null ? 1L : source.getAssignedBy())")
    @Mapping(target = "businessGroupId", expression = "java(source.getBusinessGroupId() == null ? 1 : source.getBusinessGroupId())")
    @Mapping(target = "caseStatus", source = "caseStatus", qualifiedByName = "mapCaseStatusEnum")
    AssignedCase toTargetAssignedCase(SourceAssignedCase source);

    List<AssignedCase> toTargetAssignedCaseList(List<SourceAssignedCase> sourceList);

    @Named("mapCaseStatusEnum")
    default CaseStatusEnumName.EnumCaseStatus mapCaseStatusEnum(
            vu.data.models.fraud.event.CaseStatusEnumName.Enum source) {

        if (source == null) return CaseStatusEnumName.EnumCaseStatus.NONE;

        try {
            return CaseStatusEnumName.EnumCaseStatus.valueOf(source.name());
        } catch (IllegalArgumentException e) {
            return CaseStatusEnumName.EnumCaseStatus.NONE;
        }
    }
}
