package vu.migration.mappers;

import org.mapstruct.Mapper;
import vu.data.models.administration.list.SourceCustomList;
import vu.migration.target.entities.administration.list.CustomList;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CustomListMapper{
    CustomList toTarget(SourceCustomList source);

    List<CustomList> toEntityList(List<SourceCustomList> sourceList);
}
