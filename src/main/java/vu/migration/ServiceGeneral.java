package vu.migration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vu.migration.service.*;
import vu.migration.service.audit.AuditLogService;
import vu.migration.service.audit.IAuditLogService;
import vu.migration.util.ParseUtil;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class ServiceGeneral {
    private static final Logger logger = LoggerFactory.getLogger(ServiceGeneral.class);

    private final ConfigurationBasicService configurationBasicService;
    private final TransactionManagementService transactionManagementService;
    private final EventManagementService eventManagementService;
    private final ProfileManagementService profileManagementService;
    private final FingerprintManagementService fingerprintManagementService;
    private final BusinessManagementService businessManagementService;
    private final IAuditLogService iAuditLogService;

    public ServiceGeneral(ConfigurationBasicService configurationBasicService,TransactionManagementService transactionManagementService,EventManagementService eventManagementService,ProfileManagementService profileManagementService,FingerprintManagementService fingerprintManagementService,BusinessManagementService businessManagementService,IAuditLogService iAuditLogService){
        this.configurationBasicService=configurationBasicService;
        this.transactionManagementService=transactionManagementService;
        this.eventManagementService=eventManagementService;
        this.profileManagementService=profileManagementService;
        this.fingerprintManagementService=fingerprintManagementService;
        this.businessManagementService=businessManagementService;
        this.iAuditLogService=iAuditLogService;
    }

    private static final String SUCCESSFUL = "Copy successful";
    private static final String FAIL = "Unexpected error: ";

    public void copyAll() {
        try {
            config();
            eventManagementService.copyEvent(
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());
            transactionManagementService.copyTransactionEntity(Optional.empty(),
                    Optional.empty(),
                    Optional.empty(),
                    Optional.empty());
            fingerprintManagementService.copyFingerprint(
                    Optional.empty(),
                    Optional.empty());
            profileManagementService.copyProfile(
                    Optional.empty(),
                    Optional.empty());
            businessManagementService.copyUserBusiness();
            iAuditLogService.copyAuditLog();
            System.out.println(SUCCESSFUL);
        } catch (Exception ex) {
            System.out.println(FAIL + ex.getMessage());
        }
    }

    public void copyConfiguration() {
        try {
            config();
            System.out.println(SUCCESSFUL);
        } catch (Exception ex) {
            System.out.println(FAIL + ex.getMessage());
        }
    }

    public void copyEventBYDate(String dateFrom,String dateTo) {
        try {
            Optional<LocalDateTime> dateTimeFrom = ParseUtil.parseDateStartOfDay(dateFrom);
            Optional<LocalDateTime> dateTimeTo = ParseUtil.parseDateEndOfDay(dateTo);
            eventManagementService.copyEvent(
                    dateTimeFrom,
                    dateTimeTo,
                    Optional.empty(),
                    Optional.empty());
            System.out.println(SUCCESSFUL);
        } catch (Exception ex) {
            System.out.println(FAIL + ex.getMessage());
        }
    }

    public void copyTransactionBYDate(String dateFrom,String dateTo) {
        try {
            Optional<LocalDateTime> dateTimeFrom = ParseUtil.parseDateStartOfDay(dateFrom);
            Optional<LocalDateTime> dateTimeTo = ParseUtil.parseDateEndOfDay(dateTo);

            transactionManagementService.copyTransactionEntity(
                    dateTimeFrom,
                    dateTimeTo,
                    Optional.empty(),
                    Optional.empty()
            );
            System.out.println(SUCCESSFUL);
        } catch (Exception ex) {
            System.out.println(FAIL + ex.getMessage());
        }
    }


    public void copyTransactionBYId(String idFrom,String idTo) {
        try {
            Optional<Long> idTransactionFrom = ParseUtil.parseOptionalLong(idFrom);
            Optional<Long> idTransactionTo = ParseUtil.parseOptionalLong(idTo);

            transactionManagementService.copyTransactionEntity(
                    Optional.empty(),
                    Optional.empty(),
                    idTransactionFrom,
                    idTransactionTo
            );
            System.out.println(SUCCESSFUL);
        } catch (Exception ex) {
            System.out.println(FAIL + ex.getMessage());
        }
    }

    public void copyFingerprintsBYDate(String dateFrom,String dateTo) {
        try {
            Optional<LocalDateTime> dateTimeFrom = ParseUtil.parseDateStartOfDay(dateFrom);
            Optional<LocalDateTime> dateTimeTo = ParseUtil.parseDateEndOfDay(dateTo);
            fingerprintManagementService.copyFingerprint(dateTimeFrom, dateTimeTo);
            System.out.println(SUCCESSFUL);
        } catch (Exception ex) {
            System.out.println(FAIL + ex.getMessage());
        }
    }


    public void copyUserProfilesBYDate(String dateFrom,String dateTo) {
        try {
            Optional<LocalDateTime> dateTimeFrom = ParseUtil.parseDateStartOfDay(dateFrom);
            Optional<LocalDateTime> dateTimeTo = ParseUtil.parseDateEndOfDay(dateTo);
            profileManagementService.copyProfile(dateTimeFrom, dateTimeTo);
            System.out.println(SUCCESSFUL);
        } catch (Exception ex) {
            System.out.println(FAIL + ex.getMessage());
        }
    }

    private void config(){
        configurationBasicService.updateSystemParameter();
        configurationBasicService.copyApiKey();
        configurationBasicService.copyAuthRole();
        configurationBasicService.copyAuthFaUser();
        configurationBasicService.copyChannel();
        configurationBasicService.copyOperationType();
        configurationBasicService.copyParameter();
        configurationBasicService.copyParameterChannelOperation();
        configurationBasicService.copyList();
        configurationBasicService.copyBusinessGroup();
        configurationBasicService.copyNotification();
        configurationBasicService.copyRules();
    }
}
