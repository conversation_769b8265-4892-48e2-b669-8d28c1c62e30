package vu.migration;

import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Scanner;

@Component
public class StartMigration implements CommandLineRunner{

    private final ServiceGeneral serviceGeneral;

    public StartMigration(ServiceGeneral serviceGeneral){
        this.serviceGeneral=serviceGeneral;
    }

    @Override
    public void run(String... args){
        System.out.println("=== Migration application for the fraud prevention program ===");

        if (args.length == 0 || args[0].equalsIgnoreCase("help")) {
            mostrarAyuda();
            return;
        }

        String operation = args[0];
        String[] params = Arrays.copyOfRange(args, 1, args.length);

        try {
            switch (operation) {
                case "all" -> {
                    if (params.length != 0) {
                        System.out.println("Use: all");
                    } else {
                        serviceGeneral.copyAll();
                    }
                }
                case "config" -> {
                    if (params.length != 0) {
                        System.out.println("Use: config");
                    } else {
                        serviceGeneral.copyConfiguration();
                    }
                }
                case "event" -> {
                    if (params.length != 2) {
                        System.out.println("Use: event <dateFrom> <dateTo>");
                    } else {
                        serviceGeneral.copyEventBYDate(params[0], params[1]);
                    }
                }
                case "transaction" -> {
                    if (params.length != 2) {
                        System.out.println("Use: transaction <dateFrom> <dateTo>");
                    } else {
                        serviceGeneral.copyTransactionBYDate(params[0], params[1]);
                    }
                }
                case "idtransaction" -> {
                    if (params.length != 2) {
                        System.out.println("Use: idtransaction <idFrom> <idTo>");
                    } else {
                        serviceGeneral.copyTransactionBYId(params[0], params[1]);
                    }
                }
                case "profile" -> {
                    if (params.length != 2) {
                        System.out.println("Use: profile <dateFrom> <dateTo>");
                    } else {
                        serviceGeneral.copyUserProfilesBYDate(params[0], params[1]);
                    }
                }
                case "fingerprint" -> {
                    if (params.length != 2) {
                        System.out.println("Use: fingerprint <dateFrom> <dateTo>");
                    } else {
                        serviceGeneral.copyFingerprintsBYDate(params[0], params[1]);
                    }
                }
                default -> {
                    System.out.println("Unrecognized command. Type 'help' to see options.");
                }
            }
        } catch (Exception e) {
            System.out.println("Error executing the operation: " + e.getMessage());
        }

        System.out.println("Migration finished. Exiting.");
    }

    private void mostrarAyuda() {
        System.out.println("""
        ===============================================
                      Migration App
        ===============================================

        Available commands:

        • all
          Migrates all data, only recommended for databases with little history.
          Example: all
          
        • config
          Migrates configurations (including channels, operations, optional parameters, global parameters, rules, etc.)
          Example: config

        • event <dateFrom> <dateTo>
          Migrates non-transactional events between the dates from and to, inclusive.
          Example: event 2024-01-01 2025-01-01

        • transaction <dateFrom> <dateTo>
          Migrates transactions between the dates from and to, inclusive.
          Example: transaction 2024-01-01 2025-01-01
          
        • idtransaction <min> <max>
          Migrates transactions whose database identifiers are between min and max, inclusive.
          Example: idtransaction 0 360000000

        • profile <dateFrom> <dateTo>
          Migrates user profile data, including transactions between the dates from and to, inclusive.
          Ejemplo: profile 2024-01-01 2025-01-01
          
        • fingerprint <dateFrom> <dateTo>
          Migrates fingerprints used between the dates from and to, inclusive.
          Ejemplo: fingerprint 2024-01-01 2025-01-01

        • help
          Displays the list of commands.
          
        ===============================================
    """);
    }

}

