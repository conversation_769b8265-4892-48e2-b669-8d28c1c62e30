package vu.migration.util;

import jakarta.persistence.criteria.Predicate;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class SpecificationUtils{
    public static <T> Specification<T> buildRangeSpec(
            String columnToSearch,
            Optional<Long> fromId,
            Optional<Long> toId
    ) {
        Specification<T> spec = (root, query, cb) -> cb.conjunction();

        if (fromId.isPresent()) {
            spec = spec.and((root, query, cb) ->
                    cb.greaterThanOrEqualTo(root.get(columnToSearch), fromId.get()));
        }

        if (toId.isPresent()) {
            spec = spec.and((root, query, cb) ->
                    cb.lessThanOrEqualTo(root.get(columnToSearch), toId.get()));
        }

        return spec;
    }

    public static Pageable buildPageable(Optional<Long> fromId,Optional<Long> toId,int page,int size) {
        Sort sort = (fromId.isPresent() || toId.isPresent())
                ? Sort.by(Sort.Direction.DESC, "id")
                : Sort.by(Sort.Direction.ASC, "id");
        return PageRequest.of(page, size, sort);
    }

    public static <T> Specification<T> filteredByIdRange(
            String idField,
            String filterField,
            long lastSeenId,
            long fromId,
            int size,
            boolean ascending
    ) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            long effectiveFromId = Math.max(fromId, lastSeenId - size);

            predicates.add(cb.lessThan(root.get(filterField), lastSeenId));
            predicates.add(cb.greaterThanOrEqualTo(root.get(filterField), effectiveFromId));

            assert query != null;
            if (ascending) {
                query.orderBy(cb.asc(root.get(idField)));
            } else {
                query.orderBy(cb.desc(root.get(idField)));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}
