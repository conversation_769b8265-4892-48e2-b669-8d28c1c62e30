package vu.migration.util;

import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import vu.data.models.fingerprint.SourceFingerprintUserHashes;
import vu.migration.target.entities.fingerprint.FingerprintDeviceValue;
import vu.migration.target.entities.fingerprint.OperatingSystem;
import vu.migration.target.entities.fingerprint.browser.BrowserFingerprintType;
import vu.migration.target.entities.fingerprint.browser.FingerprintWebHash;
import vu.migration.target.entities.fingerprint.browser.agent.*;
import vu.migration.target.entities.fingerprint.browser.values.CrcCanvas;
import vu.migration.target.entities.fingerprint.browser.values.FingerprintValues;
import vu.migration.target.entities.fingerprint.browser.values.Resolution;
import vu.migration.target.entities.fingerprint.browser.values.Screen;

import java.util.Date;

@Data
@NoArgsConstructor
@Getter
public class FingerprintV1AndDeviceInfo{

    private long id;
    private String userId;
    private long clientHashId;
    @Enumerated(EnumType.ORDINAL)
    private SourceFingerprintUserHashes.Status status;
    private double accuracy;
    private Date lastCheckDate = new Date();
    private Date creationDate = new Date();
    private int apiKeyId;
    private int countUsed;
    private String hash;
    private Date hashCreationDate;

    // Device value info
    private Long deviceInfoId;
    private String randomSeed;
    private String deviceId;
    private String deviceName;
    private String deviceManufacturer;
    private String deviceResolution;
    private Boolean isRooted;
    private Boolean isFingerprintCompatible;
    private Boolean isGPSCompatible;
    private String osName;
    private String osVersion;
    private Integer apiLevel;

    // Fingerprint value info
    private Long fingerprintInfoId;
    private String language;
    private Integer colorDepth;
    private Integer screenHeight;
    private Integer screenWidth;
    private Integer availableHeight;
    private Integer availableWidth;
    private Integer timezoneOffset;
    private Boolean hasSessionStorage;
    private Boolean hasLocalStorage;
    private Boolean hasIndexedDb;
    private Boolean hasAddBehavior;
    private Boolean hasOpenDatabase;
    private String navigatorCpuClass;
    private String navigatorPlatform;
    private String doNotTrack;
    private String plugins;
    private Boolean hasAdBlock;
    private Boolean hasLiedLanguages;
    private Boolean hasLiedResolution;
    private Boolean hasLiedOs;
    private String fonts;
    private String crcCanvas;
    private String crcWebGl;

    // Fingerprint agent value info
    private Long fingerprintAgentInfoId;
    private String ua;
    private String browserName;
    private Integer browserMajor;
    private String browserVersion;
    private String cpuArchitecture;
    private String deviceType;
    private String deviceModel;
    private String deviceVendor;
    private String engineName;
    private String fingerprintAgentOsName;
    private String engineVersion;
    private String fingerprintAgentOsVersion;


    public FingerprintV1AndDeviceInfo(long id,String userId,long clientHashId,SourceFingerprintUserHashes.Status status,double accuracy,Date lastCheckDate,Date creationDate,int apiKeyId,int countUsed,String hash,Date hashCreationDate,Long deviceInfoId,String randomSeed,String deviceId,String deviceName,String deviceManufacturer,String deviceResolution,Boolean isRooted,Boolean isFingerprintCompatible,Boolean isGPSCompatible,String osName,String osVersion,Integer apiLevel,Long fingerprintInfoId,String language,Integer colorDepth,Integer screenHeight,Integer screenWidth,Integer availableHeight,Integer availableWidth,Integer timezoneOffset,Boolean hasSessionStorage,Boolean hasLocalStorage,Boolean hasIndexedDb,Boolean hasAddBehavior,Boolean hasOpenDatabase,String navigatorCpuClass,String navigatorPlatform,String doNotTrack,String plugins,Boolean hasAdBlock,Boolean hasLiedLanguages,Boolean hasLiedResolution,Boolean hasLiedOs,String fonts,String crcCanvas,String crcWebGl,Long fingerprintAgentInfoId,String ua,String browserName,Integer browserMajor,String browserVersion,String cpuArchitecture,String deviceType,String deviceModel,String deviceVendor,String engineName,String fingerprintAgentOsName,String engineVersion,String fingerprintAgentOsVersion){
        this.id=id;
        this.userId=userId;
        this.clientHashId=clientHashId;
        this.status=status;
        this.accuracy=accuracy;
        this.lastCheckDate=lastCheckDate;
        this.creationDate=creationDate;
        this.apiKeyId=apiKeyId;
        this.countUsed=countUsed;
        this.hash=hash;
        this.hashCreationDate=hashCreationDate;
        this.deviceInfoId=deviceInfoId;
        this.randomSeed=randomSeed;
        this.deviceId=deviceId;
        this.deviceName=deviceName;
        this.deviceManufacturer=deviceManufacturer;
        this.deviceResolution=deviceResolution;
        this.isRooted=isRooted;
        this.isFingerprintCompatible=isFingerprintCompatible;
        this.isGPSCompatible=isGPSCompatible;
        this.osName=osName;
        this.osVersion=osVersion;
        this.apiLevel=apiLevel;
        this.fingerprintInfoId=fingerprintInfoId;
        this.language=language;
        this.colorDepth=colorDepth;
        this.screenHeight=screenHeight;
        this.screenWidth=screenWidth;
        this.availableHeight=availableHeight;
        this.availableWidth=availableWidth;
        this.timezoneOffset=timezoneOffset;
        this.hasSessionStorage=hasSessionStorage;
        this.hasLocalStorage=hasLocalStorage;
        this.hasIndexedDb=hasIndexedDb;
        this.hasAddBehavior=hasAddBehavior;
        this.hasOpenDatabase=hasOpenDatabase;
        this.navigatorCpuClass=navigatorCpuClass;
        this.navigatorPlatform=navigatorPlatform;
        this.doNotTrack=doNotTrack;
        this.plugins=plugins;
        this.hasAdBlock=hasAdBlock;
        this.hasLiedLanguages=hasLiedLanguages;
        this.hasLiedResolution=hasLiedResolution;
        this.hasLiedOs=hasLiedOs;
        this.fonts=fonts;
        this.crcCanvas=crcCanvas;
        this.crcWebGl=crcWebGl;
        this.fingerprintAgentInfoId=fingerprintAgentInfoId;
        this.ua=ua;
        this.browserName=browserName;
        this.browserMajor=browserMajor;
        this.browserVersion=browserVersion;
        this.cpuArchitecture=cpuArchitecture;
        this.deviceType=deviceType;
        this.deviceModel=deviceModel;
        this.deviceVendor=deviceVendor;
        this.engineName=engineName;
        this.fingerprintAgentOsName=fingerprintAgentOsName;
        this.engineVersion=engineVersion;
        this.fingerprintAgentOsVersion=fingerprintAgentOsVersion;
    }

    public FingerprintDeviceValue getDeviceValue(){
        FingerprintDeviceValue fingerprintDeviceValue = new FingerprintDeviceValue();
        fingerprintDeviceValue.setRandomSeed(this.randomSeed);
        fingerprintDeviceValue.setDeviceId(this.deviceId);
        fingerprintDeviceValue.setDeviceName(this.deviceName);
        fingerprintDeviceValue.setDeviceManufacturer(this.deviceManufacturer);
        fingerprintDeviceValue.setDeviceResolution(this.deviceResolution);
        fingerprintDeviceValue.setRooted(this.isRooted);
        fingerprintDeviceValue.setFingerprintCompatible(this.isFingerprintCompatible);
        fingerprintDeviceValue.setGPSCompatible(this.isGPSCompatible);
        fingerprintDeviceValue.setOsName(this.osName);
        fingerprintDeviceValue.setOsVersion(this.osVersion);
        fingerprintDeviceValue.setApiLevel(this.apiLevel);
        return fingerprintDeviceValue;
    }

    public FingerprintWebHash getFingerprintWebHash(){

        FingerprintWebHash webHash = new FingerprintWebHash(BrowserFingerprintType.BFPv1);
        webHash.setFpHash(this.getHash());
        webHash.setFingerprintAgentValues(this.fingerprintAgentValue());
        webHash.setFingerprintValues(this.fingerprintValues());

        return webHash;
    }

    private FingerprintAgentValue fingerprintAgentValue(){
        FingerprintAgentValue fingerprintAgentValue = new FingerprintAgentValue();

        fingerprintAgentValue.setUa(this.ua);
        fingerprintAgentValue.setBrowser(new Browser(this.browserName,String.valueOf(this.browserMajor),this.browserVersion));
        fingerprintAgentValue.setCpu(new Cpu(this.cpuArchitecture));
        fingerprintAgentValue.setDevice(new Device(this.deviceType,this.deviceModel,this.deviceVendor));
        fingerprintAgentValue.setEngine(new BrowserEngine(this.engineName,this.engineVersion));
        fingerprintAgentValue.setOs(new OperatingSystem(this.fingerprintAgentOsName,this.fingerprintAgentOsVersion));

        return fingerprintAgentValue;
    }

    private FingerprintValues fingerprintValues(){
        FingerprintValues fingerprintValues = new FingerprintValues();
        fingerprintValues.setLanguage(this.language);
        fingerprintValues.setScreen(new Screen(this.colorDepth, new Resolution(this.screenWidth,this.screenHeight,this.availableWidth,this.availableHeight)));
        fingerprintValues.setTimezoneOffset(this.timezoneOffset);
        fingerprintValues.setHasSessionStorage(this.hasSessionStorage);
        fingerprintValues.setHasLocalStorage(this.hasLocalStorage);
        fingerprintValues.setHasIndexedDb(this.hasIndexedDb);
        fingerprintValues.setHasAddBehavior(this.hasAddBehavior);
        fingerprintValues.setHasOpenDatabase(this.hasOpenDatabase);
        fingerprintValues.setNavigatorCpuClass(this.navigatorCpuClass);
        fingerprintValues.setNavigatorPlatform(this.navigatorPlatform);
        fingerprintValues.setDoNotTrack(this.doNotTrack);
        fingerprintValues.setPlugins(this.plugins);
        fingerprintValues.setHasAdBlock(this.hasAdBlock);
        fingerprintValues.setHasLiedLanguages(this.hasLiedLanguages);
        fingerprintValues.setHasLiedResolution(this.hasLiedResolution);
        fingerprintValues.setHasLiedOs(this.hasLiedOs);
        fingerprintValues.setFonts(this.fonts);
        fingerprintValues.setCrcCanvas(new CrcCanvas(this.crcWebGl,this.crcCanvas));

        return fingerprintValues;
    }
}
