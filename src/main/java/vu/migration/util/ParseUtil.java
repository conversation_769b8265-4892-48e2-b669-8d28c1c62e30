package vu.migration.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.function.Function;

public class ParseUtil{
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // --- Long ---
    public static Optional<Long> parseOptionalLong(String value) {
        return parseOptional(value, Long::parseLong);
    }

    // --- LocalDateTime: start of day (00:00:00) ---
    public static Optional<LocalDateTime> parseDateStartOfDay(String dateStr) {
        return parseOptional(dateStr, str -> LocalDate.parse(str, DATE_FORMATTER).atStartOfDay());
    }

    // --- LocalDateTime: end of day (23:59:59) ---
    public static Optional<LocalDateTime> parseDateEndOfDay(String dateStr) {
        return parseOptional(dateStr, str -> LocalDate.parse(str, DATE_FORMATTER).atTime(23, 59, 59,999999));
    }

    public static <T> Optional<T> parseOptional(String value, Function<String, T> parser) {
        if (value == null || value.isEmpty()) return Optional.empty();
        try {
            return Optional.of(parser.apply(value));
        } catch (Exception e) {
            return Optional.empty();
        }
    }
}
