package vu.migration.util;

import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import vu.migration.service.fingerprint.ParametersNecessary;
import vu.migration.target.entities.administration.apikey.ApiKey;
import vu.migration.target.entities.fingerprint.FingerprintDeviceValue;
import vu.migration.target.entities.fingerprint.browser.FingerprintWebHash;

@Getter
@Setter
public class CheckFingerprintRequest{

    private ApiKey apiKey;

    @NotBlank(message = "userid is required")
    private String userid;

    private FingerprintWebHash fingerprintWebHash;

    private FingerprintDeviceValue fingerprintDeviceHash;

    private ParametersNecessary parametersNecessary;

    private Integer minThresholdToValidate;

}
