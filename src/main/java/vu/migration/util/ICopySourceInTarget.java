package vu.migration.util;


import org.springframework.transaction.annotation.Transactional;
import vu.migration.target.entities.audit.AuditLog;
import vu.migration.target.entities.profile.UserBusinessStatDetailDay;
import vu.migration.target.entities.profile.UserBusinessStatDetailMonthly;
import vu.migration.target.entities.profile.UserBusinessStatItem;
import vu.migration.target.entities.profile.UserBusinessStatsDetailWeekly;

import java.util.List;

public interface ICopySourceInTarget {

    @Transactional
    void copyAuditLog(List<AuditLog> auditLogPage);

    @Transactional
    void copyUserBusinessStatItem(List<UserBusinessStatItem> userBusinessStatItemList);

    @Transactional
    void copyUserBusinessStatDetailDay(List<UserBusinessStatDetailDay> userBusinessStatDetailDailyList);

    @Transactional
    void copyUserBusinessStatDetailWeekly(List<UserBusinessStatsDetailWeekly> userBusinessStatDetailWeeklyList);

    @Transactional
    void copyUserBusinessStatDetailMonthly(List<UserBusinessStatDetailMonthly> userBusinessStatDetailMonthlyList);
}
