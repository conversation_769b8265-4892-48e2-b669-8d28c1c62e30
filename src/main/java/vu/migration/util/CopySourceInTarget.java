package vu.migration.util;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vu.migration.target.entities.audit.AuditLog;
import vu.migration.target.entities.profile.*;

import jakarta.persistence.EntityManager;
import java.util.List;

@Service
public class CopySourceInTarget implements ICopySourceInTarget{

    private final EntityManager manager;

    public CopySourceInTarget(EntityManager manager){
        this.manager=manager;
    }

    @Override
    @Transactional
    public void copyAuditLog(List<vu.migration.target.entities.audit.AuditLog> auditLogPage){
        for(AuditLog auditLog:auditLogPage){
            manager.persist(auditLog);
        }

        manager.flush();
        manager.clear();
    }

    @Override
    @Transactional
    public void copyUserBusinessStatItem(List<UserBusinessStatItem> userBusinessStatItemList){

        for(UserBusinessStatItem userBusinessStatItem:userBusinessStatItemList){
            manager.persist(userBusinessStatItem);
        }

        manager.flush();
        manager.clear();

    }

    @Override
    @Transactional
    public void copyUserBusinessStatDetailDay(List<UserBusinessStatDetailDay> userBusinessStatDetailDailyList){

        for(UserBusinessStatDetailDay userBusinessStatDetailDaily:userBusinessStatDetailDailyList){
            manager.persist(userBusinessStatDetailDaily);
        }

        manager.flush();
        manager.clear();

    }

    @Override
    @Transactional
    public void copyUserBusinessStatDetailWeekly(List<UserBusinessStatsDetailWeekly> userBusinessStatDetailWeeklyList){

        for(UserBusinessStatsDetailWeekly userBusinessStatDetailWeekly:userBusinessStatDetailWeeklyList){
            manager.persist(userBusinessStatDetailWeekly);
        }

        manager.flush();
        manager.clear();

    }

    @Override
    @Transactional
    public void copyUserBusinessStatDetailMonthly(List<UserBusinessStatDetailMonthly> userBusinessStatDetailMonthlyList){

        for(UserBusinessStatDetailMonthly userBusinessStatDetailMonthly:userBusinessStatDetailMonthlyList){
            manager.persist(userBusinessStatDetailMonthly);
        }

        manager.flush();
        manager.clear();

    }
}
