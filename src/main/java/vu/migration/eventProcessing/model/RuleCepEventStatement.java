package vu.migration.eventProcessing.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.io.Serializable;

@Entity(name = "rule_event_statement")
public class RuleCepEventStatement implements Serializable{

    private static final long serialVersionUID = 1L;

    @Id
    @SequenceGenerator(name = "statement_id_seq", sequenceName = "statement_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "statement_id_seq")
    private int id;
    @Size(max = 2000)
    private String statement;
    private boolean context;

    @ManyToOne
    @JoinColumn(nullable = false, foreignKey = @ForeignKey(name = "FK_statement_rule"))
    private RuleCepItem rule;

    public RuleCepEventStatement() {
    }

    public RuleCepEventStatement(String statement, boolean context) {
        this.statement = statement;
        this.context = context;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public RuleCepItem getRule() {
        return rule;
    }

    public void setRule(RuleCepItem rule) {
        this.rule = rule;
    }

    public String getStatement() {
        return statement;
    }

    public void setStatement(String statement) {
        this.statement = statement;
    }

    public boolean isContext() {
        return context;
    }

    public void setContext(boolean context) {
        this.context = context;
    }
}

