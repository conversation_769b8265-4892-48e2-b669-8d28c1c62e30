package vu.migration.eventProcessing.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity(name = "rule_event")
public class RuleCepItem implements Serializable{

    private static final long serialVersionUID = 1L;

    @OneToMany(mappedBy = "rule", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    private Set<RuleCepEventStatement> ruleEventStatements = new HashSet<>();

    @Id
    private int id;
    private String name;
    private boolean enabled;
    @Column(name = "id_rule_type")
    private int idRuleType;
    private int score;
    @Column(name = "id_channel")
    private int idChannel;
    @Column(name = "id_operation_type")
    private int idOperationType;
    @Column(name = "id_action")
    private int idAction;
    @Column(name = "id_critical_level")
    private int idCriticalLevel;
    @Size(max = 4000)
    @Column(name = "json_format")
    private String jsonFormat;
    @Column(name = "last_update_date")
    private LocalDateTime lastUpdateDate;

    public Set<RuleCepEventStatement> getRuleEventStatements() {
        return ruleEventStatements;
    }

    public void setRuleEventStatements(Set<RuleCepEventStatement> ruleEventStatements) {
        this.ruleEventStatements = ruleEventStatements;
    }

    public void addEventStatement(RuleCepEventStatement eventStatement){
        this.ruleEventStatements.add(eventStatement);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getIdRuleType() {
        return idRuleType;
    }

    public void setIdRuleType(int idRuleType) {
        this.idRuleType = idRuleType;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public int getIdChannel() {
        return idChannel;
    }

    public void setIdChannel(int idChannel) {
        this.idChannel = idChannel;
    }

    public int getIdOperationType() {
        return idOperationType;
    }

    public void setIdOperationType(int idOperationType) {
        this.idOperationType = idOperationType;
    }

    public int getIdAction() {
        return idAction;
    }

    public void setIdAction(int idAction) {
        this.idAction = idAction;
    }

    public int getIdCriticalLevel() {
        return idCriticalLevel;
    }

    public void setIdCriticalLevel(int idCriticalLevel) {
        this.idCriticalLevel = idCriticalLevel;
    }

    public String getJsonFormat() {
        return jsonFormat;
    }

    public void setJsonFormat(String jsonFormat) {
        this.jsonFormat = jsonFormat;
    }

    private void addStatement(RuleCepEventStatement statement){
        this.ruleEventStatements.add(statement);
    }

    public LocalDateTime getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(LocalDateTime lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }
}
