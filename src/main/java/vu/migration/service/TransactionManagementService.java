package vu.migration.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vu.data.models.fraud.cm.SourceAssignedCase;
import vu.data.models.fraud.cm.SourceCaseAdditionalInfo;
import vu.data.models.fraud.cm.SourceCaseAttachment;
import vu.data.models.fraud.cm.SourceLastAssignedCase;
import vu.data.models.fraud.transaction.SourceAdditionalParametersTransaction;
import vu.data.models.fraud.transaction.SourceRuleMatchedTransaction;
import vu.data.models.fraud.transaction.SourceTransactionEntity;
import vu.migration.mappers.*;
import vu.migration.service.fraud.cm.IAssignedCaseService;
import vu.migration.service.fraud.event.IRuleMatchedEventService;
import vu.migration.service.fraud.transaction.ITransactionEntityService;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.target.entities.fraud.cm.AssignedCase;
import vu.migration.target.entities.fraud.cm.CaseAdditionalInfo;
import vu.migration.target.entities.fraud.cm.CaseAttachment;
import vu.migration.target.entities.fraud.cm.LastAssignedCase;
import vu.migration.target.entities.fraud.transaction.AdditionalParametersTransaction;
import vu.migration.target.entities.fraud.transaction.RuleMatchedTransaction;
import vu.migration.target.entities.fraud.transaction.TransactionEntity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Service
public class TransactionManagementService{
    private static final Logger logger = LoggerFactory.getLogger(TransactionManagementService.class);

    private final ITransactionEntityService iTransactionEntityService;
    private final TransactionMapper transactionMapper;
    private final AssignedCaseMapper assignedCaseMapper;
    private final IRuleMatchedEventService iRuleMatchedEventService;
    private final AdditionalParametersTransactionMapper additionalParametersTransactionMapper;
    private final RuleMatchedTransactionMapper ruleMatchedTransactionMapper;
    private final IAssignedCaseService iAssignedCaseService;
    private final CaseAdditionalInfoMapper caseAdditionalInfoMapper;
    private final CaseAttachmentMapper caseAttachmentMapper;
    private final LastAssignedCaseMapper lastAssignedCaseMapper;

    public TransactionManagementService(ITransactionEntityService iTransactionEntityService,TransactionMapper transactionMapper,AssignedCaseMapper assignedCaseMapper,IRuleMatchedEventService iRuleMatchedEventService,AdditionalParametersTransactionMapper additionalParametersTransactionMapper,RuleMatchedTransactionMapper ruleMatchedTransactionMapper,IAssignedCaseService iAssignedCaseService,CaseAdditionalInfoMapper caseAdditionalInfoMapper,CaseAttachmentMapper caseAttachmentMapper,LastAssignedCaseMapper lastAssignedCaseMapper){
        this.iTransactionEntityService=iTransactionEntityService;
        this.transactionMapper=transactionMapper;
        this.assignedCaseMapper=assignedCaseMapper;
        this.iRuleMatchedEventService=iRuleMatchedEventService;
        this.additionalParametersTransactionMapper=additionalParametersTransactionMapper;
        this.ruleMatchedTransactionMapper=ruleMatchedTransactionMapper;
        this.iAssignedCaseService=iAssignedCaseService;
        this.caseAdditionalInfoMapper=caseAdditionalInfoMapper;
        this.caseAttachmentMapper=caseAttachmentMapper;
        this.lastAssignedCaseMapper=lastAssignedCaseMapper;
    }

    @Value("${app.migration.chunk-size:10000}")
    private int chunkSize;

    @Value("${app.migration.parallelism:4}")
    private int parallelism;

    public void copyTransactionEntity(Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate,
                                       Optional<Long> optionalFromId,Optional<Long> optionalToId){
        logger.info("TransactionEntity - Copy - Begin;");

        long fromId;
        long toId;

        if (optionalFromId.isPresent() && optionalToId.isPresent()) {
            fromId = optionalFromId.get();
            toId = optionalToId.get();
        } else if (fromDate.isPresent() && toDate.isPresent()) {
            Optional<IdRange> idRangeOpt = iTransactionEntityService.findMinAndMaxIdByDateRange(fromDate.get(), toDate.get());
            if (idRangeOpt.isEmpty()) {
                throw new IllegalStateException("No ID range found for given date range.");
            }
            IdRange idRange = idRangeOpt.get();
            fromId = idRange.getMinId();
            toId = idRange.getMaxId();
        } else {
            Optional<IdRange> idRangeOpt = iTransactionEntityService.findMinAndMaxId();
            if (idRangeOpt.isEmpty()) {
                throw new IllegalStateException("No ID range found for entire data.");
            }
            IdRange idRange = idRangeOpt.get();
            fromId = idRange.getMinId();
            toId = idRange.getMaxId();
        }

        ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
        List<Future<?>> futures = new ArrayList<>();

        long lastSeenId = toId;
        List<SourceTransactionEntity> transactionToCopy;

        try {
            do {
                transactionToCopy = iTransactionEntityService.findTransactionsWithFilters(lastSeenId, fromId, chunkSize);

                if (transactionToCopy.isEmpty()) break;

                List<SourceTransactionEntity> finalTransactionToCopy = transactionToCopy;

                Future<?> future = executor.submit(() -> {
                    List<TransactionEntity> mappedTransactions = transactionMapper.toTargetTransactionList(finalTransactionToCopy);
                    iTransactionEntityService.copyTransactions(mappedTransactions);
                });

                futures.add(future);

                lastSeenId = transactionToCopy.getLast().getId();
            } while (!transactionToCopy.isEmpty());

            waitForCompletion(futures);
        } catch (Exception e) {
            logger.error("Error during copy", e);
            throw new RuntimeException("Error copying records", e);
        } finally {
            executor.shutdown();
        }

        copyTransactionAdditionalParameter(fromId, toId);

        copyRuleMatchedTransaction(fromId, toId);

        copyAssignedCase(fromId, toId);

        logger.info("TransactionEntity - Copy - End;");
    }

    private void copyTransactionAdditionalParameter(long fromId,long toId){
        logger.info("TransactionAdditionalParameter - Copy - Begin;");

        ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
        List<Future<?>> futures = new ArrayList<>();

        long lastSeenId = toId;
        List<SourceAdditionalParametersTransaction> additionalParametersTransactionToCopy;

        try {
            do {
                additionalParametersTransactionToCopy = iTransactionEntityService.findAdditionalParametersTransactionWithFilters(lastSeenId, fromId, chunkSize);

                if (additionalParametersTransactionToCopy.isEmpty()) break;

                List<SourceAdditionalParametersTransaction> finalAdditionalParametersTransactionToCopy = additionalParametersTransactionToCopy;

                Future<?> future = executor.submit(() -> {
                    List<AdditionalParametersTransaction> mappedSourceAdditionalParametersTransaction = additionalParametersTransactionMapper.toTargetAdditionalParametersTransactionList(finalAdditionalParametersTransactionToCopy);
                    iTransactionEntityService.copyAdditionalParametersTransaction(mappedSourceAdditionalParametersTransaction);
                });

                futures.add(future);

                lastSeenId = additionalParametersTransactionToCopy.getLast().getId();
            } while (!additionalParametersTransactionToCopy.isEmpty());

            waitForCompletion(futures);
        } catch (Exception e) {
            logger.error("Error during copy", e);
            throw new RuntimeException("Error copying records", e);
        } finally {
            executor.shutdown();
        }

        logger.info("TransactionAdditionalParameter - Copy - End;");
    }

    private void copyRuleMatchedTransaction(long fromId,long toId){
        logger.info("RuleMatchedTransaction - Copy - Begin;");

        ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
        List<Future<?>> futures = new ArrayList<>();

        long lastSeenId = toId;
        List<SourceRuleMatchedTransaction> ruleMatchedTransactionToCopy;

        try {
            do {
                ruleMatchedTransactionToCopy = iRuleMatchedEventService.findRuleMatchedTransactionWithFilters(lastSeenId, fromId, chunkSize);

                if (ruleMatchedTransactionToCopy.isEmpty()) break;

                List<SourceRuleMatchedTransaction> finalRuleMatchedTransactionToCopy = ruleMatchedTransactionToCopy;

                Future<?> future = executor.submit(() -> {
                    List<RuleMatchedTransaction> ruleMatchedTransaction = ruleMatchedTransactionMapper.toTargetRuleMatchedTransactionList(finalRuleMatchedTransactionToCopy);
                    iRuleMatchedEventService.copyRuleMatchedTransaction(ruleMatchedTransaction);
                });

                futures.add(future);

                lastSeenId = ruleMatchedTransactionToCopy.getLast().getId();
            } while (!ruleMatchedTransactionToCopy.isEmpty());

            waitForCompletion(futures);
        } catch (Exception e) {
            logger.error("Error during copy", e);
            throw new RuntimeException("Error copying records", e);
        } finally {
            executor.shutdown();
        }

        logger.info("RuleMatchedTransaction - Copy - End;");
    }

    private void copyAssignedCase(long fromId,long toId){
        logger.info("AssignedCase - Copy - Begin;");

        ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
        List<Future<?>> futures = new ArrayList<>();

        long lastSeenId = toId;
        List<SourceAssignedCase> sourceAssignedCaseToCopy;

        try {
            do {
                sourceAssignedCaseToCopy = iAssignedCaseService.findAssignedCaseWithFilters(lastSeenId, fromId, chunkSize);

                if (sourceAssignedCaseToCopy.isEmpty()) break;

                List<SourceAssignedCase> finalSourceAssignedCaseToCopy = sourceAssignedCaseToCopy;

                Future<?> future = executor.submit(() -> {
                    List<AssignedCase> assignedCase = assignedCaseMapper.toTargetAssignedCaseList(finalSourceAssignedCaseToCopy);
                    iAssignedCaseService.copyAssignedCase(assignedCase);
                });

                futures.add(future);

                lastSeenId = sourceAssignedCaseToCopy.getLast().getId();
            } while (!sourceAssignedCaseToCopy.isEmpty());

            waitForCompletion(futures);
        } catch (Exception e) {
            logger.error("Error during copy", e);
            throw new RuntimeException("Error copying records", e);
        } finally {
            executor.shutdown();
        }

        copyCaseAdditionalInfo(fromId, toId);

        copyCaseAttachments(fromId, toId);

        copyLastAssignedCase(fromId, toId);

        logger.info("AssignedCase - Copy - End;");
    }

    private void copyCaseAdditionalInfo(long fromId,long toId){
        logger.info("CaseAdditionalInfo - Copy - Begin;");

        ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
        List<Future<?>> futures = new ArrayList<>();

        long lastSeenId = toId;
        List<SourceCaseAdditionalInfo> sourceCaseAdditionalInfoToCopy;

        try{
            do {
                sourceCaseAdditionalInfoToCopy = iAssignedCaseService.findCaseAdditionalInfoWithFilters(lastSeenId, fromId, chunkSize);

                if (sourceCaseAdditionalInfoToCopy.isEmpty()) break;

                List<SourceCaseAdditionalInfo> finalSourceCaseAdditionalInfoToCopy = sourceCaseAdditionalInfoToCopy;

                Future<?> future = executor.submit(() -> {
                    List<CaseAdditionalInfo> caseAdditionalInfo = caseAdditionalInfoMapper.toTargetCaseAdditionalInfoList(finalSourceCaseAdditionalInfoToCopy);
                    iAssignedCaseService.copyCaseAdditionalInfo(caseAdditionalInfo);
                });

                futures.add(future);

                lastSeenId = sourceCaseAdditionalInfoToCopy.getLast().getId();
            } while (!sourceCaseAdditionalInfoToCopy.isEmpty());

            waitForCompletion(futures);
        } catch (Exception e) {
            logger.error("Error during copy", e);
            throw new RuntimeException("Error copying records", e);
        } finally {
            executor.shutdown();
        }

        logger.info("CaseAdditionalInfo - Copy - End;");
    }

    private void copyCaseAttachments(long fromId,long toId){
        logger.info("CaseAttachments - Copy - Begin;");

        ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
        List<Future<?>> futures = new ArrayList<>();

        long lastSeenId = toId;
        List<SourceCaseAttachment> sourceCaseAttachmentToCopy;

        try{
            do {
                sourceCaseAttachmentToCopy = iAssignedCaseService.findCaseAttachmentWithFilters(lastSeenId, fromId, chunkSize);

                if (sourceCaseAttachmentToCopy.isEmpty()) break;

                List<SourceCaseAttachment> finalSourceCaseAttachmentToCopy = sourceCaseAttachmentToCopy;

                Future<?> future = executor.submit(() -> {
                    List<CaseAttachment> caseAttachment = caseAttachmentMapper.toTargetCaseAttachmentList(finalSourceCaseAttachmentToCopy);
                    iAssignedCaseService.copyCaseAttachment(caseAttachment);
                });

                futures.add(future);

                lastSeenId = sourceCaseAttachmentToCopy.getLast().getId();
            } while (!sourceCaseAttachmentToCopy.isEmpty());

            waitForCompletion(futures);
        } catch (Exception e) {
            logger.error("Error during copy", e);
            throw new RuntimeException("Error copying records", e);
        } finally {
            executor.shutdown();
        }

        logger.info("CaseAttachments - Copy - End;");
    }

    private void copyLastAssignedCase(long fromId,long toId){
        logger.info("LastAssignedCase - Copy - Begin;");

        ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
        List<Future<?>> futures = new ArrayList<>();

        long lastSeenId = toId;
        List<SourceLastAssignedCase> sourceLastAssignedCaseToCopy;

        try{
            do {
                sourceLastAssignedCaseToCopy = iAssignedCaseService.findLastAssignedCaseWithFilters(lastSeenId, fromId, chunkSize);

                if (sourceLastAssignedCaseToCopy.isEmpty()) break;

                List<SourceLastAssignedCase> finalSourceLastAssignedCaseToCopy = sourceLastAssignedCaseToCopy;

                Future<?> future = executor.submit(() -> {
                    List<LastAssignedCase> lastAssignedCase = lastAssignedCaseMapper.toTargetLastAssignedCaseList(finalSourceLastAssignedCaseToCopy);
                    iAssignedCaseService.copyLastAssignedCase(lastAssignedCase);
                });

                futures.add(future);

                lastSeenId = sourceLastAssignedCaseToCopy.getLast().getId();
            } while (!sourceLastAssignedCaseToCopy.isEmpty());

            waitForCompletion(futures);
        } catch (Exception e) {
            logger.error("Error during copy", e);
            throw new RuntimeException("Error copying records", e);
        } finally {
            executor.shutdown();
        }

        logger.info("LastAssignedCase - Copy - End;");
    }

    private void waitForCompletion(List<Future<?>> futures) {
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("Process interrupted during copying", e);
            } catch (ExecutionException e) {
                throw new RuntimeException("Error in one of the copy tasks", e.getCause());
            }
        }
    }
}
