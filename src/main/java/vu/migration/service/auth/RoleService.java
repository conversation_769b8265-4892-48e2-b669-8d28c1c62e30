package vu.migration.service.auth;

import org.springframework.stereotype.Service;
import vu.data.models.administration.user.SourceRole;
import vu.migration.source.repository.administration.user.SourceRoleRepository;
import vu.migration.target.entities.administration.user.Role;
import vu.migration.target.repositories.auth.RoleRepository;

import java.util.List;
import java.util.Optional;

@Service
public class RoleService implements IRoleService{
    private final RoleRepository roleRepository;
    private final SourceRoleRepository sourceRoleRepository;

    public RoleService(RoleRepository roleRepository,SourceRoleRepository sourceRoleRepository){
        this.roleRepository=roleRepository;
        this.sourceRoleRepository=sourceRoleRepository;
    }

    @Override
    public Role findByName(String name){
        Optional<Role> role = roleRepository.findByName(name);

        return role.orElse(null);
    }

    @Override
    public void save(Role role){
        roleRepository.save(role);
    }

    @Override
    public List<SourceRole> getAllRoles(){
        return sourceRoleRepository.findAll();
    }

    @Override
    public void saveAllRoles(List<Role> roles){
        roleRepository.saveAll(roles);
    }

}
