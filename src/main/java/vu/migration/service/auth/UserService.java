package vu.migration.service.auth;

import org.springframework.stereotype.Service;
import vu.data.models.administration.user.SourceUser;
import vu.migration.source.repository.administration.user.SourceUserRepository;
import vu.migration.target.entities.administration.user.User;
import vu.migration.target.repositories.auth.UserRepository;

import java.util.List;
import java.util.Optional;

@Service
public class UserService implements IUserService{
    private final UserRepository userRepository;
    private final SourceUserRepository sourceUserRepository;

    public UserService(UserRepository userRepository,SourceUserRepository sourceUserRepository){
        this.userRepository=userRepository;
        this.sourceUserRepository=sourceUserRepository;
    }

    @Override
    public User findByUserName(String userName){
        return userRepository.findByUserName(userName);
    }

    @Override
    public User save(User user){
        return userRepository.save(user);
    }

    @Override
    public List<SourceUser> getAllUsers(){
        return sourceUserRepository.findAll();
    }

    @Override
    public SourceUser getUserById(Long userId){
        Optional<SourceUser> user = sourceUserRepository.findById(userId);
        return user.orElse(null);
    }

    @Override
    public Long getAuthFaUserId(Long userId){
        String authFaUserName = getUserById(userId).getUserName();
        return findByUserName(authFaUserName).getId() ;
    }

    @Override
    public void saveAllUser(List<User> users){
        userRepository.saveAll(users);
    }

}
