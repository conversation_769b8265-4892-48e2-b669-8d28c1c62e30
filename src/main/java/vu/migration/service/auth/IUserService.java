package vu.migration.service.auth;

import vu.data.models.administration.user.SourceUser;
import vu.migration.target.entities.administration.user.User;

import java.util.List;

public interface IUserService{
    User findByUserName(String userName);

    User save(User user);

    List<SourceUser> getAllUsers();

    SourceUser getUserById(Long userId);

    Long getAuthFaUserId(Long userId);

    void saveAllUser(List<User> users);
}
