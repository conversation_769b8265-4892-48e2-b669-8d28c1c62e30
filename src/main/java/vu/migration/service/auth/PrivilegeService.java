package vu.migration.service.auth;

import org.springframework.stereotype.Service;
import vu.migration.target.entities.administration.user.Privilege;
import vu.migration.target.repositories.auth.PrivilegeRepository;

@Service
public class PrivilegeService implements IPrivilegeService{
    private final PrivilegeRepository privilegeRepository;

    public PrivilegeService(PrivilegeRepository privilegeRepository){
        this.privilegeRepository=privilegeRepository;
    }

    @Override
    public Privilege getPrivilege(String privilege){
        return privilegeRepository.findByName(privilege);
    }
}
