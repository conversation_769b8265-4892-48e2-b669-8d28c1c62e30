package vu.migration.service.auth;

import org.springframework.stereotype.Service;
import vu.data.models.administration.user.SourceLdapRole;
import vu.migration.source.repository.administration.user.SourceLdapRoleRepository;
import vu.migration.target.entities.administration.user.LdapRole;
import vu.migration.target.repositories.auth.LdapRolRepository;

import java.util.ArrayList;
import java.util.List;

@Service
public class LdapRolService implements ILdapRolService{
    private final LdapRolRepository ldapRolRepository;
    private final SourceLdapRoleRepository sourceLdapRoleRepository;

    public LdapRolService(LdapRolRepository ldapRolRepository,SourceLdapRoleRepository sourceLdapRoleRepository){
        this.ldapRolRepository=ldapRolRepository;
        this.sourceLdapRoleRepository=sourceLdapRoleRepository;
    }

    @Override
    public void copyLdapRole(){
        List<SourceLdapRole> sourceSourceLdapRoles= sourceLdapRoleRepository.findAll();

        if(!sourceSourceLdapRoles.isEmpty()){
            List<LdapRole> listLdapRole=new ArrayList<>();

            for(SourceLdapRole sourceLdapRole : sourceSourceLdapRoles){
                LdapRole targetLdapRole=new LdapRole();
                targetLdapRole.setRoleId(sourceLdapRole.getRoleId());
                targetLdapRole.setRoleName(sourceLdapRole.getRoleName());
                targetLdapRole.setGroupCn(sourceLdapRole.getGroupCn());

                listLdapRole.add(targetLdapRole);
            }

            saveAllLdapRole(listLdapRole);
        }
    }

    private void saveAllLdapRole(List<LdapRole>ldapRole){
        ldapRolRepository.saveAll(ldapRole);
    }
}
