package vu.migration.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vu.migration.service.report.profile.IServiceUserBusiness;
import vu.migration.target.entities.profile.UserBusinessStatDetailDay;
import vu.migration.target.entities.profile.UserBusinessStatDetailMonthly;
import vu.migration.target.entities.profile.UserBusinessStatItem;
import vu.migration.target.entities.profile.UserBusinessStatsDetailWeekly;
import vu.migration.util.ICopySourceInTarget;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class BusinessManagementService{
    private static final Logger logger = LoggerFactory.getLogger(BusinessManagementService.class);
    private final IServiceUserBusiness iServiceUserBusiness;
    private final ICopySourceInTarget iCopySourceInTarget;

    public BusinessManagementService(IServiceUserBusiness iServiceUserBusiness,ICopySourceInTarget iCopySourceInTarget){
        this.iServiceUserBusiness=iServiceUserBusiness;
        this.iCopySourceInTarget=iCopySourceInTarget;
    }

    @Value("${spring.jpa.properties.hibernate.jdbc.batch_size}")
    private int sizePage;

    public void copyUserBusiness(){
        logger.info("User Business - Copy - Begin;");

        copyUserBusinessStatItem();

        logger.info("User Business - Copy - End;");
    }

    private void copyUserBusinessStatItem(){
        Pageable pageUserBusinessStatItemSearch = PageRequest.of(0, sizePage);
        Page<UserBusinessStatItem> userBusinessStatItemPage;
        do {
            userBusinessStatItemPage = iServiceUserBusiness.userBusinessStatItemFindAll(pageUserBusinessStatItemSearch);
            iCopySourceInTarget.copyUserBusinessStatItem(userBusinessStatItemPage.getContent());
            pageUserBusinessStatItemSearch = pageUserBusinessStatItemSearch.next();
        } while (userBusinessStatItemPage.hasNext());

        copyUserBusinessStatDetailDay();
        copyUserBusinessStatDetailWeekly();
        copyUserBusinessStatDetailMonthly();
    }

    private void copyUserBusinessStatDetailDay(){
        Pageable pageUserBusinessStatDetailDaySearch = PageRequest.of(0, sizePage);
        Page<UserBusinessStatDetailDay> userBusinessStatDetailDayPage;
        do {
            userBusinessStatDetailDayPage = iServiceUserBusiness.userBusinessStatDetailDayFindAll(pageUserBusinessStatDetailDaySearch);
            iCopySourceInTarget.copyUserBusinessStatDetailDay(userBusinessStatDetailDayPage.getContent());
            pageUserBusinessStatDetailDaySearch = pageUserBusinessStatDetailDaySearch.next();
        } while (userBusinessStatDetailDayPage.hasNext());
    }

    private void copyUserBusinessStatDetailWeekly(){
        Pageable pageUserBusinessStatDetailWeeklySearch = PageRequest.of(0, sizePage);
        Page<UserBusinessStatsDetailWeekly> userBusinessStatDetailWeeklyPage;
        do {
            userBusinessStatDetailWeeklyPage = iServiceUserBusiness.userBusinessStatDetailWeeklyFindAll(pageUserBusinessStatDetailWeeklySearch);
            iCopySourceInTarget.copyUserBusinessStatDetailWeekly(userBusinessStatDetailWeeklyPage.getContent());
            pageUserBusinessStatDetailWeeklySearch = pageUserBusinessStatDetailWeeklySearch.next();
        } while (userBusinessStatDetailWeeklyPage.hasNext());
    }

    private void copyUserBusinessStatDetailMonthly(){
        Pageable pageUserBusinessStatDetailMonthlySearch = PageRequest.of(0, sizePage);
        Page<UserBusinessStatDetailMonthly> userBusinessStatDetailMonthlyPage;
        do {
            userBusinessStatDetailMonthlyPage = iServiceUserBusiness.userBusinessStatDetailMonthlyFindAll(pageUserBusinessStatDetailMonthlySearch);
            iCopySourceInTarget.copyUserBusinessStatDetailMonthly(userBusinessStatDetailMonthlyPage.getContent());
            pageUserBusinessStatDetailMonthlySearch = pageUserBusinessStatDetailMonthlySearch.next();
        } while (userBusinessStatDetailMonthlyPage.hasNext());
    }
}
