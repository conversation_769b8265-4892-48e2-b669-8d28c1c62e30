package vu.migration.service.notification;

import org.springframework.stereotype.Service;
import vu.data.models.notification.SourceNotificationConfig;
import vu.migration.source.repository.notification.SourceNotificationConfigRepository;
import vu.migration.target.repositories.notification.NotificationConfigRepository;

import java.util.ArrayList;
import java.util.List;

@Service
public class NotificationConfigService implements INotificationConfigService{
    private final SourceNotificationConfigRepository sourceNotificationConfigRepository;
    private final NotificationConfigRepository notificationConfigRepository;

    public NotificationConfigService(SourceNotificationConfigRepository sourceNotificationConfigRepository,NotificationConfigRepository notificationConfigRepository){
        this.sourceNotificationConfigRepository=sourceNotificationConfigRepository;
        this.notificationConfigRepository=notificationConfigRepository;
    }

    @Override
    public void copyNotificationConfig(){
        List<SourceNotificationConfig> sourceSourceNotificationConfigs= sourceNotificationConfigRepository.findAll();
        if (!sourceSourceNotificationConfigs.isEmpty()){
            List<vu.migration.target.entities.notification.NotificationConfig> listNotificationConfig = new ArrayList<>();

            for(SourceNotificationConfig sourceNotificationConfig: sourceSourceNotificationConfigs){
                vu.migration.target.entities.notification.NotificationConfig notificationConfig = new vu.migration.target.entities.notification.NotificationConfig();
                notificationConfig.setBusinessGroupId(sourceNotificationConfig.getBusinessGroupId());
                notificationConfig.setEnabled(sourceNotificationConfig.isEnabled());
                notificationConfig.setNotificationTypeId(sourceNotificationConfig.getNotificationTypeId());
                notificationConfig.setNotification_value(sourceNotificationConfig.getNotification_value());
                listNotificationConfig.add(notificationConfig);
            }

            notificationConfigRepository.saveAll(listNotificationConfig);
        }
    }
}
