package vu.migration.service.notification.group;

import org.springframework.stereotype.Service;
import vu.data.models.notification.group.SourceBusinessGroup;
import vu.migration.source.repository.notification.group.SourceBusinessGroupRepository;
import vu.migration.target.repositories.notification.group.BusinessGroupRepository;

import java.util.ArrayList;
import java.util.List;

@Service
public class BusinessGroupService implements IBusinessGroupService{
    private final SourceBusinessGroupRepository sourceBusinessGroupRepository;
    private final BusinessGroupRepository businessGroupRepository;

    public BusinessGroupService(SourceBusinessGroupRepository sourceBusinessGroupRepository,BusinessGroupRepository businessGroupRepository){
        this.sourceBusinessGroupRepository=sourceBusinessGroupRepository;
        this.businessGroupRepository=businessGroupRepository;
    }

    @Override
    public void copyBusinessGroup(){
        List<SourceBusinessGroup> sourceSourceBusinessGroups= sourceBusinessGroupRepository.findAll();
        if (!sourceSourceBusinessGroups.isEmpty()){
            List<vu.migration.target.entities.notification.group.BusinessGroup> listBusinessGroup = new ArrayList<>();

            for(SourceBusinessGroup sourceBusinessGroup: sourceSourceBusinessGroups){
               vu.migration.target.entities.notification.group.BusinessGroup businessGroup = new vu.migration.target.entities.notification.group.BusinessGroup();
                businessGroup.setId(sourceBusinessGroup.getId());
                businessGroup.setName(sourceBusinessGroup.getName());
                businessGroup.setSupervisorUserId(sourceBusinessGroup.getSupervisorUserId());
                listBusinessGroup.add(businessGroup);
            }

            businessGroupRepository.saveAll(listBusinessGroup);
        }
    }
}
