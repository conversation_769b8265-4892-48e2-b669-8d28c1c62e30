package vu.migration.service.notification.group;

import org.springframework.stereotype.Service;
import vu.data.models.notification.group.SourceUserBusinessGroup;
import vu.migration.source.repository.notification.group.SourceUserBusinessGroupRepository;
import vu.migration.target.repositories.notification.group.UserBusinessGroupRepository;

import java.util.ArrayList;
import java.util.List;

@Service
public class UserBusinessGroupService implements IUserBusinessGroupService{
    private final SourceUserBusinessGroupRepository sourceUserBusinessGroupRepository;
    private final UserBusinessGroupRepository userBusinessGroupRepository;

    public UserBusinessGroupService(SourceUserBusinessGroupRepository sourceUserBusinessGroupRepository,UserBusinessGroupRepository userBusinessGroupRepository){
        this.sourceUserBusinessGroupRepository=sourceUserBusinessGroupRepository;
        this.userBusinessGroupRepository=userBusinessGroupRepository;
    }

    @Override
    public void copyUserBusinessGroup(){
        List<SourceUserBusinessGroup> sourceSourceUserBusinessGroups= sourceUserBusinessGroupRepository.findAll();
        if (!sourceSourceUserBusinessGroups.isEmpty()){
            List<vu.migration.target.entities.notification.group.UserBusinessGroup> listBusinessGroup = new ArrayList<>();

            for(SourceUserBusinessGroup sourceUserBusinessGroup: sourceSourceUserBusinessGroups){
                vu.migration.target.entities.notification.group.UserBusinessGroup userBusinessGroup = new vu.migration.target.entities.notification.group.UserBusinessGroup();
                userBusinessGroup.setBusinessGroupId(sourceUserBusinessGroup.getBusinessGroupId());
                userBusinessGroup.setUserId(sourceUserBusinessGroup.getUserId());
                listBusinessGroup.add(userBusinessGroup);
            }

            userBusinessGroupRepository.saveAll(listBusinessGroup);
        }
    }
}
