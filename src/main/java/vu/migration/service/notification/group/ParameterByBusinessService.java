package vu.migration.service.notification.group;

import org.springframework.stereotype.Service;
import vu.data.models.notification.group.SourceParameterByBusiness;
import vu.migration.source.repository.notification.group.SourceParameterByBusinessRepository;
import vu.migration.target.repositories.notification.group.ParameterByBusinessRepository;

import java.util.ArrayList;
import java.util.List;

@Service
public class ParameterByBusinessService implements IParameterByBusinessService{
    private final SourceParameterByBusinessRepository sourceParameterByBusinessRepository;
    private final ParameterByBusinessRepository parameterByBusinessRepository;

    public ParameterByBusinessService(SourceParameterByBusinessRepository sourceParameterByBusinessRepository,ParameterByBusinessRepository parameterByBusinessRepository){
        this.sourceParameterByBusinessRepository=sourceParameterByBusinessRepository;
        this.parameterByBusinessRepository=parameterByBusinessRepository;
    }

    @Override
    public void copyParameterByBusiness(){
        List<SourceParameterByBusiness> sourceParametersByBusiness = sourceParameterByBusinessRepository.findAll();
        if (!sourceParametersByBusiness.isEmpty()){
            List<vu.migration.target.entities.notification.group.ParameterByBusiness> listParameterByBusiness = new ArrayList<>();

            for(SourceParameterByBusiness sourceParameterByBusiness:sourceParametersByBusiness){
                vu.migration.target.entities.notification.group.ParameterByBusiness parameterByBusiness = new vu.migration.target.entities.notification.group.ParameterByBusiness();
                parameterByBusiness.setBusiness(sourceParameterByBusiness.getBusiness());
                parameterByBusiness.setDailyMaxCount(sourceParameterByBusiness.getDailyMaxCount());
                parameterByBusiness.setWeeklyMaxCount(sourceParameterByBusiness.getWeeklyMaxCount());
                parameterByBusiness.setMonthlyMaxCount(sourceParameterByBusiness.getMonthlyMaxCount());
                parameterByBusiness.setDailyMaxAmount(sourceParameterByBusiness.getDailyMaxAmount());
                parameterByBusiness.setWeeklyMaxAmount(sourceParameterByBusiness.getWeeklyMaxAmount());
                parameterByBusiness.setMonthlyMaxAmount(sourceParameterByBusiness.getMonthlyMaxAmount());
                parameterByBusiness.setEnabled(sourceParameterByBusiness.isEnabled());
                listParameterByBusiness.add(parameterByBusiness);
            }

            parameterByBusinessRepository.saveAll(listParameterByBusiness);
        }
    }
}
