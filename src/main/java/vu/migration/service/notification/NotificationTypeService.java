package vu.migration.service.notification;

import org.springframework.stereotype.Service;
import vu.data.models.notification.SourceNotificationType;
import vu.migration.source.repository.notification.SourceNotificationTypeRepository;
import vu.migration.target.entities.notification.NotificationType;
import vu.migration.target.repositories.notification.NotificationTypeRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class NotificationTypeService implements INotificationTypeService{
    private final SourceNotificationTypeRepository sourceNotificationTypeRepository;
    private final NotificationTypeRepository notificationTypeRepository;

    public NotificationTypeService(SourceNotificationTypeRepository sourceNotificationTypeRepository,NotificationTypeRepository notificationTypeRepository){
        this.sourceNotificationTypeRepository=sourceNotificationTypeRepository;
        this.notificationTypeRepository=notificationTypeRepository;
    }

    @Override
    public void copyNotificationType(){
        List<SourceNotificationType> sourceSourceNotificationTypes= sourceNotificationTypeRepository.findAll();
        if (!sourceSourceNotificationTypes.isEmpty()){
            for(SourceNotificationType sourceNotificationType: sourceSourceNotificationTypes){
                Optional<NotificationType> optionalTarget = notificationTypeRepository.findById(sourceNotificationType.getId());
                NotificationType target;
                if (optionalTarget.isPresent()) {
                    target = optionalTarget.get();
                } else {
                    target = new NotificationType();
                }
                target.setName(sourceNotificationType.getName());
                target.setEndpoint(sourceNotificationType.getEndpoint());
                target.setEnabled(sourceNotificationType.isEnabled());

                notificationTypeRepository.save(target);
            }
        }
    }
}
