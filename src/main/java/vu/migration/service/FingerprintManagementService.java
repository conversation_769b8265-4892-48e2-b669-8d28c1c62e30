package vu.migration.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vu.data.models.fingerprint.v2.SourceFingerprintV2UserHashes;
import vu.migration.service.interfaces.fingerprint.IFingerprintService;
import vu.migration.util.FingerprintV1AndDeviceInfo;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class FingerprintManagementService{
    private static final Logger logger = LoggerFactory.getLogger(FingerprintManagementService.class);

    private final IFingerprintService iFingerprintService;

    public FingerprintManagementService(IFingerprintService iFingerprintService){
        this.iFingerprintService=iFingerprintService;
    }

    @Value("${app.migration.chunk-size:10000}")
    private int chunkSize;

    @Value("${app.migration.parallelism:4}")
    private int parallelism;

    public void copyFingerprint(Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate){
        logger.info("Fingerprint - Copy - Begin;");

        fingerprintV2(fromDate, toDate);
        fingerprintV1AndDevice(fromDate, toDate);

        logger.info("Fingerprint - Copy - End;");
    }

    private void fingerprintV2(Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate){
        logger.info("Fingerprint Browser V2 - Copy - Begin;");

    Pageable pageFingerprintV2UserHashesSearch = PageRequest.of(0, chunkSize);
        Page<SourceFingerprintV2UserHashes> fingerprintV2UserHashesPage;
        do {
            fingerprintV2UserHashesPage = iFingerprintService.fingerprintV2UserHashesFindAll(pageFingerprintV2UserHashesSearch, fromDate, toDate);
            iFingerprintService.copyFingerprintV2(fingerprintV2UserHashesPage.getContent());
            pageFingerprintV2UserHashesSearch = pageFingerprintV2UserHashesSearch.next();
        } while (fingerprintV2UserHashesPage.hasNext());

        logger.info("Fingerprint Browser V2 - Copy - End;");
    }

    private void fingerprintV1AndDevice(Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate){
        logger.info("Fingerprint Browser V1 and Device - Copy - Begin;");

    Pageable pageFingerprintUserHashesSearch = PageRequest.of(0, chunkSize);
        Page<FingerprintV1AndDeviceInfo> fingerprintUserHashesPage;
        do {
            fingerprintUserHashesPage = iFingerprintService.fingerprintUserHashesFindAll(pageFingerprintUserHashesSearch, fromDate, toDate);
            iFingerprintService.copyFingerprint(fingerprintUserHashesPage.getContent());
            pageFingerprintUserHashesSearch = pageFingerprintUserHashesSearch.next();
        } while (fingerprintUserHashesPage.hasNext());

        logger.info("Fingerprint Browser V1 and Device - Copy - End;");
    }

}
