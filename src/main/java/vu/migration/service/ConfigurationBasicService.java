package vu.migration.service;

import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vu.data.models.administration.list.SourceBlockedIpValues;
import vu.data.models.administration.settings.parameter.SourceParameter;
import vu.data.models.administration.settings.parameter.SourceParameterChannelOperation;
import vu.data.models.administration.system.SourceSystemParameter;
import vu.data.models.administration.user.SourcePrivilege;
import vu.data.models.administration.user.SourceRole;
import vu.data.models.administration.user.SourceUser;
import vu.data.models.fraud.rule.general.SourceRuleCondition;
import vu.data.models.fraud.rule.general.SourceRuleEventStatement;
import vu.data.models.fraud.rule.general.SourceRuleItem;
import vu.migration.eventProcessing.model.RuleCepEventStatement;
import vu.migration.eventProcessing.model.RuleCepItem;
import vu.migration.mappers.BlockedIpMapper;
import vu.migration.mappers.ParameterChannelOperationMapper;
import vu.migration.service.administration.apikey.IApiKeyService;
import vu.migration.service.administration.list.IListService;
import vu.migration.service.administration.settings.channel.IChannelService;
import vu.migration.service.administration.settings.operation.IOperationTypeService;
import vu.migration.service.administration.settings.parameter.IParameterChannelOperationService;
import vu.migration.service.administration.settings.parameter.IParameterService;
import vu.migration.service.administration.system.ISystemParameterService;
import vu.migration.service.auth.ILdapRolService;
import vu.migration.service.auth.IPrivilegeService;
import vu.migration.service.auth.IRoleService;
import vu.migration.service.auth.IUserService;
import vu.migration.service.fraud.event.EventTypeEnumName;
import vu.migration.service.fraud.rule.cep.IEventProcessingRuleService;
import vu.migration.service.fraud.rule.general.IRuleItemService;
import vu.migration.service.notification.INotificationConfigService;
import vu.migration.service.notification.INotificationTypeService;
import vu.migration.service.notification.group.IBusinessGroupService;
import vu.migration.service.notification.group.IParameterByBusinessService;
import vu.migration.service.notification.group.IUserBusinessGroupService;
import vu.migration.target.entities.administration.apikey.ApiKey;
import vu.migration.target.entities.administration.list.BlockedIpValues;
import vu.migration.target.entities.administration.settings.parameter.Parameter;
import vu.migration.target.entities.administration.settings.parameter.ParameterChannelOperation;
import vu.migration.target.entities.administration.system.SystemParameter;
import vu.migration.target.entities.administration.user.Privilege;
import vu.migration.target.entities.administration.user.Role;
import vu.migration.target.entities.administration.user.User;
import vu.migration.target.entities.fraud.rule.general.RuleCondition;
import vu.migration.target.entities.fraud.rule.general.RuleItem;
import vu.migration.target.entities.fraud.transaction.CepRuleMatchedTransaction;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Service
public class ConfigurationBasicService{
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationBasicService.class);

    private final ISystemParameterService iSystemParameterService;
    private final IApiKeyService iApiKeyService;
    private final IRoleService iRoleService;
    private final IPrivilegeService iPrivilegeService;
    private final IUserService iUserService;
    private final IChannelService iChannelService;
    private final IOperationTypeService iOperationTypeService;
    private final IParameterService iParameterService;
    private final IParameterChannelOperationService iParameterChannelOperationService;
    private final IRuleItemService iRuleItemService;
    private final IListService iListService;
    private final IBusinessGroupService iBusinessGroupService;
    private final IParameterByBusinessService iParameterByBusinessService;
    private final IUserBusinessGroupService iUserBusinessGroupService;
    private final INotificationTypeService iNotificationTypeService;
    private final INotificationConfigService iNotificationConfigService;
    private final ILdapRolService iLdapRolService;
    private final IEventProcessingRuleService iEventProcessingRuleService;
    private final BlockedIpMapper blockedIpMapper;
    private final ParameterChannelOperationMapper parameterChannelOperationMapper;

    public ConfigurationBasicService(ISystemParameterService iSystemParameterService,IApiKeyService iApiKeyService,IRoleService iRoleService,IPrivilegeService iPrivilegeService,IUserService iUserService,IChannelService iChannelService,IOperationTypeService iOperationTypeService,IParameterService iParameterService,IParameterChannelOperationService iParameterChannelOperationService,IRuleItemService iRuleItemService,IListService iListService,IBusinessGroupService iBusinessGroupService,IParameterByBusinessService iParameterByBusinessService,IUserBusinessGroupService iUserBusinessGroupService,INotificationTypeService iNotificationTypeService,INotificationConfigService iNotificationConfigService,ILdapRolService iLdapRolService,IEventProcessingRuleService iEventProcessingRuleService,BlockedIpMapper blockedIpMapper,ParameterChannelOperationMapper parameterChannelOperationMapper){
        this.iSystemParameterService=iSystemParameterService;
        this.iApiKeyService=iApiKeyService;
        this.iRoleService=iRoleService;
        this.iPrivilegeService=iPrivilegeService;
        this.iUserService=iUserService;
        this.iChannelService=iChannelService;
        this.iOperationTypeService=iOperationTypeService;
        this.iParameterService=iParameterService;
        this.iParameterChannelOperationService=iParameterChannelOperationService;
        this.iRuleItemService=iRuleItemService;
        this.iListService=iListService;
        this.iBusinessGroupService=iBusinessGroupService;
        this.iParameterByBusinessService=iParameterByBusinessService;
        this.iUserBusinessGroupService=iUserBusinessGroupService;
        this.iNotificationTypeService=iNotificationTypeService;
        this.iNotificationConfigService=iNotificationConfigService;
        this.iLdapRolService=iLdapRolService;
        this.iEventProcessingRuleService=iEventProcessingRuleService;
        this.blockedIpMapper=blockedIpMapper;
        this.parameterChannelOperationMapper=parameterChannelOperationMapper;
    }

    ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    private static final String EVENT_PROCESSING_ID = "Event Processing ID: ";
    private static final String NOT_SAVE = " Not save;";

    private Map<Integer, CepRuleMatchedTransaction> eventProcessingMap;
    @Value("${spring.jpa.properties.hibernate.jdbc.batch_size}")
    private int sizePage;

    public void updateSystemParameter(){
        logger.info("SystemParameter - Update - Begin;");

        List<SourceSystemParameter> sourceSystemParameters= iSystemParameterService.getAllSystemParameter();

        List<SystemParameter> systemParameterList = new ArrayList<>();
        for(SourceSystemParameter sourceSystemParameter : sourceSystemParameters){
            SystemParameter parameter = iSystemParameterService.findByParameterName(sourceSystemParameter.getParameterName());
            if(parameter != null){
                parameter.setConfigValue(sourceSystemParameter.getConfigValue());
                systemParameterList.add(parameter);
            }
        }
        iSystemParameterService.saveAllSystemParameter(systemParameterList);

        logger.info("SystemParameter - Update - End;");
    }

    public void copyApiKey(){
        logger.info("ApiKey - Copy - Begin;");
        List<ApiKey> apiKeys = iApiKeyService.getAllApiKey();
        iApiKeyService.saveAllApiKey(apiKeys);
        logger.info("ApiKey - Copy - End;");
    }

    public void copyAuthRole(){
        logger.info("AuthRole - Copy - Begin;");
        List<SourceRole> roles = iRoleService.getAllRoles();

        List<Role> roleList = new ArrayList<>();
        for(SourceRole role:roles){
            if(!role.getName().toUpperCase(Locale.ROOT).equals("ADMINISTRADOR")){
                Role roleExist = iRoleService.findByName(role.getName());
                if(Objects.nonNull(roleExist)){
                    Role roleToUpdate = iRoleService.findByName(role.getName());
                    roleList.add(chargeAuthRolePrivilege(roleToUpdate,role));
                } else {
                    Role roleTarget = new Role();
                    roleList.add(chargeAuthRolePrivilege(roleTarget,role));
                }
            }
        }

        iRoleService.saveAllRoles(roleList);

        iLdapRolService.copyLdapRole();

        logger.info("AuthRole - Copy - End;");
    }

    private @NotNull Role chargeAuthRolePrivilege(@NotNull Role roleTarget,@NotNull SourceRole sourceRole){
        if(Objects.isNull(roleTarget.getId())){
            roleTarget.setId(sourceRole.getId());
            roleTarget.setName(sourceRole.getName());
        }
        Collection<Privilege> targetPrivilege = new ArrayList<>();
        for(SourcePrivilege sourcePrivilege : sourceRole.getSourcePrivileges()){
            String privilegeName = (sourcePrivilege.getName().equals("ROLE_MANAGEMENT"))? "ROLES_MANAGEMENT" : sourcePrivilege.getName();
            targetPrivilege.add(iPrivilegeService.getPrivilege(privilegeName));
        }
        roleTarget.setPrivileges(targetPrivilege);
        return roleTarget;
    }

    public void copyAuthFaUser(){
        logger.info("AuthFaUser - Copy - Begin;");
        List<SourceUser> users = iUserService.getAllUsers();

        for(SourceUser user:users){
            User userExist = iUserService.findByUserName(user.getUserName());

            if(userExist !=null){
                iUserService.save(authFaUserToSave(userExist,user));
            }else{
                User userTarget = new User();
                userTarget.setId(user.getId());
                iUserService.save(authFaUserToSave(userTarget,user));
            }

        }

        logger.info("AuthFaUser - Copy - End;");
    }

    private @NotNull User authFaUserToSave(@NotNull User userTarget,@NotNull SourceUser user){

        userTarget.setEmail(user.getEmail());
        userTarget.setEnabled(user.isEnabled());
        userTarget.setFirstName(user.getFirstName());
        userTarget.setForceResetPassword(user.isForceResetPassword());
        userTarget.setLastName(user.getLastName());
        userTarget.setLdapUser(user.isLdapUser());
        userTarget.setPassword(user.getPassword());
        userTarget.setRoleChanged(user.isRoleChanged());
        userTarget.setTokenExpired(user.isTokenExpired());
        userTarget.setUserName(user.getUserName());
        for(SourceRole sourceRole :user.getSourceRoles()){
            userTarget.setRoles(iRoleService.findByName(sourceRole.getName()));
        }

        return userTarget;
    }

    public void copyChannel(){
        logger.info("Channel - Copy - Begin;");

        iChannelService.copyChannel();

        logger.info("Channel - Copy - End;");
    }

    public void copyOperationType(){
        logger.info("OperationType - Copy - Begin;");

        iOperationTypeService.copyOperationType();

        logger.info("OperationType - Copy - End;");
    }

    private int getEventTypeId(int eventTypeName) {
        return switch (eventTypeName) {
            case 2 -> EventTypeEnumName.EVENT;
            case 1 -> EventTypeEnumName.TRANSACTION;
            default -> 0;
        };
    }

    public void copyParameter(){
        logger.info("Parameter - Copy - Begin;");
        List<SourceParameter> parameters = iParameterService.getAllParameter();

        List<Parameter> parametersList = new ArrayList<>();

        for(SourceParameter parameter:parameters){
            int sourceEventType= getEventTypeId(parameter.getEventTypeId());
            if(sourceEventType!= 0){
                Parameter parameterExist = iParameterService.getParameterByEventTypeAndName(sourceEventType,parameter.getName());

                if(parameterExist == null){
                    Parameter parameterTarget = new Parameter();
                    parameterTarget.setColumnName(parameter.getColumnName());
                    parameterTarget.setEnabled(true);
                    parameterTarget.setEventTypeId(sourceEventType);
                    parameterTarget.setName(parameter.getName());
                    parameterTarget.setParameterInfoId(parameter.getParameterInfoId());
                    parameterTarget.setParameterTypeId(parameter.getParameterTypeId());
                    parameterTarget.setUserGenerated(parameter.isUserGenerated());
                    parameterTarget.setVisibility(parameter.getVisibility());

                    parametersList.add(parameterTarget);
                }
            }
        }

        iParameterService.saveAllParameters(parametersList);
        logger.info("Parameter - Copy - End;");
    }

    public void copyParameterChannelOperation(){

        logger.info("ParameterChannelOperation - Copy - Begin;");
        List<SourceParameterChannelOperation> sources = iParameterChannelOperationService.getAllParameterChannelOperation();

        List<ParameterChannelOperation> targets = parameterChannelOperationMapper.toTargetParameterChannelOperationList(sources, iParameterService);

        iParameterChannelOperationService.saveAllParameterChannelOperation(targets);

        logger.info("ParameterChannelOperation - Copy - End;");
    }

    public void copyList(){
        logger.info("List - Copy - Begin;");

        iListService.copyBackgroundBatchStatus();
        iListService.copyFileEntry();

        copyBlockedIpList();

        iListService.copyCustomList();

        logger.info("List - Copy - End");
    }

    @Transactional
    private void copyBlockedIpList(){
        logger.info("Blocked-Ip-List - Copy - Begin;");

        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        List<Future<?>> futures = new ArrayList<>();

        int page = 0;
        Page<SourceBlockedIpValues> blockedIpValuesPage;

        do {
            Pageable pageable = PageRequest.of(page, sizePage);
            blockedIpValuesPage = iListService.findAllSourceBlockedIpValues(pageable);
            List<SourceBlockedIpValues> blockedIpValuesToCopy = blockedIpValuesPage.getContent();

            executor.submit(() -> {
                List<BlockedIpValues> transaction = blockedIpMapper.toEntityList(blockedIpValuesToCopy);
                iListService.saveAllBlockedIpValues(transaction);
            });

            page++;
        } while (blockedIpValuesPage.hasNext());

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                logger.error("Error in parallel execution", e);
            }
        }

        executor.shutdown();

        logger.info("Blocked-Ip-List - Copy - End;");
    }

    public void copyBusinessGroup(){
        logger.info("BusinessGroup - Copy - Begin;");

        iBusinessGroupService.copyBusinessGroup();
        iParameterByBusinessService.copyParameterByBusiness();
        iUserBusinessGroupService.copyUserBusinessGroup();

        logger.info("BusinessGroup - Copy - End;");
    }

    public void copyNotification(){
        logger.info("Notification - Copy - Begin;");

        iNotificationTypeService.copyNotificationType();
        iNotificationConfigService.copyNotificationConfig();

        logger.info("Notification - Copy - End;");
    }

    public void copyRules(){
        logger.info("Rules - Copy - Begin;");
        eventProcessingMap = new TreeMap<>();
        List<SourceRuleItem> sourceRuleItems= iRuleItemService.getAllRuleItem();

        List<vu.migration.target.entities.fraud.rule.general.RuleItem> ruleItemList = new ArrayList<>();
        for(SourceRuleItem sourceRuleItem : sourceRuleItems){
            vu.migration.util.RuleType ruleType = vu.migration.util.RuleType.values()[sourceRuleItem.getRuleTypeId()];

            vu.migration.target.entities.fraud.rule.general.RuleItem ruleItemTarget = new vu.migration.target.entities.fraud.rule.general.RuleItem();
            ruleItemTarget.setId(sourceRuleItem.getId());
            ruleItemTarget.setActionId(sourceRuleItem.getActionId());

            if(ruleType != vu.migration.util.RuleType.Cross && ruleType != vu.migration.util.RuleType.EventProcessing && ruleType != vu.migration.util.RuleType.AssignCase){
                ruleItemTarget.setChannelId(sourceRuleItem.getChannelId());
                ruleItemTarget.setOperationTypeId(sourceRuleItem.getOperationTypeId());
            }

            ruleItemTarget.setCriticalLevelId(sourceRuleItem.getCriticalLevelId());
            ruleItemTarget.setEnabled(sourceRuleItem.isEnabled());
            ruleItemTarget.setImpactId(sourceRuleItem.getImpactId());
            ruleItemTarget.setJsonFormat(sourceRuleItem.getJsonFormat());
            ruleItemTarget.setName(sourceRuleItem.getName());
            ruleItemTarget.setOrder(sourceRuleItem.getOrder());
            ruleItemTarget.setProbabilityId(sourceRuleItem.getProbabilityId());
            ruleItemTarget.setRuleTypeId(sourceRuleItem.getRuleTypeId());
            ruleItemTarget.setScore(sourceRuleItem.getScore());
            if(ruleType != vu.migration.util.RuleType.EventProcessing){
                ruleItemList.add(ruleItemTarget);
            } else {
                saveEventProcessingRule(ruleItemTarget);
            }
        }

        iRuleItemService.saveAllRuleItem(ruleItemList);

        copyRuleCondition();

        logger.info("Rules - Copy - End;");
    }

    private void copyRuleCondition(){

        List<SourceRuleCondition> sourceRuleConditions= iRuleItemService.getAllRuleCondition();
        List<RuleCondition> ruleConditionCollection = new ArrayList<>();

        for(SourceRuleCondition sourceRuleCondition : sourceRuleConditions){
            vu.migration.target.entities.fraud.rule.general.RuleCondition ruleConditionTarget=new vu.migration.target.entities.fraud.rule.general.RuleCondition();
            ruleConditionTarget.setId(sourceRuleCondition.getId());
            ruleConditionTarget.setRuleId(sourceRuleCondition.getRuleId());
            ruleConditionTarget.setConditionRuleTypeId(sourceRuleCondition.getConditionRuleTypeId());
            ruleConditionTarget.setConditionTypeId(sourceRuleCondition.getConditionTypeId());
            ruleConditionTarget.setConditionValue(sourceRuleCondition.getConditionValue());
            ruleConditionTarget.setConnectorId(sourceRuleCondition.getConnectorId());
            ruleConditionTarget.setCustomListId(sourceRuleCondition.getCustomListId());
            ruleConditionTarget.setDeltaTypeId(sourceRuleCondition.getDeltaTypeId());
            ruleConditionTarget.setFieldId(sourceRuleCondition.getFieldId());
            ruleConditionTarget.setGroupId(sourceRuleCondition.getGroupId());
            ruleConditionTarget.setOperatorId(sourceRuleCondition.getOperatorId());
            ruleConditionTarget.setParentGroupId(sourceRuleCondition.getParentGroupId());

            ruleConditionCollection.add(ruleConditionTarget);
        }

        iRuleItemService.saveAllRuleCondition(ruleConditionCollection);

    }

    private void saveEventProcessingRule(@NotNull RuleItem ruleItemTarget){
        RuleCepItem rule = new RuleCepItem();
        rule.setId(ruleItemTarget.getId());
        rule.setIdAction(ruleItemTarget.getActionId());
        rule.setEnabled(false);
        rule.setName(ruleItemTarget.getName());
        rule.setIdCriticalLevel(ruleItemTarget.getCriticalLevelId());
        Set<RuleCepEventStatement> listEventStatement = new HashSet<>();
        for(SourceRuleEventStatement sourceRuleEventStatement :iRuleItemService.findAllRuleEventStatementByRuleId(ruleItemTarget.getId())){
            RuleCepEventStatement eventStatement = new RuleCepEventStatement();
            String value;
            if(sourceRuleEventStatement.isContext()){
                value = "create context SegmentedByUserId partition by userId from Transaction";
            }else{
                value = "context SegmentedByUserId select sum(amount) from Transaction.win:time(1 hour) having sum(amount) >= 2000000000";
            }
            eventStatement.setStatement(value);
            eventStatement.setContext(sourceRuleEventStatement.isContext());
            listEventStatement.add(eventStatement);
        }
        rule.setRuleEventStatements(listEventStatement);
        rule.setJsonFormat(ruleItemTarget.getJsonFormat());

        iEventProcessingRuleService.save(rule);
    }

}
