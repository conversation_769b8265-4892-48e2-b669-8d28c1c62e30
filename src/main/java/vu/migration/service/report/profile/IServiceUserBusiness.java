package vu.migration.service.report.profile;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vu.migration.target.entities.profile.UserBusinessStatDetailDay;
import vu.migration.target.entities.profile.UserBusinessStatDetailMonthly;
import vu.migration.target.entities.profile.UserBusinessStatItem;
import vu.migration.target.entities.profile.UserBusinessStatsDetailWeekly;

public interface IServiceUserBusiness{

    Page<UserBusinessStatItem> userBusinessStatItemFindAll(Pageable page);

    Page<UserBusinessStatDetailDay> userBusinessStatDetailDayFindAll(Pageable page);

    Page<UserBusinessStatsDetailWeekly> userBusinessStatDetailWeeklyFindAll(Pageable page);

    Page<UserBusinessStatDetailMonthly> userBusinessStatDetailMonthlyFindAll(Pageable page);
}
