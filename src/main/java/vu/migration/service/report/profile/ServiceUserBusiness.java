package vu.migration.service.report.profile;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vu.migration.source.repository.report.profile.SourceUserBusinessStatsDailyRepository;
import vu.migration.source.repository.report.profile.SourceUserBusinessStatsMonthlyRepository;
import vu.migration.source.repository.report.profile.SourceUserBusinessStatsRepository;
import vu.migration.source.repository.report.profile.SourceUserBusinessStatsWeeklyRepository;
import vu.migration.target.entities.profile.UserBusinessStatDetailDay;
import vu.migration.target.entities.profile.UserBusinessStatDetailMonthly;
import vu.migration.target.entities.profile.UserBusinessStatItem;
import vu.migration.target.entities.profile.UserBusinessStatsDetailWeekly;

@Service
public class ServiceUserBusiness implements IServiceUserBusiness{

    private final SourceUserBusinessStatsRepository sourceUserBusinessStatsRepository;
    private final SourceUserBusinessStatsDailyRepository sourceUserBusinessStatsDailyRepository;
    private final SourceUserBusinessStatsWeeklyRepository sourceUserBusinessStatsWeeklyRepository;
    private final SourceUserBusinessStatsMonthlyRepository sourceUserBusinessStatsMonthlyRepository;


    public ServiceUserBusiness(SourceUserBusinessStatsRepository sourceUserBusinessStatsRepository,SourceUserBusinessStatsDailyRepository sourceUserBusinessStatsDailyRepository,SourceUserBusinessStatsWeeklyRepository sourceUserBusinessStatsWeeklyRepository,SourceUserBusinessStatsMonthlyRepository sourceUserBusinessStatsMonthlyRepository){
        this.sourceUserBusinessStatsRepository=sourceUserBusinessStatsRepository;
        this.sourceUserBusinessStatsDailyRepository=sourceUserBusinessStatsDailyRepository;
        this.sourceUserBusinessStatsWeeklyRepository=sourceUserBusinessStatsWeeklyRepository;
        this.sourceUserBusinessStatsMonthlyRepository=sourceUserBusinessStatsMonthlyRepository;
    }

    @Override
    public Page<UserBusinessStatItem> userBusinessStatItemFindAll(Pageable page){
        return sourceUserBusinessStatsRepository.getAllUserBusinessStatItem(page);
    }

    @Override
    public Page<UserBusinessStatDetailDay> userBusinessStatDetailDayFindAll(Pageable page){
        return sourceUserBusinessStatsDailyRepository.getAllUserBusinessStatDetailDay(page);
    }

    @Override
    public Page<UserBusinessStatsDetailWeekly> userBusinessStatDetailWeeklyFindAll(Pageable page){
        return sourceUserBusinessStatsWeeklyRepository.getAllUserBusinessStatDetailWeekly(page);
    }

    @Override
    public Page<UserBusinessStatDetailMonthly> userBusinessStatDetailMonthlyFindAll(Pageable page){
        return sourceUserBusinessStatsMonthlyRepository.getAllUserBusinessStatDetailMonthly(page);
    }
}
