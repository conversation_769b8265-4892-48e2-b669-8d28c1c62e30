package vu.migration.service.report.profile;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import vu.data.models.report.profile.SourceProfileByOperation;
import vu.data.models.report.profile.SourceProfilePeriodInfoByCreditAccount;
import vu.data.models.report.profile.SourceProfilePeriodInformation;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.source.repository.report.profile.SourceProfileByOperationRepository;
import vu.migration.source.repository.report.profile.SourceProfilePeriodInfoByCreditAccountRepository;
import vu.migration.source.repository.report.profile.SourceProfilePeriodInformationRepository;
import vu.migration.target.entities.profile.ProfileByOperation;
import vu.migration.target.entities.profile.ProfilePeriodInfoByCreditAccount;
import vu.migration.target.entities.profile.ProfilePeriodInformation;
import vu.migration.target.repositories.report.profile.ProfileByOperationRepository;
import vu.migration.target.repositories.report.profile.ProfilePeriodInfoByCreditAccountRepository;
import vu.migration.target.repositories.report.profile.ProfilePeriodInformationRepository;
import vu.migration.util.SpecificationUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class ServiceProfile implements IServiceProfile{

    private final SourceProfileByOperationRepository sourceProfileByOperationRepository;
    private final SourceProfilePeriodInformationRepository sourceProfilePeriodInformationRepository;
    private final SourceProfilePeriodInfoByCreditAccountRepository sourceProfilePeriodInfoByCreditAccountRepository;
    private final ProfileByOperationRepository profileByOperationRepository;
    private final ProfilePeriodInformationRepository profilePeriodInformationRepository;
    private final ProfilePeriodInfoByCreditAccountRepository profilePeriodInfoByCreditAccountRepository;

    @PersistenceContext(unitName = "Target")
    private EntityManager targetEntityManager;

    public ServiceProfile(SourceProfileByOperationRepository sourceProfileByOperationRepository,SourceProfilePeriodInformationRepository sourceProfilePeriodInformationRepository,SourceProfilePeriodInfoByCreditAccountRepository sourceProfilePeriodInfoByCreditAccountRepository,ProfileByOperationRepository profileByOperationRepository,ProfilePeriodInformationRepository profilePeriodInformationRepository,ProfilePeriodInfoByCreditAccountRepository profilePeriodInfoByCreditAccountRepository){
        this.sourceProfileByOperationRepository=sourceProfileByOperationRepository;
        this.sourceProfilePeriodInformationRepository=sourceProfilePeriodInformationRepository;
        this.sourceProfilePeriodInfoByCreditAccountRepository=sourceProfilePeriodInfoByCreditAccountRepository;
        this.profileByOperationRepository=profileByOperationRepository;
        this.profilePeriodInformationRepository=profilePeriodInformationRepository;
        this.profilePeriodInfoByCreditAccountRepository=profilePeriodInfoByCreditAccountRepository;
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Optional<IdRange> findMinAndMaxIdByDateRange(LocalDateTime fromDate,LocalDateTime toDate){
        int fromPeriod = fromDate.getYear() * 100 + fromDate.getMonthValue();
        int toPeriod = toDate.getYear() * 100 + toDate.getMonthValue();
        return sourceProfilePeriodInformationRepository.findMinAndMaxIdByDateRange(fromPeriod, toPeriod);
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Optional<IdRange> findMinAndMaxId(){
        return sourceProfilePeriodInformationRepository.findMinAndMaxId();
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<SourceProfilePeriodInformation> findProfilePeriodInformationWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size) {
        Specification<SourceProfilePeriodInformation> spec = SpecificationUtils.buildRangeSpec("id", fromId, toId);

        Pageable pageable = SpecificationUtils.buildPageable(fromId, toId, page, size);

        return sourceProfilePeriodInformationRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyProfilePeriodInformation(List<ProfilePeriodInformation> profilePeriodInformation) {
        profilePeriodInformationRepository.saveAll(profilePeriodInformation);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<SourceProfilePeriodInfoByCreditAccount> findProfilePeriodInfoByCreditAccountWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size){
        Specification<SourceProfilePeriodInfoByCreditAccount> spec = SpecificationUtils.buildRangeSpec("profilePeriodInfoId", fromId, toId);

        Pageable pageable = SpecificationUtils.buildPageable(fromId, toId, page, size);

        return sourceProfilePeriodInfoByCreditAccountRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyProfilePeriodInfoByCreditAccount(List<ProfilePeriodInfoByCreditAccount> profilePeriodInfoByCreditAccount){
        profilePeriodInfoByCreditAccountRepository.saveAll(profilePeriodInfoByCreditAccount);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<SourceProfileByOperation> findProfileByOperationWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size){
        Specification<SourceProfileByOperation> spec = SpecificationUtils.buildRangeSpec("profilePeriodInfoId", fromId, toId);

        Pageable pageable = SpecificationUtils.buildPageable(fromId, toId, page, size);

        return sourceProfileByOperationRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyProfileByOperation(List<ProfileByOperation> profileByOperation){
        profileByOperationRepository.saveAll(profileByOperation);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }
}
