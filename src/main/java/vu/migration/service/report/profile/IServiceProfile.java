package vu.migration.service.report.profile;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import vu.data.models.report.profile.SourceProfileByOperation;
import vu.data.models.report.profile.SourceProfilePeriodInfoByCreditAccount;
import vu.data.models.report.profile.SourceProfilePeriodInformation;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.target.entities.profile.ProfileByOperation;
import vu.migration.target.entities.profile.ProfilePeriodInfoByCreditAccount;
import vu.migration.target.entities.profile.ProfilePeriodInformation;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface IServiceProfile{

    Optional<IdRange> findMinAndMaxIdByDateRange(LocalDateTime fromDate,LocalDateTime toDate);

    Optional<IdRange> findMinAndMaxId();

    Page<SourceProfilePeriodInformation> findProfilePeriodInformationWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size);

    @Transactional
    void copyProfilePeriodInformation(List<ProfilePeriodInformation> profilePeriodInformation);

    Page<SourceProfilePeriodInfoByCreditAccount> findProfilePeriodInfoByCreditAccountWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size);

    @Transactional
    void copyProfilePeriodInfoByCreditAccount(List<ProfilePeriodInfoByCreditAccount> profilePeriodInfoByCreditAccount);

    Page<SourceProfileByOperation> findProfileByOperationWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size);

    @Transactional
    void copyProfileByOperation(List<ProfileByOperation> profileByOperation);
}
