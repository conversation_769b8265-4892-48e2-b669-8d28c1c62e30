package vu.migration.service.fingerprint.rule.browser;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import vu.migration.service.fingerprint.mapper.FingerprintHashPrototype;
import vu.migration.service.interfaces.fingerprint.rule.IHashFingerprintService;
import vu.migration.target.common.Paged;
import vu.migration.target.entities.common.Filter;
import vu.migration.target.entities.fingerprint.FingerprintHash;
import vu.migration.target.entities.fingerprint.FingerprintType;
import vu.migration.target.entities.fingerprint.FingerprintUserHash;
import vu.migration.target.entities.fingerprint.browser.FingerprintWebHash;
import vu.migration.target.repositories.fingerprint.FingerprintSpecification;
import vu.migration.target.repositories.fingerprint.FingerprintUserHashRepository;

import java.util.List;
import java.util.stream.Collectors;

@Service("BrowserFingerprint")
public class BrowserFingerprintService implements IHashFingerprintService{

    private FingerprintUserHashRepository fingerprintUserHashRepository;

    public BrowserFingerprintService(FingerprintUserHashRepository fingerprintUserHashRepository) {
        this.fingerprintUserHashRepository = fingerprintUserHashRepository;
    }

    @Override
    public Paged<FingerprintHash> getPagedHashes(Filter filter,FingerprintType fingerprintType) {
        
        int start = filter.getIntegerValue("start");
        int end = filter.getIntegerValue("end");

        int page = start / end;
        PageRequest pageRequest = PageRequest.of(page, end);

        Specification<FingerprintUserHash> spec = Specification.where(FingerprintSpecification.browserFingerprintTypeOrdered(filter,fingerprintType));
        Page<FingerprintUserHash> fingerprintUserHashes = fingerprintUserHashRepository.findAll(spec, pageRequest);

        List<FingerprintHash> fingerprintHashes = fingerprintUserHashes.getContent().stream()
                .map(t -> FingerprintHashPrototype.cloneFrom(t))
                .collect(Collectors.toList());

        Paged<FingerprintHash> result = new Paged<>();
        result.setElements(fingerprintHashes);
        result.setTotalPages(fingerprintUserHashes.getTotalPages());
        result.setTotalElements(fingerprintUserHashes.getTotalElements());

        return result;
    }



    @Override
    public boolean verifyFingerprintCoincidence(FingerprintUserHash fingerprintUserHash, FingerprintHash compareTo) {
        FingerprintWebHash webHashFingerprint = (FingerprintWebHash) fingerprintUserHash.getFingerprintHash();

        FingerprintWebHash fingerprint = (FingerprintWebHash) compareTo;

        if (fingerprintUserHash.getStatus() == FingerprintHash.Status.VERIFIED
                && webHashFingerprint.getFingerprintValues().getLocalStorageId() != null) {
            return (webHashFingerprint.getFingerprintValues().getLocalStorageId().equals(fingerprint.getFingerprintValues().getLocalStorageId()));
        }
        return ((fingerprintUserHash.getStatus() == FingerprintHash.Status.VERIFIED)
                && (webHashFingerprint.getFingerprintAgentValues().getOs().getName().compareTo(fingerprint.getFingerprintAgentValues().getOs().getName()) == 0
                && webHashFingerprint.getFingerprintAgentValues().getOs().getVersion().compareTo(fingerprint.getFingerprintAgentValues().getOs().getVersion()) == 0
                && webHashFingerprint.getFingerprintAgentValues().getCpu().getArchitecture().compareTo(fingerprint.getFingerprintAgentValues().getCpu().getArchitecture()) == 0
                && webHashFingerprint.getFingerprintAgentValues().getBrowserName().compareTo(fingerprint.getFingerprintAgentValues().getBrowserName()) == 0));
    }
}
