package vu.migration.service.fingerprint.rule;


import vu.migration.service.fingerprint.FingerprintResult;

public class FingerprintAndAnalyzeResult extends FingerprintResult{

    private int analyzeResult;

    public FingerprintAndAnalyzeResult() {
    }

    public FingerprintAndAnalyzeResult(FingerprintResult fingerprintResult) {
        this.setAccuracyPercent(fingerprintResult.getAccuracyPercent());
        this.setToken(fingerprintResult.getToken());
        this.setExistsHash(fingerprintResult.isExistsHash());
        this.setCode(fingerprintResult.getCode());
        this.setDescription(fingerprintResult.getDescription());
        this.setTriggeredRules(fingerprintResult.getTriggeredRules());
        this.setCollection(fingerprintResult.getCollection());
        this.setFingerprint(fingerprintResult.getFingerprint());
    }

    public int getAnalyzeResult() {
        return analyzeResult;
    }

    public void setAnalyzeResult(int analyzeResult) {
        this.analyzeResult = analyzeResult;
    }
}
