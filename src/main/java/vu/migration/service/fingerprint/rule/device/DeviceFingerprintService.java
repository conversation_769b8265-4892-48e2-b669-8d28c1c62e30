package vu.migration.service.fingerprint.rule.device;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import vu.migration.service.fingerprint.mapper.FingerprintHashPrototype;
import vu.migration.service.interfaces.fingerprint.rule.IHashFingerprintService;
import vu.migration.target.common.Paged;
import vu.migration.target.entities.common.Filter;
import vu.migration.target.entities.fingerprint.FingerprintDeviceValue;
import vu.migration.target.entities.fingerprint.FingerprintHash;
import vu.migration.target.entities.fingerprint.FingerprintType;
import vu.migration.target.entities.fingerprint.FingerprintUserHash;
import vu.migration.target.repositories.fingerprint.FingerprintSpecification;
import vu.migration.target.repositories.fingerprint.FingerprintUserHashRepository;

import java.util.List;
import java.util.stream.Collectors;

@Service("DeviceFingerprint")
public class DeviceFingerprintService implements IHashFingerprintService{

    private FingerprintUserHashRepository fingerprintUserHashRepository;

    public DeviceFingerprintService(FingerprintUserHashRepository fingerprintUserHashRepository) {
        this.fingerprintUserHashRepository = fingerprintUserHashRepository;
    }

    @Override
    public Paged<FingerprintHash> getPagedHashes(Filter filter,FingerprintType fingerprintType) {

        int start = filter.getIntegerValue("start");
        int end = filter.getIntegerValue("end");

        int page = start / end;
        PageRequest pageRequest = PageRequest.of(page, end);

        Specification<FingerprintUserHash> spec = Specification.where(FingerprintSpecification.deviceFingerprintTypeOrdered(filter));
        Page<FingerprintUserHash> fingerprintUserHashes = fingerprintUserHashRepository.findAll(spec, pageRequest);

        List<FingerprintHash> fingerprintHashes = fingerprintUserHashes.getContent().stream()
                .map(t -> FingerprintHashPrototype.cloneFrom(t))
                .collect(Collectors.toList());

        Paged<FingerprintHash> result = new Paged<>();
        result.setElements(fingerprintHashes);
        result.setTotalPages(fingerprintUserHashes.getTotalPages());
        result.setTotalElements(fingerprintUserHashes.getTotalElements());

        return result;
    }

    @Override
    public boolean verifyFingerprintCoincidence(FingerprintUserHash fingerprintUserHash, FingerprintHash compareTo) {
        FingerprintDeviceValue deviceHashFingerprint = (FingerprintDeviceValue) fingerprintUserHash.getFingerprintHash();

        FingerprintDeviceValue fingerprint = (FingerprintDeviceValue) compareTo;

        if (fingerprintUserHash.getStatus() == FingerprintHash.Status.VERIFIED && deviceHashFingerprint.getRandomSeed() != null) {
            return (deviceHashFingerprint.getRandomSeed().equals(fingerprint.getRandomSeed()));
        }

        return ((fingerprintUserHash.getStatus() == FingerprintHash.Status.VERIFIED)
                && deviceHashFingerprint.getDeviceId().compareTo(fingerprint.getDeviceId()) == 0);
    }
}
