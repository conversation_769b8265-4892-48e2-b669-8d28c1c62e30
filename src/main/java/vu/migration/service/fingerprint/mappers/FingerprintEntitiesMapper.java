package vu.migration.service.fingerprint.mappers;

import vu.migration.service.fingerprint.SystemInfo;
import vu.migration.service.fingerprint.v2.BrowserFingerprintInfo;
import vu.migration.service.fingerprint.v2.ServerFingerprintInfo;
import vu.migration.target.entities.fingerprint.browser.agent.FingerprintAgentValue;
import vu.migration.target.entities.fingerprint.browser.builders.*;
import vu.migration.target.entities.fingerprint.browser.server.FingerprintServer;
import vu.migration.target.entities.fingerprint.browser.server.GeoData;
import vu.migration.target.entities.fingerprint.browser.server.UserData;
import vu.migration.target.entities.fingerprint.browser.values.*;

public class FingerprintEntitiesMapper{

    private FingerprintEntitiesMapper(){}

    public static FingerprintAgentValue mapSystemInfo(SystemInfo systemInfo) {

        FingerprintAgentValueBuilder builder = new FingerprintAgentValueBuilder();

        return builder.withUserAgent(systemInfo.getUa())
                .withBrowser(systemInfo.getBrowser().getName(), String.valueOf(systemInfo.getBrowser().getMajor()), systemInfo.getBrowser().getVersion())
                .withCpu(systemInfo.getCpu().getArchitecture())
                .withDevice(systemInfo.getDevice().getModel(), systemInfo.getDevice().getType(), systemInfo.getDevice().getVendor())
                .withEngine(systemInfo.getEngine().getName(), systemInfo.getEngine().getVersion())
                .withOperatingSystem(systemInfo.getOs().getName(), systemInfo.getOs().getVersion())
                .build();
    }

    public static FingerprintValues mapBrowserFingerprintInfo(BrowserFingerprintInfo fingerprintInfo) {

        BrowserFingerprintValuesBuilder builder = new BrowserFingerprintValuesBuilder();

        return builder.withLocalstorageID(fingerprintInfo.getLocalstorageID())
                .withLanguage(fingerprintInfo.getLanguage())
                .withTimezone(fingerprintInfo.getTimezone())
                .withVendor(fingerprintInfo.getVendor())
                .withVendorFlavors(fingerprintInfo.getVendorFlavors())
                .withSessionStorage(fingerprintInfo.isHasSessionStorage())
                .withLocalStorage(fingerprintInfo.isHasLocalStorage())
                .withIndexedDb(fingerprintInfo.isHasIndexedDb())
                .withOpenDatabase(fingerprintInfo.isHasOpenDatabase())
                .withCookiesEnabled(fingerprintInfo.isCookiesEnabled())
                .withNavigatorPlatform(fingerprintInfo.getNavigatorPlatform())
                .withExtendedFonts(fingerprintInfo.getExtendedFonts())
                .withFonts(fingerprintInfo.getFonts())
                .withAudio(fingerprintInfo.getAudio())
                .withDomBlocker(fingerprintInfo.getDomBlocker())
                .withMaths(mapMaths(fingerprintInfo.getMaths()))
                .withFontsPreferences(mapFontPreferences(fingerprintInfo.getFontsPreferences()))
                .withCrcCanvas(mapCrcCanvas(fingerprintInfo.getCrcCanvas()))
                .withHardware(mapHardware(fingerprintInfo.getHardware()))
                .withMedia(mapMedia(fingerprintInfo.getMedia()))
                .withTouchSupport(mapTouchSupport(fingerprintInfo.getTouchSupport()))
                .withScreen(mapScreen(fingerprintInfo.getScreen()))
                .build();
    }

    private static Screen mapScreen(BrowserFingerprintInfo.Screen screen) {

        return new ScreenBuilder()
                .withColorDepth(screen.getColorDepth())
                .withResolution(screen.getResolution().getScreenWidth(), screen.getResolution().getScreenHeight())
                .withFrameSize(screen.getFrameSize())
                .build();
    }

    private static TouchSupport mapTouchSupport(BrowserFingerprintInfo.TouchSupport touchSupport) {
        return new TouchSupportBuilder()
                .withMaxTouchPoints(touchSupport.getMaxTouchPoints())
                .withTouchEvent(touchSupport.isTouchEvent())
                .withTouchStart(touchSupport.isTouchStart())
                .build();
    }

    private static Media mapMedia(BrowserFingerprintInfo.Media media) {

        return new MediaBuilder()
                .withColorGamut(media.getColorGamut())
                .withInvertedColors(media.getInvertedColors())
                .withForcedColors(media.isForcedColors())
                .withMonochrome(media.getMonochrome())
                .withContrast(media.getContrast())
                .withHdr(media.isHdr())
                .withReducedMotion(media.isReducedMotion())
                .build();
    }

    private static Hardware mapHardware(BrowserFingerprintInfo.Hardware hardware) {

        return new HardwareBuilder()
                .withLogicalProcessors(hardware.getLogicalProcessors())
                .withMemory(hardware.getMemory())
                .withGpu(hardware.getGpu())
                .build();
    }

    private static CrcCanvas mapCrcCanvas(BrowserFingerprintInfo.CrcCanvas crcCanvas) {

        return new CrcCanvasBuilder()
                .withGeometry(crcCanvas.getGeometry())
                .withText(crcCanvas.getText())
                .withWebgl(crcCanvas.getWebgl())
                .build();
    }

    private static FontPreferences mapFontPreferences(BrowserFingerprintInfo.FontPreferences fontsPreferences) {
        BrowserFingerprintValuesBuilder.FontPreferencesBuilder fontPrefBuilder = new BrowserFingerprintValuesBuilder.FontPreferencesBuilder();

        return fontPrefBuilder.withDefaultValue(fontsPreferences.getDefault())
                .withApple(fontsPreferences.getApple())
                .withMin(fontsPreferences.getMin())
                .withMono(fontsPreferences.getMono())
                .withSans(fontsPreferences.getSans())
                .withSerif(fontsPreferences.getSerif())
                .withSystem(fontsPreferences.getSystem())
                .build();
    }

    private static Maths mapMaths(BrowserFingerprintInfo.Maths mathsViewModel) {
        BrowserFingerprintValuesBuilder.MathsBuilder mathsBuilder = new BrowserFingerprintValuesBuilder.MathsBuilder();

        return mathsBuilder.withAcos(mathsViewModel.getAcos())
                .withAcosh(mathsViewModel.getAcosh())
                .withAcoshPf(mathsViewModel.getAcoshPf())
                .withAsin(mathsViewModel.getAsin())
                .withAsinh(mathsViewModel.getAsinh())
                .withAsinhPf(mathsViewModel.getAsinhPf())
                .withAtanh(mathsViewModel.getAtanh())
                .withAtanhPf(mathsViewModel.getAtanhPf())
                .withAtan(mathsViewModel.getAtan())
                .withSin(mathsViewModel.getSin())
                .withSinh(mathsViewModel.getSinh())
                .withSinhPf(mathsViewModel.getSinhPf())
                .withCos(mathsViewModel.getCos())
                .withCosh(mathsViewModel.getCosh())
                .withCoshPf(mathsViewModel.getCoshPf())
                .withTan(mathsViewModel.getTan())
                .withTanh(mathsViewModel.getTanh())
                .withTanhPf(mathsViewModel.getTanhPf())
                .withExp(mathsViewModel.getExp())
                .withExpm1(mathsViewModel.getExpm1())
                .withExpm1Pf(mathsViewModel.getExpm1Pf())
                .withLog1p(mathsViewModel.getLog1p())
                .withLog1pPf(mathsViewModel.getLog1pPf())
                .withPowPI(mathsViewModel.getPowPI())
                .build();
    }

    public static FingerprintServer mapServerSide(ServerFingerprintInfo serverSide) {

        FingerprintServer serverFingerprintInfo = new FingerprintServer();
        serverFingerprintInfo.setSignature(serverSide.getSignature());
        serverFingerprintInfo.setUserData(mapUserData(serverSide.getUserData()));

        return serverFingerprintInfo;
    }
    private static UserData mapUserData(ServerFingerprintInfo.UserData userData) {

        GeoData geoData = new GeoDataBuilder()
                .withAccuracyRadius(userData.getGeoData().getAccuracyRadius())
                .withLatitude(userData.getGeoData().getLatitude())
                .withLongitude(userData.getGeoData().getLongitude())
                .withCountry(userData.getGeoData().getCountry())
                .withCity(userData.getGeoData().getCity())
                .withSubdivisions(userData.getGeoData().getSubdivisions())
                .build();

        return new UserDataBuilder()
                .withTimeStampUtc(userData.getTimeStampUtc())
                .withHttp2Fingerprint(userData.getHttp2Fingerprint())
                .withHttp2FingerprintHash(userData.getHttp2FingerprintHash())
                .withTlsFingerprint(userData.getTlsFingerprint())
                .withTlsFingerprintHash(userData.getTlsFingerprintHash())
                .withTlsFingerprintSslGreased(userData.getTlsFingerprintSslGreased())
                .withHeaders(userData.getHeaders())
                .withHeaderSignature(userData.getHeaderSignature())
                .withEtag(userData.getEtag())
                .withEtagFirstRequest(userData.getEtagFirstRequest())
                .withGeoData(geoData)
                .withIp(userData.getIp())
                .withCookie(userData.getCookie())
                .build();

    }

}
