package vu.migration.service.fingerprint;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.UUID;

@Data
@EqualsAndHashCode(callSuper=false)
public class FingerprintResult extends BaseResult {

    private boolean existsHash;
    private double accuracyPercent;
    private String token;
    private List<String> triggeredRules;
    private UUID collection;

    private String fingerprint;
}
