package vu.migration.service.fingerprint.v1;

import com.fasterxml.jackson.annotation.JsonAlias;
import vu.migration.target.entities.fingerprint.ScreenResolutionInfo;

public class FingerprintInfo{

    private String randomSeed;
    private String language;
    private int colorDepth;
    @JsonAlias("screenResolutionInfo")
    ScreenResolutionInfo screenResolution;
    private int timezoneOffset;
    private boolean hasSessionStorage;
    private boolean hasLocalStorage;
    private boolean hasIndexedDb;
    private boolean hasAddBehavior;
    private boolean hasOpenDatabase;
    private String navigatorCpuClass;
    private String navigatorPlatform;
    private String doNotTrack;
    private String plugins;
    private boolean hasAdBlock;
    private boolean hasLiedLanguages;
    private boolean hasLiedResolution;
    private boolean hasLiedOs;
    private String fonts;
    private String crcCanvas;
    private String crcWebGl;

    public String getRandomSeed() {
        return randomSeed;
    }

    public void setRandomSeed(String randomSeed) {
        this.randomSeed = randomSeed;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public int getColorDepth() {
        return colorDepth;
    }

    public void setColorDepth(int colorDepth) {
        this.colorDepth = colorDepth;
    }

    public ScreenResolutionInfo getScreenResolution() {
        return screenResolution;
    }

    public void setScreenResolution(ScreenResolutionInfo screenResolution) {
        this.screenResolution = screenResolution;
    }

    public int getTimezoneOffset() {
        return timezoneOffset;
    }

    public void setTimezoneOffset(int timezoneOffset) {
        this.timezoneOffset = timezoneOffset;
    }

    public boolean isHasSessionStorage() {
        return hasSessionStorage;
    }

    public void setHasSessionStorage(boolean hasSessionStorage) {
        this.hasSessionStorage = hasSessionStorage;
    }

    public boolean isHasLocalStorage() {
        return hasLocalStorage;
    }

    public void setHasLocalStorage(boolean hasLocalStorage) {
        this.hasLocalStorage = hasLocalStorage;
    }

    public boolean isHasIndexedDb() {
        return hasIndexedDb;
    }

    public void setHasIndexedDb(boolean hasIndexedDb) {
        this.hasIndexedDb = hasIndexedDb;
    }

    public boolean isHasOpenDatabase() {
        return hasOpenDatabase;
    }

    public void setHasOpenDatabase(boolean hasOpenDatabase) {
        this.hasOpenDatabase = hasOpenDatabase;
    }

    public String getNavigatorCpuClass() {
        return navigatorCpuClass;
    }

    public void setNavigatorCpuClass(String navigatorCpuClass) {
        this.navigatorCpuClass = navigatorCpuClass;
    }

    public String getNavigatorPlatform() {
        return navigatorPlatform;
    }

    public void setNavigatorPlatform(String navigatorPlatform) {
        this.navigatorPlatform = navigatorPlatform;
    }

    public String getDoNotTrack() {
        return doNotTrack;
    }

    public void setDoNotTrack(String doNotTrack) {
        this.doNotTrack = doNotTrack;
    }

    public String getPlugins() {
        return plugins;
    }

    public void setPlugins(String plugins) {
        this.plugins = plugins;
    }

    public boolean isHasAdBlock() {
        return hasAdBlock;
    }

    public void setHasAdBlock(boolean hasAdBlock) {
        this.hasAdBlock = hasAdBlock;
    }

    public boolean isHasLiedLanguages() {
        return hasLiedLanguages;
    }

    public void setHasLiedLanguages(boolean hasLiedLanguages) {
        this.hasLiedLanguages = hasLiedLanguages;
    }

    public boolean isHasLiedResolution() {
        return hasLiedResolution;
    }

    public void setHasLiedResolution(boolean hasLiedResolution) {
        this.hasLiedResolution = hasLiedResolution;
    }

    public boolean isHasLiedOs() {
        return hasLiedOs;
    }

    public void setHasLiedOs(boolean hasLiedOs) {
        this.hasLiedOs = hasLiedOs;
    }

    public String getFonts() {
        return fonts;
    }

    public void setFonts(String fonts) {
        this.fonts = fonts;
    }

    public String getCrcCanvas() {
        return crcCanvas;
    }

    public void setCrcCanvas(String crcCanvas) {
        this.crcCanvas = crcCanvas;
    }

    public String getCrcWebGl() {
        return crcWebGl;
    }

    public void setCrcWebGl(String crcWebGl) {
        this.crcWebGl = crcWebGl;
    }

    public boolean isHasAddBehavior() {
        return hasAddBehavior;
    }

    public void setHasAddBehavior(boolean hasAddBehavior) {
        this.hasAddBehavior = hasAddBehavior;
    }

}
