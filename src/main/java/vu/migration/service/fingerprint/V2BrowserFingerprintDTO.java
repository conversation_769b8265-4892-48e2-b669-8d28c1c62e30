package vu.migration.service.fingerprint;


import vu.migration.service.fingerprint.v2.BrowserFingerprintInfo;
import vu.migration.service.fingerprint.v2.EmptyServerSideInfo;
import vu.migration.service.fingerprint.v2.ServerFingerprintInfo;

public class V2BrowserFingerprintDTO{

    private String fingerprint;

    private BrowserFingerprintInfo fingerprintInfo;

    private SystemInfo systemInfo;

    private ServerFingerprintInfo serverSide;

    private boolean incognito;

    public String getFingerprint() {
        return fingerprint;
    }

    public void setFingerprint(String fingerprint) {
        this.fingerprint = fingerprint;
    }

    public SystemInfo getSystemInfo() {
        return systemInfo;
    }

    public void setSystemInfo(SystemInfo systemInfo) {
        this.systemInfo = systemInfo;
    }

    public BrowserFingerprintInfo getFingerprintInfo() {
        return fingerprintInfo;
    }

    public void setFingerprintInfo(BrowserFingerprintInfo fingerprintInfo) {
        this.fingerprintInfo = fingerprintInfo;
    }

    public ServerFingerprintInfo getServerSide() {

        if(serverSide == null)
            return new EmptyServerSideInfo();

        return serverSide;
    }

    public void setServerSide(ServerFingerprintInfo serverSide) {
        this.serverSide = serverSide;
    }

    public boolean isIncognito() {
        return incognito;
    }

    public void setIncognito(boolean incognito) {
        this.incognito = incognito;
    }

    public V2BrowserFingerprintDTO(){
    }

    public V2BrowserFingerprintDTO(String fingerprint,BrowserFingerprintInfo fingerprintInfo,SystemInfo systemInfo,ServerFingerprintInfo serverSide,boolean incognito){
        this.fingerprint=fingerprint;
        this.fingerprintInfo=fingerprintInfo;
        this.systemInfo=systemInfo;
        this.serverSide=serverSide;
        this.incognito=incognito;
    }
}
