package vu.migration.service.fingerprint.generator;

import jakarta.validation.constraints.NotNull;
import org.springframework.stereotype.Service;
import vu.migration.service.fingerprint.composer.ClientFingerprintComposer;
import vu.migration.service.fingerprint.composer.filters.BrowserFilterFactory;
import vu.migration.service.interfaces.fingerprint.IFingerprintGenerator;
import vu.migration.target.entities.fingerprint.browser.agent.FingerprintAgentValue;
import vu.migration.target.entities.fingerprint.browser.component.IComponentRetriever;

@Service
public class FingerprintGenerator implements IFingerprintGenerator{

    @Override
    public Fingerprint generate(IComponentRetriever componentRetriever){
        ClientFingerprintComposer fingerprintComposer = new ClientFingerprintComposer();
        Fingerprint fingerprintHash = fingerprintComposer.create(componentRetriever);

        return fingerprintHash;
    }

    @Override
    public Fingerprint generate(IComponentRetriever componentRetriever,@NotNull FingerprintAgentValue agent) {

        IComponentRetriever browserSpecific = BrowserFilterFactory.create(agent.getBrowserName(), componentRetriever);

        ClientFingerprintComposer fingerprintComposer = new ClientFingerprintComposer();
        Fingerprint fingerprintHash = fingerprintComposer.create(browserSpecific);

        return fingerprintHash;
    }
}
