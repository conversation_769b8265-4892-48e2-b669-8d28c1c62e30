package vu.migration.service.fingerprint.generator;

import vu.migration.target.entities.fingerprint.browser.component.Component;

import java.util.List;

public class Fingerprint {
    private String fingerprint;
    private List<Component> componentsUsed;

    public String getFingerprint() {
        return fingerprint;
    }

    public void setFingerprint(String fingerprint) {
        this.fingerprint = fingerprint;
    }

    public List<Component> getComponentsUsed() {
        return componentsUsed;
    }

    public void setComponentsUsed(List<Component> componentsUsed) {
        this.componentsUsed = componentsUsed;
    }
}
