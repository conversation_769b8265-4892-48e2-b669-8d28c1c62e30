package vu.migration.service.fingerprint;

import lombok.Synchronized;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import vu.data.models.fingerprint.v2.SourceFingerprintV2UserHashes;
import vu.migration.service.administration.apikey.ApiKeyService;
import vu.migration.service.fingerprint.generator.Fingerprint;
import vu.migration.service.fingerprint.mappers.FingerprintEntitiesMapper;
import vu.migration.service.fingerprint.v2.BrowserFingerprintInfo;
import vu.migration.service.fingerprint.v2.ServerFingerprintInfo;
import vu.migration.service.interfaces.fingerprint.IFingerprintGenerator;
import vu.migration.service.interfaces.fingerprint.IFingerprintService;
import vu.migration.service.interfaces.fingerprint.review.review.IFingerprintServiceLocator;
import vu.migration.source.repository.fingerprint.SourceFingerprintUserHashesRepository;
import vu.migration.source.repository.fingerprint.v2.SourceV2FingerprintIpGeoData;
import vu.migration.source.repository.fingerprint.v2.SourceV2FingerprintUserHashesRepository;
import vu.migration.target.entities.administration.apikey.ApiKey;
import vu.migration.target.entities.fingerprint.*;
import vu.migration.target.entities.fingerprint.browser.BrowserFingerprintType;
import vu.migration.target.entities.fingerprint.browser.FingerprintWebHash;
import vu.migration.target.entities.fingerprint.browser.server.GeoData;
import vu.migration.target.repositories.fingerprint.*;
import vu.migration.util.CheckFingerprintRequest;
import vu.migration.util.FingerprintV1AndDeviceInfo;

import java.time.LocalDateTime;
import java.util.*;

@Service
public class FingerprintService implements IFingerprintService {
    private final IFingerprintGenerator fingerprintGenerator;
    private final IFingerprintServiceLocator serviceLocator;
    private final FingerprintUserHashRepository fingerprintUserHashRepository;
    private final FingerprintCollectionRepository fingerprintCollectionRepository;
    private final FingerprintUserRepository fingerprintUserRepository;
    private final FingerprintResponseRepository repository;
    private final FingerprintUnverifiedValuesRepository fingerprintUnverifiedValuesRepository;
    private final SourceV2FingerprintUserHashesRepository sourceV2FingerprintUserHashesRepository;
    private final SourceV2FingerprintIpGeoData sourceV2FingerprintIpGeoData;
    private final SourceFingerprintUserHashesRepository sourceFingerprintUserHashesRepository;
    private final ApiKeyService apiKeyService;

    @PersistenceContext(unitName = "Target")
    private EntityManager targetEntityManager;

    public FingerprintService(IFingerprintGenerator fingerprintGenerator,IFingerprintServiceLocator serviceLocator,FingerprintUserHashRepository fingerprintUserHashRepository,FingerprintCollectionRepository fingerprintCollectionRepository,FingerprintUserRepository fingerprintUserRepository,FingerprintResponseRepository repository,FingerprintUnverifiedValuesRepository fingerprintUnverifiedValuesRepository,SourceV2FingerprintUserHashesRepository sourceV2FingerprintUserHashesRepository,SourceV2FingerprintIpGeoData sourceV2FingerprintIpGeoData,SourceFingerprintUserHashesRepository sourceFingerprintUserHashesRepository,ApiKeyService apiKeyService){
        this.fingerprintGenerator=fingerprintGenerator;
        this.serviceLocator=serviceLocator;
        this.fingerprintUserHashRepository=fingerprintUserHashRepository;
        this.fingerprintCollectionRepository=fingerprintCollectionRepository;
        this.fingerprintUserRepository=fingerprintUserRepository;
        this.repository=repository;
        this.fingerprintUnverifiedValuesRepository=fingerprintUnverifiedValuesRepository;
        this.sourceV2FingerprintUserHashesRepository=sourceV2FingerprintUserHashesRepository;
        this.sourceV2FingerprintIpGeoData=sourceV2FingerprintIpGeoData;
        this.sourceFingerprintUserHashesRepository=sourceFingerprintUserHashesRepository;
        this.apiKeyService=apiKeyService;
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<SourceFingerprintV2UserHashes> fingerprintV2UserHashesFindAll(Pageable page,Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate){
        if (fromDate.isPresent() && toDate.isPresent()) {
            return sourceV2FingerprintUserHashesRepository.findByCreationDateBetween(fromDate.get(), toDate.get(), page);
        } else {
            return sourceV2FingerprintUserHashesRepository.findAll(page);
        }
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<FingerprintV1AndDeviceInfo> fingerprintUserHashesFindAll(Pageable page, Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate){
        if (fromDate.isPresent() && toDate.isPresent()) {
            return sourceFingerprintUserHashesRepository.findByCreationDateBetween(fromDate.get(), toDate.get(), page);
        } else{
            return sourceFingerprintUserHashesRepository.findAllFingerprintUserHashes(page);
        }
    }

    @Override
    public void copyFingerprintV2(List<SourceFingerprintV2UserHashes> userFingerprintV2){
        for(SourceFingerprintV2UserHashes fingerprint:userFingerprintV2){

            ApiKey apiKey = apiKeyService.findByApiKeyId(fingerprint.getApiKeyId());

            CheckFingerprintRequest fingerprintRequest = new CheckFingerprintRequest();
            fingerprintRequest.setApiKey(apiKey);
            fingerprintRequest.setUserid(fingerprint.getUserId());

            ParametersNecessary parametersNecessary = new ParametersNecessary(fingerprint.getCreationDate(),fingerprint.getLastCheckDate(),fingerprint.getAccuracyPercent());
            fingerprintRequest.setParametersNecessary(parametersNecessary);

            V2BrowserFingerprintDTO fingerprintBrowser = mapBrowserV2Fingerprint(fingerprint);
            FingerprintWebHash  hash = createWebHashFingerprint(fingerprintBrowser);
            generateFingerprint(hash, fingerprint.getCombinedHash(), fingerprint.getSourceFingerprintClientHash().getHash(), fingerprint.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getHash(), parametersNecessary);
            fingerprintRequest.setFingerprintWebHash(hash);

            checkFingerprint("v2",fingerprintRequest);

        }
    }

    @Override
    public void copyFingerprint(List<FingerprintV1AndDeviceInfo> userFingerprint){
        for(FingerprintV1AndDeviceInfo fingerprint:userFingerprint){

            ApiKey apiKey = apiKeyService.findByApiKeyId(fingerprint.getApiKeyId());

            CheckFingerprintRequest fingerprintRequest = new CheckFingerprintRequest();
            fingerprintRequest.setApiKey(apiKey);
            fingerprintRequest.setUserid(fingerprint.getUserId());

            ParametersNecessary parametersNecessary=new ParametersNecessary(fingerprint.getCreationDate(),fingerprint.getLastCheckDate(),fingerprint.getAccuracy());
            fingerprintRequest.setParametersNecessary(parametersNecessary);

            String fingerprintType = "device";

            if (!Objects.nonNull(fingerprint.getFingerprintInfoId())){

                vu.migration.target.entities.fingerprint.FingerprintDeviceValue hash = fingerprint.getDeviceValue();
                hash.setFpHash(fingerprint.getHash());
                hash.setCreationDate(parametersNecessary.getCreationDate());
                fingerprintRequest.setFingerprintDeviceHash(hash);

            } else{

                FingerprintWebHash hash = fingerprint.getFingerprintWebHash();

                fingerprintRequest.setFingerprintWebHash(hash);
                fingerprintType = "v1";

            }

            String finalFingerprintType=fingerprintType;
            checkFingerprint(finalFingerprintType,fingerprintRequest);

        }
    }

    private @NotNull FingerprintWebHash createWebHashFingerprint(@NotNull V2BrowserFingerprintDTO fingerprint) {

        FingerprintWebHash hash = new FingerprintWebHash(BrowserFingerprintType.BFPv2);

        hash.setFpHash(fingerprint.getFingerprint());
        hash.setIncognito(fingerprint.isIncognito());
        hash.setFingerprintAgentValues(FingerprintEntitiesMapper.mapSystemInfo(fingerprint.getSystemInfo()));
        hash.setFingerprintValues(FingerprintEntitiesMapper.mapBrowserFingerprintInfo(fingerprint.getFingerprintInfo()));
        hash.setFingerprintServer(FingerprintEntitiesMapper.mapServerSide(fingerprint.getServerSide()));

        return hash;
    }

    private void generateFingerprint(FingerprintWebHash hash,String combinedFingerprint,String primaryHash,String secondaryHash,@NotNull ParametersNecessary parametersNecessary) {
        Fingerprint primaryFingerprint = fingerprintGenerator.generate(hash, hash.getFingerprintAgentValues());
        Fingerprint secondaryFingerprint = fingerprintGenerator.generate(hash.getSecondaryComponents());
        hash.getFingerprintServer().setHash(secondaryFingerprint.getFingerprint());
        hash.addComponentsUsed(primaryFingerprint.getComponentsUsed());
        hash.addComponentsUsedInSecondary(secondaryFingerprint.getComponentsUsed());

        hash.setSecondaryHash(secondaryHash);
        hash.setPrimaryHash(primaryHash);
        hash.setFpHash(combinedFingerprint);
        hash.setCreationDate(parametersNecessary.getCreationDate());
    }

    private void checkFingerprint(String version, CheckFingerprintRequest fingerprint) {

        CheckFingerprint checkFingerprint = new CheckFingerprint();

        switch (version) {

            case "v2":
                checkFingerprint.setFingerprintType(FingerprintType.BrowserV2);
                checkFingerprint.setHashFingerprint(fingerprint.getFingerprintWebHash());
                break;

            case "v1":
                checkFingerprint.setFingerprintType(FingerprintType.BrowserV1);
                checkFingerprint.setHashFingerprint(fingerprint.getFingerprintWebHash());
                break;
            case "device":
                checkFingerprint.setFingerprintType(FingerprintType.Device);
                checkFingerprint.setHashFingerprint(fingerprint.getFingerprintDeviceHash());
                checkFingerprint.getHashFingerprint().setAccuracy(fingerprint.getParametersNecessary().getAccuracy());
                break;
            default:
                break;
        }

        checkFingerprint.setUserId(fingerprint.getUserid());
        checkFingerprint.setApiKey(fingerprint.getApiKey());

        saveCheckFingerprint(checkFingerprint, fingerprint.getParametersNecessary());
    }

    @Transactional(transactionManager = "transactionTargetManager")
    public void saveCheckFingerprint(@NotNull CheckFingerprint checkFingerprint, @NotNull ParametersNecessary parametersNecessary) {
        FingerprintUser user = getOrCreateUser(checkFingerprint.getUserId());
        List<FingerprintCollection> fingerprintCollections = fingerprintCollectionRepository.findAllByUserId(user.getId());

        if (fingerprintCollections.isEmpty()) {
            handleNewFingerprint(checkFingerprint, parametersNecessary, user);
        } else {
            handleExistingFingerprint(checkFingerprint, parametersNecessary, user, fingerprintCollections);
        }
    }

    @Synchronized
    private FingerprintUser getOrCreateUser(String userId) {
        FingerprintUser user = fingerprintUserRepository.findByUserId(userId);
        if (user == null) {
            user = new FingerprintUser(userId);
            user = fingerprintUserRepository.saveAndFlush(user);
        }
        return user;
    }

    private void handleNewFingerprint(CheckFingerprint checkFingerprint, ParametersNecessary parametersNecessary, FingerprintUser user) {
        double accuracy = parametersNecessary.getAccuracy();
        FingerprintHash.Status fingerprintStatus = (accuracy == 100) ? FingerprintHash.Status.VERIFIED : FingerprintHash.Status.UNVERIFIED;

        checkFingerprint.getHashFingerprint().setAccuracy(accuracy);
        FingerprintCollection fingerprintCollection = createFingerprintCollection(checkFingerprint, parametersNecessary, user);

        FingerprintUserHash userHashFingerprint = addFingerprintToCollection(checkFingerprint, fingerprintStatus, user, fingerprintCollection);

        FingerprintResult result = createResultResponse(HttpStatus.CREATED,
                "User created with fingerprint " + fingerprintStatus.name().toLowerCase(),
                true, accuracy, new ArrayList<>(), fingerprintCollection.getCollectionUUID());

        finalizeFingerprintProcess(checkFingerprint, parametersNecessary, userHashFingerprint, result);
    }

    private FingerprintCollection createFingerprintCollection(CheckFingerprint checkFingerprint, ParametersNecessary parametersNecessary, FingerprintUser user) {
        FingerprintCollection fingerprintCollection = FingerprintCollection.create(user);
        fingerprintCollection.setCreationDate(parametersNecessary.getCreationDate());
        fingerprintCollection.setLastCheckDate(parametersNecessary.getLastCheckDate());
        fingerprintCollection.setType(checkFingerprint.getFingerprintType());
        fingerprintCollectionRepository.save(fingerprintCollection);
        return fingerprintCollection;
    }

    private FingerprintUserHash addFingerprintToCollection(CheckFingerprint checkFingerprint, FingerprintHash.Status fingerprintStatus, FingerprintUser user, FingerprintCollection fingerprintCollection) {
        FingerprintUserHash userHashFingerprint;
        if (checkFingerprint.getHashFingerprint().getFingerprintHashes().size() == 1) {
            userHashFingerprint = fingerprintCollection.addFingerprintToCollection(
                    checkFingerprint.getHashFingerprint().getFingerprintHashes().get(0),
                    fingerprintStatus,
                    user.getId());
        } else {
            userHashFingerprint = fingerprintCollection.addFingerprintToCollection(
                    checkFingerprint.getHashFingerprint(),
                    fingerprintStatus,
                    user.getId());
        }
        return userHashFingerprint;
    }

    private void handleExistingFingerprint(CheckFingerprint checkFingerprint, ParametersNecessary parametersNecessary, FingerprintUser user, List<FingerprintCollection> fingerprintCollections) {
        FingerprintUserHash userHashFingerprint = findMatchingFingerprint(checkFingerprint, fingerprintCollections);

        if (userHashFingerprint != null) {
            updateExistingFingerprint(checkFingerprint, userHashFingerprint);
        } else {
            userHashFingerprint = groupFingerprintIntoCollection(checkFingerprint, user, parametersNecessary);
        }

        updateFingerprintStatus(checkFingerprint, userHashFingerprint);

        FingerprintResult result = createResultResponse(HttpStatus.OK,
                "Unknown Fingerprint for the current user, needs validation.",
                false, checkFingerprint.getHashFingerprint().getAccuracy(),
                new ArrayList<>(),
                userHashFingerprint.getFingerprintCollectionFromCollectionId().getCollectionUUID());

        finalizeFingerprintProcess(checkFingerprint, parametersNecessary, userHashFingerprint, result);
    }

    private FingerprintUserHash findMatchingFingerprint(CheckFingerprint checkFingerprint, List<FingerprintCollection> fingerprintCollections) {
        for (FingerprintCollection fingerprintCollection : fingerprintCollections) {
            if (fingerprintCollection.getType() == checkFingerprint.getFingerprintType()) {
                for (FingerprintUserHash fingerprintUserHash : fingerprintCollection.getUserFingerprintList()) {
                    if (fingerprintUserHash.getFingerprintHash().getFpHash().compareTo(checkFingerprint.getHashFingerprint().getFpHash()) == 0) {
                        return fingerprintUserHash;
                    }
                }
            }
        }
        return null;
    }

    private void updateExistingFingerprint(CheckFingerprint checkFingerprint, FingerprintUserHash userHashFingerprint) {
        userHashFingerprint.incrementUsedCount();
        userHashFingerprint.setLastCheckDate(checkFingerprint.getHashFingerprint().getLastCheckDate());

        if (userHashFingerprint.getStatus() == FingerprintHash.Status.VERIFIED) {
            createResultResponse(HttpStatus.OK,
                    "Known Fingerprint for the current user.",
                    true, userHashFingerprint.getAccuracyPercent(),
                    new ArrayList<>(),
                    userHashFingerprint.getFingerprintCollectionFromCollectionId().getCollectionUUID());
        } else {
            userHashFingerprint.setAccuracyPercent(checkFingerprint.getHashFingerprint().getAccuracy());
        }
    }

    private void updateFingerprintStatus(CheckFingerprint checkFingerprint, FingerprintUserHash userHashFingerprint) {
        if (checkFingerprint.getHashFingerprint().getAccuracy() == 100) {
            userHashFingerprint.setStatus(FingerprintHash.Status.VERIFIED);
            createResultResponse(HttpStatus.ACCEPTED,
                    "Fingerprint verified",
                    false,
                    userHashFingerprint.getAccuracyPercent(),
                    new ArrayList<>(),
                    userHashFingerprint.getFingerprintCollectionFromCollectionId().getCollectionUUID());
        }
    }

    private void finalizeFingerprintProcess(CheckFingerprint checkFingerprint, ParametersNecessary parametersNecessary, FingerprintUserHash userHashFingerprint, FingerprintResult result) {
        userHashFingerprint.setApiKeyId(checkFingerprint.getApiKey().getId());
        userHashFingerprint.setCreationDate(parametersNecessary.getCreationDate());
        userHashFingerprint.setLastCheckDate(parametersNecessary.getLastCheckDate());
        userHashFingerprint = fingerprintUserHashRepository.saveAndFlush(userHashFingerprint);

        if (userHashFingerprint.getStatus() == FingerprintHash.Status.UNVERIFIED) {
            createFingerprintTokenIfIsEnable(userHashFingerprint);
        }

        checkFingerprint.setHashFingerprint(userHashFingerprint.getFingerprintHash());
    saveFingerprintResultLog(checkFingerprint, result, userHashFingerprint.getId());
    // Flush/clear per logical write to control 1st-level cache growth in bulk runs
    targetEntityManager.flush();
    targetEntityManager.clear();
    }

    private void saveFingerprintResultLog(@NotNull CheckFingerprint checkFingerprint,@NotNull FingerprintResult result,long userHashId) {
        FingerprintResponse fingerprintResponse = new FingerprintResponse();

        String userId = checkFingerprint.getUserId();

        fingerprintResponse.setAccuracyPercent(result.getAccuracyPercent());
        fingerprintResponse.setUserId(userId);
        fingerprintResponse.setHashKnown(result.isExistsHash());
        fingerprintResponse.setResultCode(result.getCode());
        fingerprintResponse.setCreationDate(new Date());

        fingerprintResponse.setUserHashId(userHashId);

        repository.save(fingerprintResponse);
    }

    FingerprintUserHash groupFingerprintIntoCollection(CheckFingerprint checkFingerprint,@NotNull FingerprintUser fingerprintUser,ParametersNecessary parametersNecessary) {

        // Case #2 f no está en F y existe un fingerprint f1 con el mismo "deviceID" o "randomSeed" que está en F
        for (FingerprintCollection collection : fingerprintUser.getUserFingerprintCollection()) {
            if (checkFingerprint.getFingerprintType() == collection.getType()) {
                for (FingerprintUserHash hash : collection.getUserFingerprintList()) {

                    if (serviceLocator.getService(checkFingerprint.getFingerprintType()).verifyFingerprintCoincidence(hash, checkFingerprint.getHashFingerprint())) {

                        FingerprintUserHash fingerprintUserHash = collection.addFingerprintToCollection(checkFingerprint.getHashFingerprint(), FingerprintHash.Status.UNVERIFIED, fingerprintUser.getId());
                        fingerprintUserHash.setAccuracyPercent(parametersNecessary.getAccuracy());
                        return fingerprintUserHash;

                    }
                }
            }
        }

        if (parametersNecessary.getAccuracy() != 0) {
            double threshold = 85.0;

            // Case #3 f no está en F y existe un único dispositivo d1 tal que la comparación entre f y algún elemento f1 de d1 da un puntaje mayor o igual al umbral de comparación
            // Case #4 f no está en F y existen dispositivos d1, d2 ... dn, tales que la comparación entre f y algún elemento fi de di da un puntaje mayor o igual al umbral de comparación
            UUID collectionId = null;

            double accuracyToCompare = parametersNecessary.getAccuracy();

            for (FingerprintCollection collection : fingerprintUser.getUserFingerprintCollection()) {
                if (checkFingerprint.getFingerprintType() == collection.getType()) {

                    double accuracyMaxPerCollection = 0.0;

                    for (FingerprintUserHash hash : collection.getUserFingerprintList()) {
                        double accuracyFromHash = hash.getAccuracyPercent();
                        if (parametersNecessary.getAccuracy() > accuracyMaxPerCollection) {
                            accuracyMaxPerCollection = accuracyFromHash;
                        }
                    }

                    if (accuracyToCompare > threshold && accuracyToCompare >= accuracyMaxPerCollection) {
                        collectionId = collection.getCollectionUUID();
                    }
                }
            }

            if (collectionId != null) {
                FingerprintCollection fingerprintCollection = fingerprintUser.getCollectionByUUID(collectionId);
                FingerprintUserHash fingerprintUserHash = fingerprintCollection.addFingerprintToCollection(checkFingerprint.getHashFingerprint(), FingerprintHash.Status.UNVERIFIED, fingerprintUser.getId());
                fingerprintUserHash.setAccuracyPercent(parametersNecessary.getAccuracy());
                return fingerprintUserHash;
            }
        }
        // Case #5: f no está en F y no existen dispositivos tales que al comparar f con algún f1 miembro del dispositivo se supere el umbral de comparación
        FingerprintCollection fingerprintCollection = FingerprintCollection.create(fingerprintUser);
        fingerprintCollection.setType(checkFingerprint.getFingerprintType());
        fingerprintCollection.setCreationDate(parametersNecessary.getCreationDate());
        fingerprintCollection.setLastCheckDate(parametersNecessary.getLastCheckDate());
        fingerprintCollection = this.fingerprintCollectionRepository.save(fingerprintCollection);

        FingerprintUserHash fingerprintUserHash = fingerprintCollection.addFingerprintToCollection(checkFingerprint.getHashFingerprint(), FingerprintHash.Status.UNVERIFIED, fingerprintUser.getId());
        fingerprintUserHash.setAccuracyPercent(parametersNecessary.getAccuracy());
        fingerprintUserHash.setFingerprintCollectionFromCollectionId(fingerprintCollection);
        fingerprintUserHash = this.fingerprintUserHashRepository.save(fingerprintUserHash);

        return fingerprintUserHash;
    }

    FingerprintResult createResultResponse(@NotNull HttpStatus status,String description,boolean existHash,double accuracyPercent,List<String> triggeredRules,UUID collectionId) {
        FingerprintResult result = new FingerprintResult();

        result.setCode(status.value());
        result.setDescription(description);
        result.setExistsHash(existHash);
        result.setAccuracyPercent(accuracyPercent);
        result.setTriggeredRules(triggeredRules);
        result.setCollection(collectionId);

        return result;
    }

    private @NotNull V2BrowserFingerprintDTO mapBrowserV2Fingerprint(@NotNull SourceFingerprintV2UserHashes fingerprintV2UserHashes){

        GeoData geoData = sourceV2FingerprintIpGeoData.getGeoDataByUser(fingerprintV2UserHashes.getUserId());

        ServerFingerprintInfo.UserData userData = new ServerFingerprintInfo.UserData(
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getTimeStampUtc(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getHttp2Fingerprint(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getHttp2FingerprintHash(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getTlsFingerprint(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getTlsFingerprintHash(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getTlsFingerprintSslGreased(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getHeaders(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getHeaderSignature(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getEtag(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getEtagFirstRequest(),
                geoData,
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getIp(),
                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintServerValues().getCookie()
        );

        BrowserFingerprintInfo browserFingerprintInfo = new BrowserFingerprintInfo();
        browserFingerprintInfo.setLocalstorageID(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getLocalStorageId());
        browserFingerprintInfo.setLanguage(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getLanguage());
        browserFingerprintInfo.setTimezone(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getTimezone());
        browserFingerprintInfo.setVendor(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getVendor());
        browserFingerprintInfo.setVendorFlavors(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getVendorFlavors());
        browserFingerprintInfo.setHasSessionStorage(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isHasSessionStorage());
        browserFingerprintInfo.setHasLocalStorage(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isHasLocalStorage());
        browserFingerprintInfo.setHasIndexedDb(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isHasIndexedDb());
        browserFingerprintInfo.setHasOpenDatabase(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isHasOpenDatabase());
        browserFingerprintInfo.setCookiesEnabled(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isCookiesEnabled());
        browserFingerprintInfo.setNavigatorPlatform(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getNavigatorPlatform());
        browserFingerprintInfo.setExtendedFonts(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getExtendedFonts());
        browserFingerprintInfo.setFonts(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getFonts());
        browserFingerprintInfo.setFontsPreferences(
                new BrowserFingerprintInfo.FontPreferences(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().get_default(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getApple(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getSerif(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getSans(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getMono(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getMin(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getSystem()
                )
        );
        browserFingerprintInfo.setAudio(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAudio());
        browserFingerprintInfo.setDomBlocker(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getDomBlocker());
        browserFingerprintInfo.setMaths(
                new BrowserFingerprintInfo.Maths(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAcos(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAcosh(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAcoshPf(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAsin(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAsinh(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAsinhPf(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAtanh(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAtanhPf(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getAtan(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getSin(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getSinh(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getSinhPf(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getCos(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getCosh(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getCoshPf(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getTan(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getTanh(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getTanhPf(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getExp(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getExpm1(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getExpm1Pf(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getLog1p(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getLog1pPf(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getPowPI()
                )
        );
        browserFingerprintInfo.setTouchSupport(
                new BrowserFingerprintInfo.TouchSupport(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getMaxTouchPoints(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isTouchEvent(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isTouchStart()
                )
        );
        browserFingerprintInfo.setHardware(
                new BrowserFingerprintInfo.Hardware(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getLogicalProcessors(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getMemory(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getGpu()
                )
        );
        browserFingerprintInfo.setScreen(
                new BrowserFingerprintInfo.Screen(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getColorDepth(),
                        new BrowserFingerprintInfo.Resolution(
                                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getScreenHeight(),
                                fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getScreenWidth()
                        ),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getFrameSize()
                )
        );
        browserFingerprintInfo.setCrcCanvas(
                new BrowserFingerprintInfo.CrcCanvas(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getGeometry(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getText(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getWebgl()
                )
        );
        browserFingerprintInfo.setMedia(
                new BrowserFingerprintInfo.Media(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getColorGamut(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getInvertedColors(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isForcedColors(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getMonochrome(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().getContrast(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isHdr(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintClientValues().isReducedMotion()
                )
        );

        SystemInfo systemInfo = new SystemInfo();
        systemInfo.setUa(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getUa());
        systemInfo.setBrowser(
                new SystemInfo.Browser(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getBrowserName(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getBrowserVersion(),
                        Integer.parseInt(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getBrowserMajor())
                )
        );
        systemInfo.setEngine(
                new SystemInfo.UserAgentElement(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getEngineName(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getEngineVersion()
                )
        );
        systemInfo.setOs(
                new SystemInfo.UserAgentElement(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getOsName(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getOsVersion()
                )
        );
        systemInfo.setDevice(
                new SystemInfo.Device(
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getDeviceType(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getDeviceModel(),
                        fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getDeviceVendor()
                )
        );
        systemInfo.setCpu(
                new SystemInfo.Cpu(fingerprintV2UserHashes.getSourceFingerprintClientHash().getSourceFingerprintV2AgentValue().getCpuArchitecture())
        );

        ServerFingerprintInfo serverFingerprintInfo = new ServerFingerprintInfo();
        serverFingerprintInfo.setUserData(userData);

        V2BrowserFingerprintDTO fingerprintDTO = new V2BrowserFingerprintDTO();
        fingerprintDTO.setFingerprint(fingerprintV2UserHashes.getCombinedHash());
        fingerprintDTO.setFingerprintInfo(browserFingerprintInfo);
        fingerprintDTO.setServerSide(serverFingerprintInfo);
        fingerprintDTO.setSystemInfo(systemInfo);
        fingerprintDTO.setIncognito(fingerprintV2UserHashes.isIncognito());
        return fingerprintDTO;
    }

    private void createFingerprintTokenIfIsEnable(FingerprintUserHash fingerprintUserHash) {

            FingerprintUnverifiedValue fingerprintUnverifiedValue = new FingerprintUnverifiedValue();

            fingerprintUnverifiedValue.setFingerprintUserHash(fingerprintUserHash);
            fingerprintUnverifiedValue.setToken(UUID.randomUUID());
            fingerprintUnverifiedValue.setCreationDate(new Date());
            fingerprintUnverifiedValue.setFingerprintUserHash(fingerprintUserHash);

            this.fingerprintUnverifiedValuesRepository.save(fingerprintUnverifiedValue);
    }

}
