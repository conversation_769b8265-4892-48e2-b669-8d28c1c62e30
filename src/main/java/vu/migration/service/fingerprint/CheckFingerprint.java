package vu.migration.service.fingerprint;

import vu.migration.target.entities.administration.apikey.ApiKey;
import vu.migration.target.entities.fingerprint.FingerprintHash;
import vu.migration.target.entities.fingerprint.FingerprintType;

import java.util.Date;

public class CheckFingerprint {
    private String userId;
    private FingerprintHash fingerprintHash;
    private FingerprintType fingerprintType;
    private ApiKey apiKey;

    public ApiKey getApiKey() {
        return apiKey;
    }

    public void setApiKey(Api<PERSON>ey apiKey) {
        this.apiKey = apiKey;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public FingerprintHash getHashFingerprint() {
        return this.fingerprintHash;
    }

    public void setHashFingerprint(FingerprintHash fingerprintHash) {

        this.fingerprintHash = fingerprintHash;
        this.fingerprintHash.setChannelId(FingerprintServiceValues.BOTH_CHANNEL_FINGERPRINT);
        this.fingerprintHash.setEventDate(new Date());

    }

    public FingerprintType getFingerprintType() {
        return fingerprintType;
    }

    public void setFingerprintType(FingerprintType fingerprintType) {
        this.fingerprintType = fingerprintType;

        if(fingerprintHash != null) {
            if (fingerprintType == FingerprintType.BrowserV2)
                this.fingerprintHash.setOperationTypeId(FingerprintServiceValues.BROWSER_FINGERPRINT_V2_ID_OPERATION);
            else
                this.fingerprintHash.setOperationTypeId(FingerprintServiceValues.DEVICE_ID_OPERATION_FINGERPRINT);
        }
    }
}
