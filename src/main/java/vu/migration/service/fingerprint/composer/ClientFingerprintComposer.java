package vu.migration.service.fingerprint.composer;

import org.apache.commons.codec.digest.MurmurHash3;
import vu.migration.service.fingerprint.generator.Fingerprint;
import vu.migration.target.entities.fingerprint.browser.component.IComponentRetriever;

import java.util.Arrays;
import java.util.stream.Collectors;

public class ClientFingerprintComposer implements IFingerprintComposer {

    @Override
    public Fingerprint create(IComponentRetriever componentRetriever) {

        Fingerprint fingerprint = new Fingerprint();
        fingerprint.setComponentsUsed(componentRetriever.getComponents());

        byte[] componentBytes = fingerprint.getComponentsUsed()
                .stream()
                .map(component -> component.getValue())
                .collect(Collectors.joining(";"))
                .getBytes();

        long[] hashResult = MurmurHash3.hash128x64(componentBytes);

        String hash = Arrays.stream(hashResult)
                .mapToObj(t -> Long.toHexString(t))
                .collect(Collectors.joining());

        fingerprint.setFingerprint(hash);

        return fingerprint;
    }
}
