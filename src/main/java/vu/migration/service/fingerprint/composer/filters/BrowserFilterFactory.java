package vu.migration.service.fingerprint.composer.filters;

import vu.migration.target.entities.fingerprint.browser.component.IComponentRetriever;

public class BrowserFilterFactory {

    public static IComponentRetriever create(String browserName, IComponentRetriever componentRetriever){

        IComponentRetriever browserSpecific;

        switch (browserName){
            case "Edge":
            case "Microsoft Edge":
                browserSpecific = new EdgeComponentFilter(componentRetriever);
                break;
            case "Chrome":
            case "Google Chrome":
                browserSpecific = new ChromeComponentFilter(componentRetriever);
                break;
            case "Firefox":
                browserSpecific = new FirefoxComponentFilter(componentRetriever);
                break;
            case "Opera":
                browserSpecific = new OperaComponentFilter(componentRetriever);
                break;
            case "Safari":
                browserSpecific = new SafariComponentFilter(componentRetriever);
                break;
            default:
                browserSpecific = new GenericComponentFilter(componentRetriever);
        }

        return browserSpecific;
    }
}
