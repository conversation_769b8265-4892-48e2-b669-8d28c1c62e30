package vu.migration.service.fingerprint.composer.filters;

import vu.migration.target.entities.fingerprint.browser.component.FingerprintComponent;
import vu.migration.target.entities.fingerprint.browser.component.IComponentRetriever;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class FirefoxComponentFilter extends ComponentFilter {

    public FirefoxComponentFilter(IComponentRetriever allComponents) {
        super(allComponents);
    }

    @Override
    protected List<FingerprintComponent> getFilter() {
        return Stream.of(
                FingerprintComponent.AUDIO,
                FingerprintComponent.COOKIE_ENABLED,
                FingerprintComponent.CRC_CANVAS_GEOMETRY,
                FingerprintComponent.CRC_CANVAS_WEBGL,
                FingerprintComponent.DOM_BLOCKER,
                FingerprintComponent.EXTENDED_FONTS,
                FingerprintComponent.FONTS,
                FingerprintComponent.FONTS_PREFERENCES,
                FingerprintComponent.HARDWARE_GPU,
                FingerprintComponent.HARDWARE_PROCESSORS,
                FingerprintComponent.HARDWARE_MEMORY,
                FingerprintComponent.INDEXED_DATABASE,
                FingerprintComponent.LOCAL_STORAGE,
                FingerprintComponent.OPEN_DATABASE,
                FingerprintComponent.SESSION_STORAGE,
                FingerprintComponent.LANGUAGE,
                FingerprintComponent.MATHS,
                FingerprintComponent.MEDIA_COLOR_GAMUT,
                FingerprintComponent.MEDIA_CONTRAST,
                FingerprintComponent.MEDIA_FORCED_COLORS,
                FingerprintComponent.MEDIA_HDR,
                FingerprintComponent.MEDIA_INVERTED_COLORS,
                FingerprintComponent.MEDIA_REDUCED_MOTION,
                FingerprintComponent.MEDIA_MONOCHROME,
                FingerprintComponent.PLATFORM,
                FingerprintComponent.SCREEN_COLOR_DEPTH,
                FingerprintComponent.VENDOR,
                FingerprintComponent.VENDOR_FLAVOR,
                FingerprintComponent.TIMEZONE,
                FingerprintComponent.TOUCH_SUPPORT,

                FingerprintComponent.HTTP2,
                FingerprintComponent.HEADER,
                FingerprintComponent.TLS_SSL_GREASED,

                FingerprintComponent.AGENT_BROWSER_NAME,
                FingerprintComponent.AGENT_BROWSER_MAJOR,
                FingerprintComponent.AGENT_BROWSER_VERSION,
                FingerprintComponent.AGENT_CPU_ARCHITECTURE,
                FingerprintComponent.AGENT_DEVICE_MODEL,
                FingerprintComponent.AGENT_DEVICE_TYPE,
                FingerprintComponent.AGENT_DEVICE_VENDOR,
                FingerprintComponent.AGENT_ENGINE_NAME,
                FingerprintComponent.AGENT_ENGINE_VERSION,
                FingerprintComponent.AGENT_OS_NAME,
                FingerprintComponent.AGENT_OS_VERSION,
                FingerprintComponent.AGENT_UA
        ).collect(Collectors.toList());

    }
}