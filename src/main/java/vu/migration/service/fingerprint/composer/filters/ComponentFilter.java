package vu.migration.service.fingerprint.composer.filters;

import vu.migration.target.entities.fingerprint.browser.component.Component;
import vu.migration.target.entities.fingerprint.browser.component.FingerprintComponent;
import vu.migration.target.entities.fingerprint.browser.component.IComponentRetriever;

import java.util.List;
import java.util.stream.Collectors;

public abstract class ComponentFilter implements IComponentRetriever {
    IComponentRetriever allComponents;
    List<FingerprintComponent> componentNamesToFilter;

    public ComponentFilter(IComponentRetriever allComponents) {
        this.allComponents = allComponents;
    }

    @Override
    public List<Component> getComponents() {

        componentNamesToFilter = getFilter();

        return allComponents.getComponents()
                .stream()
                .filter(t -> componentNamesToFilter.contains(t.getName()))
                .collect(Collectors.toList());
    }

    protected abstract List<FingerprintComponent> getFilter();
}
