package vu.migration.service.fingerprint;

import lombok.Data;
import vu.migration.target.entities.fingerprint.FingerprintType;

@Data
public class FingerprintUpdatedEvent {
    private String userId;
    private FingerprintType fingerprintType;

    public FingerprintUpdatedEvent(String userid, FingerprintType fingerprintType) {
        this.userId = userid;
        this.fingerprintType = fingerprintType;
    }
}
