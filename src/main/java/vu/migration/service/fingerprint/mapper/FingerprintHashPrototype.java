package vu.migration.service.fingerprint.mapper;

import vu.migration.target.entities.fingerprint.FingerprintHash;
import vu.migration.target.entities.fingerprint.FingerprintUserHash;

public class FingerprintHashPrototype {

    public static FingerprintHash cloneFrom(FingerprintUserHash fingerprintUserHash){
            FingerprintHash fingerprintHash = (FingerprintHash) fingerprintUserHash.getFingerprintHash().clone();
            fingerprintHash.setAccuracy(fingerprintUserHash.getAccuracyPercent());
            fingerprintHash.setStatus(fingerprintUserHash.getStatus());
            fingerprintHash.setLastCheckDate(fingerprintUserHash.getLastCheckDate());
            fingerprintHash.setUserId(fingerprintUserHash.getFingerprintUser().getUserId());
            return fingerprintHash;
    }
}
