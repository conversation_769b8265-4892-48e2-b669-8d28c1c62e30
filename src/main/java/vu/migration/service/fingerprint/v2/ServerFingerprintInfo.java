package vu.migration.service.fingerprint.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import vu.migration.target.entities.fingerprint.browser.server.GeoData;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ServerFingerprintInfo{

    private UserData userData;
    private String signature;

    public UserData getUserData() {
        return userData;
    }

    public void setUserData(UserData userData) {
        this.userData = userData;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserData {
        private String timeStampUtc;
        private String http2Fingerprint;
        private String http2FingerprintHash;
        private String tlsFingerprint;
        private String tlsFingerprintHash;
        private String tlsFingerprintSslGreased;

        private String headers;
        private String headerSignature;
        private String etag;
        private String etagFirstRequest;
        private GeoData geoData;
        private String ip;
        String cookie;

        public UserData(){
        }

        public UserData(String timeStampUtc,String http2Fingerprint,String http2FingerprintHash,String tlsFingerprint,String tlsFingerprintHash,String tlsFingerprintSslGreased,String headers,String headerSignature,String etag,String etagFirstRequest,GeoData geoData,String ip,String cookie){
            this.timeStampUtc=timeStampUtc;
            this.http2Fingerprint=http2Fingerprint;
            this.http2FingerprintHash=http2FingerprintHash;
            this.tlsFingerprint=tlsFingerprint;
            this.tlsFingerprintHash=tlsFingerprintHash;
            this.tlsFingerprintSslGreased=tlsFingerprintSslGreased;
            this.headers=headers;
            this.headerSignature=headerSignature;
            this.etag=etag;
            this.etagFirstRequest=etagFirstRequest;
            this.geoData=geoData;
            this.ip=ip;
            this.cookie=cookie;
        }

        public String getTimeStampUtc() {
            return timeStampUtc;
        }

        public void setTimeStampUtc(String timeStampUtc) {
            this.timeStampUtc = timeStampUtc;
        }

        public String getHttp2Fingerprint() {
            return http2Fingerprint;
        }

        public void setHttp2Fingerprint(String http2Fingerprint) {
            this.http2Fingerprint = http2Fingerprint;
        }

        public String getHttp2FingerprintHash() {
            return http2FingerprintHash;
        }

        public void setHttp2FingerprintHash(String http2FingerprintHash) {
            this.http2FingerprintHash = http2FingerprintHash;
        }

        public String getTlsFingerprint() {
            return tlsFingerprint;
        }

        public void setTlsFingerprint(String tlsFingerprint) {
            this.tlsFingerprint = tlsFingerprint;
        }

        public String getTlsFingerprintHash() {
            return tlsFingerprintHash;
        }

        public void setTlsFingerprintHash(String tlsFingerprintHash) {
            this.tlsFingerprintHash = tlsFingerprintHash;
        }

        public String getTlsFingerprintSslGreased() {
            return tlsFingerprintSslGreased;
        }

        public void setTlsFingerprintSslGreased(String tlsFingerprintSslGreased) {
            this.tlsFingerprintSslGreased = tlsFingerprintSslGreased;
        }

        public String getHeaders() {
            return headers;
        }

        public void setHeaders(String headers) {
            this.headers = headers;
        }

        public String getHeaderSignature() {
            return headerSignature;
        }

        public void setHeaderSignature(String headerSignature) {
            this.headerSignature = headerSignature;
        }

        public String getEtag() {
            return etag;
        }

        public void setEtag(String etag) {
            this.etag = etag;
        }

        public String getEtagFirstRequest() {
            return etagFirstRequest;
        }

        public void setEtagFirstRequest(String etagFirstRequest) {
            this.etagFirstRequest = etagFirstRequest;
        }

        public GeoData getGeoData() {
            return geoData;
        }

        public void setGeoData(GeoData geoData) {
            this.geoData = geoData;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public String getCookie() {
            return cookie;
        }

        public void setCookie(String cookie) {
            this.cookie = cookie;
        }
    }

}
