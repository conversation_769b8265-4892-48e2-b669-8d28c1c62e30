package vu.migration.service.fingerprint.v2;

public class SystemInfo{

    private String ua;
    private Browser browser;
    private UserAgentElement engine;
    private UserAgentElement os;

    private Device device;
    private Cpu cpu;

    public String getUa() {
        return ua;
    }

    public void setUa(String ua) {
        this.ua = ua;
    }

    public Browser getBrowser() {
        return browser;
    }

    public void setBrowser(Browser browser) {
        this.browser = browser;
    }

    public UserAgentElement getEngine() {
        return engine;
    }

    public void setEngine(UserAgentElement engine) {
        this.engine = engine;
    }

    public UserAgentElement getOs() {
        return os;
    }

    public void setOs(UserAgentElement os) {
        this.os = os;
    }

    public Cpu getCpu() {
        return cpu;
    }

    public void setCpu(Cpu cpu) {
        this.cpu = cpu;
    }

    public Device getDevice() {
        return device;
    }

    public void setDevice(Device device) {
        this.device = device;
    }

    public static class Browser{
        private String name;
        private String version;
        private int major;

        public Browser(String name,String version,int major){
            this.name=name;
            this.version=version;
            this.major=major;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public int getMajor() {
            return major;
        }

        public void setMajor(int major) {
            this.major = major;
        }
    }

    public static class UserAgentElement {
        private String name;
        private String version;

        public UserAgentElement(String name,String version){
            this.name=name;
            this.version=version;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }

    public static class Cpu {
        private String architecture;

        public Cpu(String architecture){
            this.architecture=architecture;
        }

        public String getArchitecture() {
            return architecture;
        }

        public void setArchitecture(String architecture) {
            this.architecture = architecture;
        }
    }

    public static class Device {
        private String type;
        private String model;
        private String vendor;

        public Device(String type,String model,String vendor){
            this.type=type;
            this.model=model;
            this.vendor=vendor;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getModel() {
            return model;
        }

        public void setModel(String model) {
            this.model = model;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }
    }

}
