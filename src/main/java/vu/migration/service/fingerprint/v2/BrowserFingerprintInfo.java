package vu.migration.service.fingerprint.v2;

public class BrowserFingerprintInfo{

    private String localstorageID;
    private String language;
    private String timezone;
    private String vendor;
    private String vendorFlavors;
    private boolean hasSessionStorage;
    private boolean hasLocalStorage;
    private boolean hasIndexedDb;
    private boolean hasOpenDatabase;
    private boolean cookiesEnabled;
    private String navigatorPlatform;
    private String extendedFonts;
    private String fonts;
    private FontPreferences fontsPreferences;
    private double audio;
    private String domBlocker;
    private Maths maths;
    private TouchSupport touchSupport;
    private Hardware hardware;
    private Screen screen;
    private CrcCanvas crcCanvas;
    private Media media;

    public String getLocalstorageID() {
        return localstorageID;
    }

    public void setLocalstorageID(String randomSeed) {
        this.localstorageID = randomSeed;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getVendorFlavors() {
        return vendorFlavors;
    }

    public void setVendorFlavors(String vendorFlavors) {
        this.vendorFlavors = vendorFlavors;
    }

    public boolean isHasSessionStorage() {
        return hasSessionStorage;
    }

    public void setHasSessionStorage(boolean hasSessionStorage) {
        this.hasSessionStorage = hasSessionStorage;
    }

    public boolean isHasLocalStorage() {
        return hasLocalStorage;
    }

    public void setHasLocalStorage(boolean hasLocalStorage) {
        this.hasLocalStorage = hasLocalStorage;
    }

    public boolean isHasIndexedDb() {
        return hasIndexedDb;
    }

    public void setHasIndexedDb(boolean hasIndexedDb) {
        this.hasIndexedDb = hasIndexedDb;
    }

    public boolean isHasOpenDatabase() {
        return hasOpenDatabase;
    }

    public void setHasOpenDatabase(boolean hasOpenDatabase) {
        this.hasOpenDatabase = hasOpenDatabase;
    }

    public boolean isCookiesEnabled() {
        return cookiesEnabled;
    }

    public void setCookiesEnabled(boolean cookiesEnabled) {
        this.cookiesEnabled = cookiesEnabled;
    }

    public String getNavigatorPlatform() {
        return navigatorPlatform;
    }

    public void setNavigatorPlatform(String navigatorPlatform) {
        this.navigatorPlatform = navigatorPlatform;
    }

    public String getExtendedFonts() {
        return extendedFonts;
    }

    public void setExtendedFonts(String extendedFonts) {
        this.extendedFonts = extendedFonts;
    }

    public String getFonts() {
        return fonts;
    }

    public void setFonts(String fonts) {
        this.fonts = fonts;
    }

    public FontPreferences getFontsPreferences() {
        return fontsPreferences;
    }

    public void setFontsPreferences(FontPreferences fontPreferences) {
        this.fontsPreferences = fontPreferences;
    }

    public double getAudio() {
        return audio;
    }

    public void setAudio(double audio) {
        this.audio = audio;
    }

    public String getDomBlocker() {
        return domBlocker;
    }

    public void setDomBlocker(String domBlocker) {
        this.domBlocker = domBlocker;
    }

    public Maths getMaths() {
        return maths;
    }

    public void setMaths(Maths maths) {
        this.maths = maths;
    }

    public TouchSupport getTouchSupport() {
        return touchSupport;
    }

    public void setTouchSupport(TouchSupport touchSupport) {
        this.touchSupport = touchSupport;
    }

    public Hardware getHardware() {
        return hardware;
    }

    public void setHardware(Hardware hardware) {
        this.hardware = hardware;
    }

    public Screen getScreen() {
        return screen;
    }

    public void setScreen(Screen screen) {
        this.screen = screen;
    }

    public CrcCanvas getCrcCanvas() {
        return crcCanvas;
    }

    public void setCrcCanvas(CrcCanvas crcCanvas) {
        this.crcCanvas = crcCanvas;
    }

    public Media getMedia() {
        return media;
    }

    public void setMedia(Media media) {
        this.media = media;
    }

    public static class FontPreferences {
        private double _default;
        private double apple;
        private double serif;
        private double sans;
        private double mono;
        private double min;
        private double system;

        public FontPreferences(double _default,double apple,double serif,double sans,double mono,double min,double system){
            this._default=_default;
            this.apple=apple;
            this.serif=serif;
            this.sans=sans;
            this.mono=mono;
            this.min=min;
            this.system=system;
        }

        public double getDefault() {
            return _default;
        }

        public void setDefault(double _default) {
            this._default = _default;
        }

        public double getApple() {
            return apple;
        }

        public void setApple(double apple) {
            this.apple = apple;
        }

        public double getSerif() {
            return serif;
        }

        public void setSerif(double serif) {
            this.serif = serif;
        }

        public double getSans() {
            return sans;
        }

        public void setSans(double sans) {
            this.sans = sans;
        }

        public double getMono() {
            return mono;
        }

        public void setMono(double mono) {
            this.mono = mono;
        }

        public double getMin() {
            return min;
        }

        public void setMin(double min) {
            this.min = min;
        }

        public double getSystem() {
            return system;
        }

        public void setSystem(double system) {
            this.system = system;
        }
    }

    public static class Maths {
        private double acos;
        private double acosh;
        private double acoshPf;
        private double asin;
        private double asinh;
        private double asinhPf;
        private double atanh;
        private double atanhPf;
        private double atan;
        private double sin;
        private double sinh;
        private double sinhPf;
        private double cos;
        private double cosh;
        private double coshPf;
        private double tan;
        private double tanh;
        private double tanhPf;
        private double exp;
        private double expm1;
        private double expm1Pf;
        private double log1p;
        private double log1pPf;
        private double powPI;

        public Maths(double acos,double acosh,double acoshPf,double asin,double asinh,double asinhPf,double atanh,double atanhPf,double atan,double sin,double sinh,double sinhPf,double cos,double cosh,double coshPf,double tan,double tanh,double tanhPf,double exp,double expm1,double expm1Pf,double log1p,double log1pPf,double powPI){
            this.acos=acos;
            this.acosh=acosh;
            this.acoshPf=acoshPf;
            this.asin=asin;
            this.asinh=asinh;
            this.asinhPf=asinhPf;
            this.atanh=atanh;
            this.atanhPf=atanhPf;
            this.atan=atan;
            this.sin=sin;
            this.sinh=sinh;
            this.sinhPf=sinhPf;
            this.cos=cos;
            this.cosh=cosh;
            this.coshPf=coshPf;
            this.tan=tan;
            this.tanh=tanh;
            this.tanhPf=tanhPf;
            this.exp=exp;
            this.expm1=expm1;
            this.expm1Pf=expm1Pf;
            this.log1p=log1p;
            this.log1pPf=log1pPf;
            this.powPI=powPI;
        }

        public double getAcos() {
            return acos;
        }

        public void setAcos(double acos) {
            this.acos = acos;
        }

        public double getAcosh() {
            return acosh;
        }

        public void setAcosh(double acosh) {
            this.acosh = acosh;
        }

        public double getAcoshPf() {
            return acoshPf;
        }

        public void setAcoshPf(double acoshPf) {
            this.acoshPf = acoshPf;
        }

        public double getAsin() {
            return asin;
        }

        public void setAsin(double asin) {
            this.asin = asin;
        }

        public double getAsinh() {
            return asinh;
        }

        public void setAsinh(double asinh) {
            this.asinh = asinh;
        }

        public double getAsinhPf() {
            return asinhPf;
        }

        public void setAsinhPf(double asinhPf) {
            this.asinhPf = asinhPf;
        }

        public double getAtanh() {
            return atanh;
        }

        public void setAtanh(double atanh) {
            this.atanh = atanh;
        }

        public double getAtanhPf() {
            return atanhPf;
        }

        public void setAtanhPf(double atanhPf) {
            this.atanhPf = atanhPf;
        }

        public double getAtan() {
            return atan;
        }

        public void setAtan(double atan) {
            this.atan = atan;
        }

        public double getSin() {
            return sin;
        }

        public void setSin(double sin) {
            this.sin = sin;
        }

        public double getSinh() {
            return sinh;
        }

        public void setSinh(double sinh) {
            this.sinh = sinh;
        }

        public double getSinhPf() {
            return sinhPf;
        }

        public void setSinhPf(double sinhPf) {
            this.sinhPf = sinhPf;
        }

        public double getCos() {
            return cos;
        }

        public void setCos(double cos) {
            this.cos = cos;
        }

        public double getCosh() {
            return cosh;
        }

        public void setCosh(double cosh) {
            this.cosh = cosh;
        }

        public double getCoshPf() {
            return coshPf;
        }

        public void setCoshPf(double coshPf) {
            this.coshPf = coshPf;
        }

        public double getTan() {
            return tan;
        }

        public void setTan(double tan) {
            this.tan = tan;
        }

        public double getTanh() {
            return tanh;
        }

        public void setTanh(double tanh) {
            this.tanh = tanh;
        }

        public double getTanhPf() {
            return tanhPf;
        }

        public void setTanhPf(double tanhPf) {
            this.tanhPf = tanhPf;
        }

        public double getExp() {
            return exp;
        }

        public void setExp(double exp) {
            this.exp = exp;
        }

        public double getExpm1() {
            return expm1;
        }

        public void setExpm1(double expm1) {
            this.expm1 = expm1;
        }

        public double getExpm1Pf() {
            return expm1Pf;
        }

        public void setExpm1Pf(double expm1Pf) {
            this.expm1Pf = expm1Pf;
        }

        public double getLog1p() {
            return log1p;
        }

        public void setLog1p(double log1p) {
            this.log1p = log1p;
        }

        public double getLog1pPf() {
            return log1pPf;
        }

        public void setLog1pPf(double log1pPf) {
            this.log1pPf = log1pPf;
        }

        public double getPowPI() {
            return powPI;
        }

        public void setPowPI(double powPI) {
            this.powPI = powPI;
        }
    }

    public static class TouchSupport {
        private int maxTouchPoints;
        private boolean touchEvent;
        private boolean touchStart;

        public TouchSupport(int maxTouchPoints,boolean touchEvent,boolean touchStart){
            this.maxTouchPoints=maxTouchPoints;
            this.touchEvent=touchEvent;
            this.touchStart=touchStart;
        }

        public int getMaxTouchPoints() {
            return maxTouchPoints;
        }

        public void setMaxTouchPoints(int maxTouchPoints) {
            this.maxTouchPoints = maxTouchPoints;
        }

        public boolean isTouchEvent() {
            return touchEvent;
        }

        public void setTouchEvent(boolean touchEvent) {
            this.touchEvent = touchEvent;
        }

        public boolean isTouchStart() {
            return touchStart;
        }

        public void setTouchStart(boolean touchStart) {
            this.touchStart = touchStart;
        }
    }

    public static class Hardware {
        private int logicalProcessors;
        private double memory;
        private String gpu;

        public Hardware(int logicalProcessors,double memory,String gpu){
            this.logicalProcessors=logicalProcessors;
            this.memory=memory;
            this.gpu=gpu;
        }

        public int getLogicalProcessors() {
            return logicalProcessors;
        }

        public void setLogicalProcessors(int logicalProcessors) {
            this.logicalProcessors = logicalProcessors;
        }

        public double getMemory() {
            return memory;
        }

        public void setMemory(double memory) {
            this.memory = memory;
        }

        public String getGpu() {
            return gpu;
        }

        public void setGpu(String gpu) {
            this.gpu = gpu;
        }
    }

    public static class Screen {
        private int colorDepth;
        private Resolution resolution;
        private String frameSize;

        public Screen(int colorDepth,Resolution resolution,String frameSize){
            this.colorDepth=colorDepth;
            this.resolution=resolution;
            this.frameSize=frameSize;
        }

        public int getColorDepth() {
            return colorDepth;
        }

        public void setColorDepth(int colorDepth) {
            this.colorDepth = colorDepth;
        }

        public Resolution getResolution() {
            return resolution;
        }

        public void setResolution(Resolution resolution) {
            this.resolution = resolution;
        }

        public String getFrameSize() {
            return frameSize;
        }

        public void setFrameSize(String frameSize) {
            this.frameSize = frameSize;
        }
    }

    public static  class Resolution {
        private int screenWidth;
        private int screenHeight;

        public Resolution(int screenWidth,int screenHeight){
            this.screenWidth=screenWidth;
            this.screenHeight=screenHeight;
        }

        public int getScreenWidth() {
            return screenWidth;
        }

        public void setScreenWidth(int screenWidth) {
            this.screenWidth = screenWidth;
        }

        public int getScreenHeight() {
            return screenHeight;
        }

        public void setScreenHeight(int screenHeight) {
            this.screenHeight = screenHeight;
        }
    }

    public static class CrcCanvas {
        private String geometry;
        private String text;
        private String webgl;

        public CrcCanvas(String geometry,String text,String webgl){
            this.geometry=geometry;
            this.text=text;
            this.webgl=webgl;
        }

        public String getGeometry() {
            return geometry;
        }

        public void setGeometry(String geometry) {
            this.geometry = geometry;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getWebgl() {
            return webgl;
        }

        public void setWebgl(String webgl) {
            this.webgl = webgl;
        }
    }

    public static class Media {
        private String colorGamut;
        private String invertedColors;
        private boolean forcedColors;
        private int monochrome;
        private int contrast;
        private boolean hdr;
        private boolean reducedMotion;

        public Media(String colorGamut,String invertedColors,boolean forcedColors,int monochrome,int contrast,boolean hdr,boolean reducedMotion){
            this.colorGamut=colorGamut;
            this.invertedColors=invertedColors;
            this.forcedColors=forcedColors;
            this.monochrome=monochrome;
            this.contrast=contrast;
            this.hdr=hdr;
            this.reducedMotion=reducedMotion;
        }

        public String getColorGamut() {
            return colorGamut;
        }

        public void setColorGamut(String colorGamut) {
            this.colorGamut = colorGamut;
        }

        public String getInvertedColors() {
            return invertedColors;
        }

        public void setInvertedColors(String invertedColors) {
            this.invertedColors = invertedColors;
        }

        public boolean isForcedColors() {
            return forcedColors;
        }

        public void setForcedColors(boolean forcedColors) {
            this.forcedColors = forcedColors;
        }

        public int getMonochrome() {
            return monochrome;
        }

        public void setMonochrome(int monochrome) {
            this.monochrome = monochrome;
        }

        public int getContrast() {
            return contrast;
        }

        public void setContrast(int contrast) {
            this.contrast = contrast;
        }

        public boolean isHdr() {
            return hdr;
        }

        public void setHdr(boolean hdr) {
            this.hdr = hdr;
        }

        public boolean isReducedMotion() {
            return reducedMotion;
        }

        public void setReducedMotion(boolean reducedMotion) {
            this.reducedMotion = reducedMotion;
        }
    }

    public BrowserFingerprintInfo(){
    }

    public BrowserFingerprintInfo(String localstorageID,String language,String timezone,String vendor,String vendorFlavors,boolean hasSessionStorage,boolean hasLocalStorage,boolean hasIndexedDb,boolean hasOpenDatabase,boolean cookiesEnabled,String navigatorPlatform,String extendedFonts,String fonts,FontPreferences fontsPreferences,double audio,String domBlocker,Maths maths,TouchSupport touchSupport,Hardware hardware,Screen screen,CrcCanvas crcCanvas,Media media){
        this.localstorageID=localstorageID;
        this.language=language;
        this.timezone=timezone;
        this.vendor=vendor;
        this.vendorFlavors=vendorFlavors;
        this.hasSessionStorage=hasSessionStorage;
        this.hasLocalStorage=hasLocalStorage;
        this.hasIndexedDb=hasIndexedDb;
        this.hasOpenDatabase=hasOpenDatabase;
        this.cookiesEnabled=cookiesEnabled;
        this.navigatorPlatform=navigatorPlatform;
        this.extendedFonts=extendedFonts;
        this.fonts=fonts;
        this.fontsPreferences=fontsPreferences;
        this.audio=audio;
        this.domBlocker=domBlocker;
        this.maths=maths;
        this.touchSupport=touchSupport;
        this.hardware=hardware;
        this.screen=screen;
        this.crcCanvas=crcCanvas;
        this.media=media;
    }
}
