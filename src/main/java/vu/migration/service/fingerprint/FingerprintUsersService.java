package vu.migration.service.fingerprint;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vu.migration.service.interfaces.fingerprint.IFingerprintUsersService;
import vu.migration.target.entities.fingerprint.FingerprintCollection;
import vu.migration.target.entities.fingerprint.FingerprintUser;
import vu.migration.target.repositories.fingerprint.FingerprintUserRepository;

import java.util.ArrayList;
import java.util.List;

@Service
public class FingerprintUsersService implements IFingerprintUsersService{

    private final FingerprintUserRepository repository;

    @Autowired
    public FingerprintUsersService(FingerprintUserRepository repository) {
        this.repository = repository;
    }

    @Override
    public FingerprintUser getFingerprintUser(String userId) {
        return this.repository.findByUserId(userId);

    }

    @Override
    public List<FingerprintCollection> getUserFingerprintCollections(String userId) {
        return new ArrayList<>();
    }

    @Override
    public FingerprintUser saveNewFingerprintUser(FingerprintUser user) {
        return this.repository.saveAndFlush(user);
    }
}
