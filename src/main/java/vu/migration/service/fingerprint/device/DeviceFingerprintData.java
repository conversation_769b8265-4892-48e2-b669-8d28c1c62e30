package vu.migration.service.fingerprint.device;

public class DeviceFingerprintData{
    //Common
    private String deviceName;

    private String isJailbroken;
    private String osName;

    //Android
    private String osVersion;
    private int apiLevel;

    //Iphone
    private String deviceIdentifier;

    //Parámetros retrocompatibles con SDK antiguos.

    private String fingerprintCompatible;
    private String rooted;
    private String gpsCompatible;
    private String deviceId;
    private String deviceManufacturer;
    private String deviceResolution;

    public DeviceFingerprintData() {
    }

    public void setResolution(String resolution) {
        this.deviceResolution = resolution;
    }

    public void setManufacturer(String manufacturer) {
        this.deviceManufacturer = manufacturer;
    }

    public void setDeviceID(String deviceID) {
        this.deviceId = deviceID;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceManufacturer() {
        return deviceManufacturer;
    }

    public void setDeviceManufacturer(String deviceManufacturer) {
        this.deviceManufacturer = deviceManufacturer;
    }

    public String getDeviceResolution() {
        return deviceResolution;
    }

    public void setDeviceResolution(String deviceResolution) {
        this.deviceResolution = deviceResolution;
    }

    public boolean isRooted() {
        return parseBooleanAttribute(this.rooted);
    }

    public void setRooted(String isRooted) {
        this.rooted = isRooted;
    }

    public void setIsDeviceRooted(String isDeviceRooted) {
        this.rooted = isDeviceRooted;
    }

    public Boolean isFingerprintCompatible() {
        return parseBooleanAttribute(this.fingerprintCompatible);
    }

    boolean parseBooleanAttribute(String attribute) {
        if (attribute != null) {
            switch (attribute) {
                case "YES":
                    return true;
                case "NO":
                    return false;
                case "true":
                    return true;
                case "false":
                    return false;
                case "1":
                    return true;
                case "0":
                    return false;
                default:
                    return false;
            }
        } else {
            return false;
        }
    }

    public void setFingerprintCompatible(String isFingerprintCompatible) {
        this.fingerprintCompatible = isFingerprintCompatible;
    }

    public void setIsFingerprintCompatible(String isFingerprintCompatible) {
        this.fingerprintCompatible = isFingerprintCompatible;
    }

    public boolean isGpsCompatible() {
        return parseBooleanAttribute(this.gpsCompatible);
    }

    public void setGpsCompatible(String isGPSCompatible) {
        this.gpsCompatible = isGPSCompatible;
    }

    public void setIsGPSCompatible(String isGPSCompatible) {
        this.gpsCompatible = isGPSCompatible;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getOsName() {
        return osName;
    }

    public void setOsName(String osName) {
        this.osName = osName;
    }

    public int getApiLevel() {
        return apiLevel;
    }

    public void setApiLevel(int apiLevel) {
        this.apiLevel = apiLevel;
    }

    public String getDeviceIdentifier() {
        return deviceIdentifier;
    }

    public void setDeviceIdentifier(String deviceIdentifier) {
        this.deviceIdentifier = deviceIdentifier;
    }

    public boolean isJailbroken() {
        return parseBooleanAttribute(this.isJailbroken);
    }

    public void setIsJailbroken(String isJailbroken) {
        this.isJailbroken = isJailbroken;
    }

    public void setIsDeviceJailbroken(String isDeviceJailbroken) {
        this.isJailbroken = isDeviceJailbroken;
    }

    public boolean isRootedOrJailbroken() {
        return parseBooleanAttribute(isJailbroken) || parseBooleanAttribute(rooted);
    }
}
