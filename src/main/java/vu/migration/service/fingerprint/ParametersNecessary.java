package vu.migration.service.fingerprint;

import lombok.Data;

import java.util.Date;

@Data
public class ParametersNecessary{

    private Date creationDate;

    private Date lastCheckDate;

    private double accuracy;

    public ParametersNecessary(Date creationDate,Date lastCheckDate,double accuracy){
        this.creationDate=creationDate;
        this.lastCheckDate=lastCheckDate;
        this.accuracy=accuracy;
    }
}
