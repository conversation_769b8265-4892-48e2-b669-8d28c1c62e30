package vu.migration.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import vu.data.models.fraud.event.SourceAdditionalParametersEvent;
import vu.data.models.fraud.event.SourceEvents;
import vu.data.models.fraud.event.SourceRuleMatchedEvent;
import vu.migration.mappers.AdditionalParametersEventMapper;
import vu.migration.mappers.EventMapper;
import vu.migration.mappers.RuleMatchedEventMapper;
import vu.migration.service.fraud.event.IEventService;
import vu.migration.service.fraud.event.IRuleMatchedEventService;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.target.entities.fraud.event.AdditionalParametersEvent;
import vu.migration.target.entities.fraud.event.Events;
import vu.migration.target.entities.fraud.event.RuleMatchedEvent;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Service
public class EventManagementService{
    private static final Logger logger = LoggerFactory.getLogger(EventManagementService.class);
    private final IEventService iEventService;
    private final IRuleMatchedEventService iRuleMatchedEventService;
    private final EventMapper eventMapper;
    private final AdditionalParametersEventMapper additionalParametersEventMapper;
    private final RuleMatchedEventMapper ruleMatchedEventMapper;

    public EventManagementService(IEventService iEventService,IRuleMatchedEventService iRuleMatchedEventService,EventMapper eventMapper,AdditionalParametersEventMapper additionalParametersEventMapper,RuleMatchedEventMapper ruleMatchedEventMapper){
        this.iEventService=iEventService;
        this.iRuleMatchedEventService=iRuleMatchedEventService;
        this.eventMapper=eventMapper;
        this.additionalParametersEventMapper=additionalParametersEventMapper;
        this.ruleMatchedEventMapper=ruleMatchedEventMapper;
    }

    @Value("${app.migration.chunk-size:10000}")
    private int chunkSize;

    @Value("${app.migration.parallelism:4}")
    private int parallelism;

    public void copyEvent(Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate,
                           Optional<Long> fromId,Optional<Long> toId){
        logger.info("Event - Copy - Begin;");

        if (fromDate.isPresent() && toDate.isPresent()){
            Optional<IdRange> idRangeOpt = iEventService.findMinAndMaxIdByDateRange(fromDate.get(), toDate.get());

            if (idRangeOpt.isPresent()) {
                IdRange idRange = idRangeOpt.get();
                fromId = Optional.ofNullable(idRange.getMinId());
                toId = Optional.ofNullable(idRange.getMaxId());
            }
        }else if (fromId.isEmpty() &&toId.isEmpty()){
            Optional<IdRange> idRangeOpt = iEventService.findMinAndMaxId();

            if (idRangeOpt.isPresent()) {
                IdRange idRange = idRangeOpt.get();
                fromId = Optional.ofNullable(idRange.getMinId());
                toId = Optional.ofNullable(idRange.getMaxId());
            }
        }

    ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
    List<Future<?>> futures = new ArrayList<>();

        int page = 0;
        Page<SourceEvents> eventsPage;

        do {
            eventsPage = iEventService.findEventsWithFilters(fromId, toId, page, chunkSize);
            List<SourceEvents> eventsToCopy = eventsPage.getContent();

            Future<?> f = executor.submit(() -> {
                try {
                    List<Events> event = eventMapper.toTargetEventList(eventsToCopy);
                    iEventService.copyEvent(event);
                } catch (Exception e) {
                    logger.error("Error copying event chunk", e);
                    throw e;
                }
            });
            futures.add(f);

            page++;
        } while (eventsPage.hasNext());

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                logger.error("Error in parallel execution", e);
            }
        }

        executor.shutdown();

        copyEventAdditionalParameter(fromId,toId);
        copyRuleMatchedEvent(fromId,toId);

        logger.info("Event - Copy - End;");
    }

    private void copyEventAdditionalParameter(Optional<Long> fromId,Optional<Long> toId){
        logger.info("EventAdditionalParameter - Copy - Begin;");

    ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
    List<Future<?>> futures = new ArrayList<>();

        int page = 0;
        Page<SourceAdditionalParametersEvent> additionalParametersEventPage;

        do {
            additionalParametersEventPage = iEventService.findAdditionalParametersEventWithFilters(fromId, toId, page, chunkSize);
            List<SourceAdditionalParametersEvent> additionalParametersEventToCopy = additionalParametersEventPage.getContent();

            Future<?> f = executor.submit(() -> {
                try {
                    List<AdditionalParametersEvent> additionalParametersTransaction = additionalParametersEventMapper.toTargetAdditionalParametersEventList(additionalParametersEventToCopy);
                    iEventService.copyAdditionalParametersEvent(additionalParametersTransaction);
                } catch (Exception e) {
                    logger.error("Error copying additional parameters event chunk", e);
                    throw e;
                }
            });
            futures.add(f);

            page++;
        } while (additionalParametersEventPage.hasNext());

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                logger.error("Error in parallel execution", e);
            }
        }

        executor.shutdown();

        logger.info("EventAdditionalParameter - Copy - End;");
    }

    private void copyRuleMatchedEvent(Optional<Long> fromId,Optional<Long> toId){
        logger.info("RuleMatchedEvent - Copy - Begin;");

    ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
    List<Future<?>> futures = new ArrayList<>();

        int page = 0;
        Page<SourceRuleMatchedEvent> ruleMatchedEventPage;

        do {
            ruleMatchedEventPage = iRuleMatchedEventService.findRuleMatchedEventWithFilters(fromId, toId, page, chunkSize);
            List<SourceRuleMatchedEvent> sourceRuleMatchedEventToCopy = ruleMatchedEventPage.getContent();

            Future<?> f = executor.submit(() -> {
                try {
                    List<RuleMatchedEvent> ruleMatchedEvent = ruleMatchedEventMapper.toTargetRuleMatchedEventList(sourceRuleMatchedEventToCopy);
                    iRuleMatchedEventService.copyRuleMatchedEvent(ruleMatchedEvent);
                } catch (Exception e) {
                    logger.error("Error copying rule matched event chunk", e);
                    throw e;
                }
            });
            futures.add(f);

            page++;
        } while (ruleMatchedEventPage.hasNext());

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                logger.error("Error in parallel execution", e);
            }
        }

        executor.shutdown();

        logger.info("RuleMatchedEvent - Copy - End;");
    }

}
