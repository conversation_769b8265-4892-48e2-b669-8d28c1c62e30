package vu.migration.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import vu.data.models.fraud.transaction.SourceTransactionEntity;
import vu.data.models.report.profile.SourceProfileByOperation;
import vu.data.models.report.profile.SourceProfilePeriodInfoByCreditAccount;
import vu.data.models.report.profile.SourceProfilePeriodInformation;
import vu.migration.mappers.ProfileByOperationMapper;
import vu.migration.mappers.ProfilePeriodInfoByCreditAccountMapper;
import vu.migration.mappers.ProfilePeriodInformationMapper;
import vu.migration.service.report.profile.IServiceProfile;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.target.entities.fraud.transaction.TransactionEntity;
import vu.migration.target.entities.profile.ProfileByOperation;
import vu.migration.target.entities.profile.ProfilePeriodInfoByCreditAccount;
import vu.migration.target.entities.profile.ProfilePeriodInformation;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Service
public class ProfileManagementService{
    private static final Logger logger = LoggerFactory.getLogger(ProfileManagementService.class);

    private final IServiceProfile iServiceProfile;
    private final ProfilePeriodInformationMapper profilePeriodInformationMapper;
    private final ProfilePeriodInfoByCreditAccountMapper profilePeriodInfoByCreditAccountMapper;
    private final ProfileByOperationMapper profileByOperationMapper;

    public ProfileManagementService(IServiceProfile iServiceProfile,ProfilePeriodInformationMapper profilePeriodInformationMapper,ProfilePeriodInfoByCreditAccountMapper profilePeriodInfoByCreditAccountMapper,ProfileByOperationMapper profileByOperationMapper){
        this.iServiceProfile=iServiceProfile;
        this.profilePeriodInformationMapper=profilePeriodInformationMapper;
        this.profilePeriodInfoByCreditAccountMapper=profilePeriodInfoByCreditAccountMapper;
        this.profileByOperationMapper=profileByOperationMapper;
    }

    @Value("${app.migration.chunk-size:10000}")
    private int chunkSize;

    @Value("${app.migration.parallelism:4}")
    private int parallelism;

    public void copyProfile(Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate){
        logger.info("Profile - Copy - Begin;");

        copyProfilePeriodInformation(fromDate, toDate);

        logger.info("Profile - Copy - End;");
    }

    private void copyProfilePeriodInformation(Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate){
        logger.info("ProfilePeriodInformation - Copy - Begin;");

        Long fromId = 1L;
        Long toId = 1L;

        if (fromDate.isPresent() && toDate.isPresent()){
            Optional<IdRange> idRangeOpt = iServiceProfile.findMinAndMaxIdByDateRange(fromDate.get(), toDate.get());

            if (idRangeOpt.isPresent()) {
                IdRange idRange = idRangeOpt.get();
                fromId = idRange.getMinId();
                toId = idRange.getMaxId();
            }
        }else {
            Optional<IdRange> idRangeOpt = iServiceProfile.findMinAndMaxId();

            if (idRangeOpt.isPresent()) {
                IdRange idRange = idRangeOpt.get();
                fromId = idRange.getMinId();
                toId = idRange.getMaxId();
            }
        }

    ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
    List<Future<?>> futures = new ArrayList<>();

        int page = 0;
        Page<SourceProfilePeriodInformation> profilePeriodInformationPage;

        do {
            profilePeriodInformationPage = iServiceProfile.findProfilePeriodInformationWithFilters(Optional.of(fromId), Optional.of(toId), page, chunkSize);
            List<SourceProfilePeriodInformation> profilePeriodInformationToCopy = profilePeriodInformationPage.getContent();

            Future<?> f = executor.submit(() -> {
                try {
                    List<ProfilePeriodInformation> profilePeriodInformation = profilePeriodInformationMapper.toTargetProfilePeriodInformationList(profilePeriodInformationToCopy);
                    iServiceProfile.copyProfilePeriodInformation(profilePeriodInformation);
                } catch (Exception e) {
                    logger.error("Error copying profile period information chunk", e);
                    throw e;
                }
            });
            futures.add(f);

            page++;
        } while (profilePeriodInformationPage.hasNext());

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                logger.error("Error in parallel execution", e);
            }
        }

        executor.shutdown();

        copyProfilePeriodInfoByCreditAccount(fromId, toId);
        copyProfileByOperation(fromId, toId);

        logger.info("ProfilePeriodInformation - Copy - End;");
    }

    private void copyProfilePeriodInfoByCreditAccount(long fromId,long toId){
        logger.info("ProfilePeriodInfoByCreditAccount - Copy - Begin;");

    ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
    List<Future<?>> futures = new ArrayList<>();

        int page = 0;
        Page<SourceProfilePeriodInfoByCreditAccount> profilePeriodInfoByCreditAccountPage;

        do {
            profilePeriodInfoByCreditAccountPage = iServiceProfile.findProfilePeriodInfoByCreditAccountWithFilters(Optional.of(fromId), Optional.of(toId), page, chunkSize);
            List<SourceProfilePeriodInfoByCreditAccount> profilePeriodInfoByCreditAccountToCopy = profilePeriodInfoByCreditAccountPage.getContent();

            Future<?> f = executor.submit(() -> {
                try {
                    List<ProfilePeriodInfoByCreditAccount> profilePeriodInfoByCreditAccount = profilePeriodInfoByCreditAccountMapper.toTargetProfilePeriodInfoByCreditAccountList(profilePeriodInfoByCreditAccountToCopy);
                    iServiceProfile.copyProfilePeriodInfoByCreditAccount(profilePeriodInfoByCreditAccount);
                } catch (Exception e) {
                    logger.error("Error copying profile period info by credit account chunk", e);
                    throw e;
                }
            });
            futures.add(f);

            page++;
        } while (profilePeriodInfoByCreditAccountPage.hasNext());

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                logger.error("Error in parallel execution", e);
            }
        }

        executor.shutdown();

        logger.info("ProfilePeriodInfoByCreditAccount - Copy - End;");
    }

    private void copyProfileByOperation(long fromId,long toId){
        logger.info("ProfileByOperation - Copy - Begin;");

    ExecutorService executor = Executors.newFixedThreadPool(Math.max(Runtime.getRuntime().availableProcessors(), parallelism));
    List<Future<?>> futures = new ArrayList<>();

        int page = 0;
        Page<SourceProfileByOperation> profileByOperationPage;

        do {
            profileByOperationPage = iServiceProfile.findProfileByOperationWithFilters(Optional.of(fromId), Optional.of(toId), page, chunkSize);
            List<SourceProfileByOperation> transactionsToCopy = profileByOperationPage.getContent();

            Future<?> f = executor.submit(() -> {
                try {
                    List<ProfileByOperation> profileByOperation = profileByOperationMapper.toTargetProfileByOperationList(transactionsToCopy);
                    iServiceProfile.copyProfileByOperation(profileByOperation);
                } catch (Exception e) {
                    logger.error("Error copying profile by operation chunk", e);
                    throw e;
                }
            });
            futures.add(f);

            page++;
        } while (profileByOperationPage.hasNext());

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                logger.error("Error in parallel execution", e);
            }
        }

        executor.shutdown();

        logger.info("ProfileByOperation - Copy - End;");
    }
}
