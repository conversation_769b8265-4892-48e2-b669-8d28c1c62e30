package vu.migration.service.audit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vu.migration.source.repository.Audit.SourceAuditLogRepository;
import vu.migration.target.entities.audit.AuditLog;
import vu.migration.util.ICopySourceInTarget;

@Service
public class AuditLogService implements IAuditLogService{
    private static final Logger logger = LoggerFactory.getLogger(AuditLogService.class);
    private final SourceAuditLogRepository sourceAuditLogRepository;

    private final ICopySourceInTarget iCopySourceInTarget;

    public AuditLogService(SourceAuditLogRepository sourceAuditLogRepository,ICopySourceInTarget iCopySourceInTarget){
        this.sourceAuditLogRepository=sourceAuditLogRepository;
        this.iCopySourceInTarget=iCopySourceInTarget;
    }

    @Value("${spring.jpa.properties.hibernate.jdbc.batch_size}")
    private int sizePage;

    @Override
    public void copyAuditLog(){
        logger.info("AuditLog - Copy - Begin;");

        Pageable pageAuditLogSearch = PageRequest.of(0, sizePage);
        Page<AuditLog> auditLogPage;
        do {
            auditLogPage = sourceAuditLogRepository.findAllAuditLog(pageAuditLogSearch);
            iCopySourceInTarget.copyAuditLog(auditLogPage.getContent());
            pageAuditLogSearch = pageAuditLogSearch.next();
        } while (auditLogPage.hasNext());

        logger.info("AuditLog - Copy - End;");
    }
}
