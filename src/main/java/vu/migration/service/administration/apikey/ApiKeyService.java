package vu.migration.service.administration.apikey;

import org.springframework.stereotype.Service;
import vu.migration.mappers.ApiKeyMapper;
import vu.migration.source.repository.administration.apikey.SourceApiKeyRepository;
import vu.migration.target.entities.administration.apikey.ApiKey;
import vu.migration.target.repositories.administration.apikey.ApiKeyRepository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ApiKeyService implements IApiKeyService{
    private final ApiKeyRepository apiKeyRepository;
    private final SourceApiKeyRepository sourceApiKeyRepository;
    private final ApiKeyMapper apiKeyMapper;

    public ApiKeyService(ApiKeyRepository apiKeyRepository,SourceApiKeyRepository sourceApiKeyRepository,ApiKeyMapper apiKeyMapper){
        this.apiKeyRepository=apiKeyRepository;
        this.sourceApiKeyRepository=sourceApiKeyRepository;
        this.apiKeyMapper=apiKeyMapper;
    }

    @Override
    public List<ApiKey> getAllApiKey(){
        return sourceApiKeyRepository.findAll().stream()
                .map(apiKeyMapper::sourceToApiKey)
                .collect(Collectors.toList());
    }

    @Override
    public void saveAllApiKey(List<ApiKey> apiKey){
        apiKeyRepository.saveAll(apiKey);
    }

    @Override
    public ApiKey findByApiKeyId(int apiKey){
        Optional<ApiKey> key = apiKeyRepository.findById(apiKey);

        return key.orElse(null);
    }
}
