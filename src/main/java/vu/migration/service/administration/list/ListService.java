package vu.migration.service.administration.list;

import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vu.data.models.administration.list.*;
import vu.migration.mappers.BackgroundBatchStatusMapper;
import vu.migration.mappers.CustomListMapper;
import vu.migration.source.repository.administration.list.*;
import vu.migration.target.repositories.administration.list.BlockedIpValuesRepository;
import vu.migration.target.repositories.administration.list.CustomListRepository;
import vu.migration.target.repositories.administration.list.CustomListValueRepository;
import vu.migration.target.repositories.administration.list.FileEntryRepository;
import vu.migration.target.repositories.administration.list.batch.BackgroundBatchStatusRepository;


import java.util.ArrayList;
import java.util.List;

@Service
public class ListService implements IListService{

    private final SourceBackgroundBatchStatusRepository sourceBackgroundBatchStatusRepository;
    private final BackgroundBatchStatusRepository backgroundBatchStatusRepository;
    private final SourceBlockedIpValuesRepository sourceBlockedIpValuesRepository;
    private final SourceCustomListRepository sourceCustomListRepository;
    private final CustomListRepository customListRepository;
    private final SourceCustomListValueRepository sourceCustomListValueRepository;
    private final CustomListValueRepository customListValueRepository;
    private final SourceFileEntryRepository sourceFileEntryRepository;
    private final FileEntryRepository fileEntryRepository;
    private final BlockedIpValuesRepository blockedIpValuesRepository;
    private final BackgroundBatchStatusMapper backgroundBatchStatusMapper;
    private final CustomListMapper customListMapper;


    public ListService(SourceBackgroundBatchStatusRepository sourceBackgroundBatchStatusRepository,BackgroundBatchStatusRepository backgroundBatchStatusRepository,SourceBlockedIpValuesRepository sourceBlockedIpValuesRepository,SourceCustomListRepository sourceCustomListRepository,CustomListRepository customListRepository,SourceCustomListValueRepository sourceCustomListValueRepository,CustomListValueRepository customListValueRepository,SourceFileEntryRepository sourceFileEntryRepository,FileEntryRepository fileEntryRepository,BlockedIpValuesRepository blockedIpValuesRepository,BackgroundBatchStatusMapper backgroundBatchStatusMapper,CustomListMapper customListMapper){
        this.sourceBackgroundBatchStatusRepository=sourceBackgroundBatchStatusRepository;
        this.backgroundBatchStatusRepository=backgroundBatchStatusRepository;
        this.sourceBlockedIpValuesRepository=sourceBlockedIpValuesRepository;
        this.sourceCustomListRepository=sourceCustomListRepository;
        this.customListRepository=customListRepository;
        this.sourceCustomListValueRepository=sourceCustomListValueRepository;
        this.customListValueRepository=customListValueRepository;
        this.sourceFileEntryRepository=sourceFileEntryRepository;
        this.fileEntryRepository=fileEntryRepository;
        this.blockedIpValuesRepository=blockedIpValuesRepository;
        this.backgroundBatchStatusMapper=backgroundBatchStatusMapper;
        this.customListMapper=customListMapper;
    }

    private @NotNull List<SourceBackgroundBatchStatus> getAllBackgroundBatchStatus(){
        return sourceBackgroundBatchStatusRepository.findAll();
    }

    private @NotNull List<SourceCustomList> getAllCustomList(){
        return sourceCustomListRepository.findAll();
    }

    private @NotNull List<SourceCustomListValue> getAllCustomListValue(){
        return sourceCustomListValueRepository.findAll();
    }

    private @NotNull List<SourceFileEntry> getAllFileEntry(){
        return sourceFileEntryRepository.findAll();
    }

    @Override
    public void copyBackgroundBatchStatus(){
        List<SourceBackgroundBatchStatus> sourceList = getAllBackgroundBatchStatus();
        List<vu.migration.target.entities.administration.list.BackgroundBatchStatus> targetList =
        backgroundBatchStatusMapper.toTargetList(sourceList);

        backgroundBatchStatusRepository.saveAll(targetList);
    }

    @Override
    public void copyCustomList(){

        List<SourceCustomList> sourceList = getAllCustomList();

        List<vu.migration.target.entities.administration.list.CustomList> targetList = customListMapper.toEntityList(sourceList);

        customListRepository.saveAll(targetList);

        copyCustomListValue();
    }

    private void copyCustomListValue(){
        List<SourceCustomListValue> sourceCustomListValues= getAllCustomListValue();

        for(SourceCustomListValue sourceCustomListValue : sourceCustomListValues){
            vu.migration.target.entities.administration.list.CustomListValue listValue = new vu.migration.target.entities.administration.list.CustomListValue();
            listValue.setListValue(sourceCustomListValue.getListValue());
            listValue.setCustomListId(sourceCustomListValue.getCustomListId());
            customListValueRepository.save(listValue);
        }
    }

    @Override
    public void copyFileEntry(){
        List<SourceFileEntry> sourceFileEntryLists= getAllFileEntry();
        List<vu.migration.target.entities.administration.list.FileEntry> targetFileEntry = new ArrayList<>();

        for(SourceFileEntry sourceFileEntry : sourceFileEntryLists){
            vu.migration.target.entities.administration.list.FileEntry list = new vu.migration.target.entities.administration.list.FileEntry();
            list.setFileName(sourceFileEntry.getFileName());
            list.setFilePath(sourceFileEntry.getFilePath());
            list.setFileSizeInKB(sourceFileEntry.getFileSizeInKB());
            list.setFileStatus(sourceFileEntry.getFileStatus());
            list.setAdditionalFieldId(sourceFileEntry.getAdditionalFieldId());
            list.setAutoImport(sourceFileEntry.isAutoImport());
            list.setStatusDetail(sourceFileEntry.getStatusDetail());
            list.setUploadDate(sourceFileEntry.getUploadDate());
            list.setType(vu.migration.target.entities.administration.list.FileEntry.Type.valueOf(sourceFileEntry.getType().name()));
            targetFileEntry.add(list);
        }

        fileEntryRepository.saveAll(targetFileEntry);
    }

    @Override
    @Transactional
    public void saveAllBlockedIpValues(List<vu.migration.target.entities.administration.list.BlockedIpValues> values) {
        blockedIpValuesRepository.saveAll(values);
    }

    @Override
    public Page<SourceBlockedIpValues> findAllSourceBlockedIpValues(Pageable pageable) {
        return sourceBlockedIpValuesRepository.findAll(pageable);
    }
}
