package vu.migration.service.administration.list;

import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vu.data.models.administration.list.SourceBlockedIpValues;
import vu.migration.target.entities.administration.list.BlockedIpValues;

import java.util.List;

public interface IListService{
    void copyBackgroundBatchStatus();

    void copyCustomList();

    void copyFileEntry();

    @Transactional
    void saveAllBlockedIpValues(List<BlockedIpValues> values);

    Page<SourceBlockedIpValues> findAllSourceBlockedIpValues(Pageable pageable);
}
