package vu.migration.service.administration.settings.parameter;

import org.springframework.stereotype.Service;
import vu.data.models.administration.settings.parameter.SourceParameter;
import vu.migration.source.repository.administration.settings.parameter.SourceParameterRepository;
import vu.migration.target.entities.administration.settings.parameter.Parameter;
import vu.migration.target.repositories.administration.setting.parameter.ParameterRepository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class ParameterService implements IParameterService{

    private final ParameterRepository parameterRepository;
    private final SourceParameterRepository sourceParameterRepository;

    public ParameterService(ParameterRepository parameterRepository,SourceParameterRepository sourceParameterRepository){
        this.parameterRepository=parameterRepository;
        this.sourceParameterRepository=sourceParameterRepository;
    }

    @Override
    public Parameter getParameterByEventTypeAndName(Integer eventTypeId,String name){
        if(Objects.equals(name,"randomSeed"))
            name = "RandomSeed";
        Optional<Parameter> parameter = parameterRepository.findByEventTypeIdAndName(eventTypeId, name);

        return parameter.orElse(null);
    }

    @Override
    public void save(Parameter parameter){
        parameterRepository.save(parameter);
    }

    @Override
    public void saveAllParameters(List<Parameter> parameters){
        parameterRepository.saveAll(parameters);
    }

    @Override
    public List<SourceParameter> getAllParameter(){
        return sourceParameterRepository.findAll();
    }

    @Override
    public SourceParameter getParameterById(Integer id){
        Optional<SourceParameter> parameter = sourceParameterRepository.findById(id);
        return parameter.orElse(null);
    }

    @Override
    public Parameter getParameterBySourceParameterId(int id) {
        SourceParameter parameterSource = getParameterById(id);

        return getParameterByEventTypeAndName(parameterSource.getEventTypeId(),parameterSource.getName());
    }
}
