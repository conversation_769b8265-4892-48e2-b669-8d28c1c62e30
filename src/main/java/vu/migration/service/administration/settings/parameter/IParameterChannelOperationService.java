package vu.migration.service.administration.settings.parameter;

import vu.data.models.administration.settings.parameter.SourceParameterChannelOperation;
import vu.migration.target.entities.administration.settings.parameter.ParameterChannelOperation;

import java.util.List;

public interface IParameterChannelOperationService {
    void save(ParameterChannelOperation parameterChannelOperation);

    void saveAllParameterChannelOperation(List<ParameterChannelOperation> parameterChannelOperations);

    ParameterChannelOperation getParameterChannelOperation(int channelId,int operationTypeId,int parameterId);

    List<SourceParameterChannelOperation> getAllParameterChannelOperation();
}
