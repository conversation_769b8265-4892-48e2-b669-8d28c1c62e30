package vu.migration.service.administration.settings.channel;

import vu.data.models.administration.settings.channel.SourceChannel;
import vu.migration.target.entities.administration.settings.channel.Channel;

import java.util.List;

public interface IChannelService{
    Channel findChannelByName(String channelName);

    Channel save(Channel channel);

    List<Channel> saveAll(List<Channel> channel);

    List<SourceChannel> getAllChannel();

    SourceChannel getChannelById(Integer id);

    int getChannelId(int id);

    void copyChannel();
}
