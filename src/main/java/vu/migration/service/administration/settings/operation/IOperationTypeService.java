package vu.migration.service.administration.settings.operation;

import vu.data.models.administration.settings.operation.SourceOperationType;
import vu.migration.target.entities.administration.settings.operation.OperationType;

import java.util.List;

public interface IOperationTypeService{
    OperationType findOperationTypeByName(String nameOperationType);

    OperationType save(OperationType operationType);

    List<OperationType> saveAll(List<OperationType> operationType);

    List<SourceOperationType> getAllOperationType();

    SourceOperationType getOperationTypeById(Integer id);

    int getOperationTypeId(int id);

    void copyOperationType();
}
