package vu.migration.service.administration.settings.parameter;

import vu.data.models.administration.settings.parameter.SourceParameter;
import vu.migration.target.entities.administration.settings.parameter.Parameter;

import java.util.List;

public interface IParameterService{
    Parameter getParameterByEventTypeAndName(Integer eventTypeId,String name);

    void save(Parameter parameter);

    void saveAllParameters(List<Parameter> parameters);

    List<SourceParameter> getAllParameter();

    SourceParameter getParameterById(Integer id);

    Parameter getParameterBySourceParameterId(int id);
}
