package vu.migration.service.administration.settings.operation;

import org.springframework.stereotype.Service;
import vu.data.models.administration.settings.operation.SourceOperationType;
import vu.migration.mappers.OperationTypeMapper;
import vu.migration.source.repository.administration.settings.operation.SourceOperationTypeRepository;
import vu.migration.target.entities.administration.settings.operation.OperationType;
import vu.migration.target.repositories.administration.setting.operation.OperationTypeRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class OperationTypeService implements IOperationTypeService{

    private final OperationTypeRepository operationTypeRepository;
    private final SourceOperationTypeRepository sourceOperationTypeRepository;

    private final OperationTypeMapper operationTypeMapper;

    public OperationTypeService(OperationTypeRepository operationTypeRepository,SourceOperationTypeRepository sourceOperationTypeRepository,OperationTypeMapper operationTypeMapper){
        this.operationTypeRepository=operationTypeRepository;
        this.sourceOperationTypeRepository=sourceOperationTypeRepository;
        this.operationTypeMapper=operationTypeMapper;
    }

    @Override
    public OperationType findOperationTypeByName (String nameOperationType){
        return operationTypeRepository.findByName(nameOperationType);
    }

    @Override
    public OperationType save(OperationType operationType){
        return operationTypeRepository.save(operationType);
    }

    @Override
    public List<OperationType> saveAll(List<OperationType> operationType){
        return operationTypeRepository.saveAll(operationType);
    }

    @Override
    public List<SourceOperationType> getAllOperationType(){
        return sourceOperationTypeRepository.findAll();
    }

    @Override
    public SourceOperationType getOperationTypeById(Integer id){
        Optional<SourceOperationType> operationType = sourceOperationTypeRepository.findById(id);

        return operationType.orElse(null);
    }

    @Override
    public int getOperationTypeId(int id) {
        String operationTypeName = getOperationTypeById(id).getName();
        return findOperationTypeByName(operationTypeName).getId();
    }

    @Override
    public void copyOperationType() {

        List<SourceOperationType> sourceTypes = getAllOperationType();
        List<OperationType> toSave = new ArrayList<>();

        for (SourceOperationType source : sourceTypes) {
            String normalizedName = operationTypeMapper.normalizedName(source.getName());
            OperationType existing = findOperationTypeByName(normalizedName);
            OperationType target = (existing != null) ? existing : new OperationType();

            operationTypeMapper.updateOperationTypeFromSource(target, source);

            // Solo guardar si se mapeó correctamente (evento válido)
            if (target.getEventTypeId() != 0) {
                toSave.add(target);
            }
        }

        saveAll(toSave);
    }
}
