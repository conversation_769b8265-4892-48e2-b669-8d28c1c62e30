package vu.migration.service.administration.settings.parameter;

import org.springframework.stereotype.Service;
import vu.data.models.administration.settings.parameter.SourceParameterChannelOperation;
import vu.migration.source.repository.administration.settings.parameter.SourceParameterChannelOperationRepository;
import vu.migration.target.entities.administration.settings.parameter.ParameterChannelOperation;
import vu.migration.target.repositories.administration.setting.parameter.ParameterChannelOperationRepository;

import java.util.List;
import java.util.Optional;

@Service
public class ParameterChannelOperationService implements IParameterChannelOperationService{
    private final ParameterChannelOperationRepository parameterChannelOperationRepository;
    private final SourceParameterChannelOperationRepository sourceParameterChannelOperationRepository;

    public ParameterChannelOperationService(ParameterChannelOperationRepository parameterChannelOperationRepository,SourceParameterChannelOperationRepository sourceParameterChannelOperationRepository){
        this.parameterChannelOperationRepository=parameterChannelOperationRepository;
        this.sourceParameterChannelOperationRepository=sourceParameterChannelOperationRepository;
    }

    @Override
    public void save(ParameterChannelOperation parameterChannelOperation){
        parameterChannelOperationRepository.save(parameterChannelOperation);
    }

    @Override
    public void saveAllParameterChannelOperation(List<ParameterChannelOperation> parameterChannelOperations){
        parameterChannelOperationRepository.saveAll(parameterChannelOperations);
    }

    @Override
    public ParameterChannelOperation getParameterChannelOperation(int channelId,int operationTypeId,int parameterId){
        Optional<ParameterChannelOperation> parameterChannelOperationOptional = parameterChannelOperationRepository.findByChannelIdAndOperationTypeIdAndParameterId(channelId,operationTypeId, parameterId);

        return parameterChannelOperationOptional.orElse(null);
    }

    @Override
    public List<SourceParameterChannelOperation> getAllParameterChannelOperation(){
        return sourceParameterChannelOperationRepository.findAll();
    }
}
