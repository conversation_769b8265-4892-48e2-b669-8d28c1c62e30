package vu.migration.service.administration.settings.channel;

import org.springframework.stereotype.Service;
import vu.data.models.administration.settings.channel.SourceChannel;
import vu.migration.mappers.ChannelMapper;
import vu.migration.source.repository.administration.settings.channel.SourceChannelRepository;
import vu.migration.target.entities.administration.settings.channel.Channel;
import vu.migration.target.repositories.administration.setting.channel.ChannelRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ChannelService implements IChannelService{
    private final ChannelRepository channelRepository;
    private final SourceChannelRepository sourceChannelRepository;

    private final ChannelMapper channelMapper;

    public ChannelService(ChannelRepository channelRepository,SourceChannelRepository sourceChannelRepository,ChannelMapper channelMapper){
        this.channelRepository=channelRepository;
        this.sourceChannelRepository=sourceChannelRepository;
        this.channelMapper=channelMapper;
    }

    @Override
    public Channel findChannelByName(String channelName){
        return channelRepository.findByName(channelName);
    }

    @Override
    public Channel save(Channel channel){
        return channelRepository.save(channel);
    }

    @Override
    public List<Channel> saveAll(List<Channel> channel){
        return channelRepository.saveAll(channel);
    }

    @Override
    public List<SourceChannel> getAllChannel(){
        return sourceChannelRepository.findAll();
    }

    @Override
    public SourceChannel getChannelById(Integer id){
        Optional<SourceChannel> channel = sourceChannelRepository.findById(id);
        return channel.orElse(null);
    }

    @Override
    public int getChannelId(int id) {
        String channelName = getChannelById(id).getName();
        return findChannelByName(channelName).getId();
    }

    @Override
    public void copyChannel(){
        List<SourceChannel> sourceChannels = getAllChannel();
        List<Channel> channelsToSave = new ArrayList<>();

        for (SourceChannel source : sourceChannels) {
            Channel existing = findChannelByName(source.getName());
            Channel target = (existing != null) ? existing : new Channel();

            channelMapper.updateChannelFromSource(target, source);
            channelsToSave.add(target);
        }

        saveAll(channelsToSave);
    }
}
