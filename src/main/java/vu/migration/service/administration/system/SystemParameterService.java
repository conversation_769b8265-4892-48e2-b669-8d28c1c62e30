package vu.migration.service.administration.system;

import org.springframework.stereotype.Service;
import vu.data.models.administration.system.SourceSystemParameter;
import vu.migration.source.repository.administration.system.SourceSystemParameterRepository;
import vu.migration.target.entities.administration.system.SystemParameter;
import vu.migration.target.repositories.auth.SystemParametersRepository;

import java.util.List;

@Service
public class SystemParameterService implements ISystemParameterService{
    private final SystemParametersRepository systemParametersRepository;
    private final SourceSystemParameterRepository sourceSystemParameterRepository;

    public SystemParameterService(SystemParametersRepository systemParametersRepository,SourceSystemParameterRepository sourceSystemParameterRepository){
        this.systemParametersRepository=systemParametersRepository;
        this.sourceSystemParameterRepository=sourceSystemParameterRepository;
    }

    @Override
    public SystemParameter findByParameterName(String parameter){
        return systemParametersRepository.findByParameterName(parameter);
    }

    @Override
    public void save(SystemParameter systemParameter){
        systemParametersRepository.save(systemParameter);
    }

    @Override
    public List<SourceSystemParameter> getAllSystemParameter(){
        return sourceSystemParameterRepository.findAll();
    }

    @Override
    public void saveAllSystemParameter(List<SystemParameter> systemParameters){
        systemParametersRepository.saveAll(systemParameters);
    }
}
