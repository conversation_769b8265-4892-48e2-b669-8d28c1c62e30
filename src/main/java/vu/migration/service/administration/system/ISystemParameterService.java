package vu.migration.service.administration.system;

import vu.data.models.administration.system.SourceSystemParameter;
import vu.migration.target.entities.administration.system.SystemParameter;

import java.util.List;

public interface ISystemParameterService{
    SystemParameter findByParameterName(String parameter);

    void save(SystemParameter systemParameter);

    List<SourceSystemParameter> getAllSystemParameter();

    void saveAllSystemParameter(List<SystemParameter> systemParameters);
}
