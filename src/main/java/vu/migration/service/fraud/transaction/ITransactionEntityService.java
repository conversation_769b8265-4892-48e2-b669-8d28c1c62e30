package vu.migration.service.fraud.transaction;

import org.springframework.transaction.annotation.Transactional;
import vu.data.models.fraud.transaction.SourceAdditionalParametersTransaction;
import vu.data.models.fraud.transaction.SourceTransactionEntity;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.target.entities.fraud.transaction.AdditionalParametersTransaction;
import vu.migration.target.entities.fraud.transaction.TransactionEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ITransactionEntityService{
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    List<SourceTransactionEntity> findTransactionsWithFilters(long lastSeenId, long fromId, int size);

    @Transactional
    void copyTransactions(List<TransactionEntity> transactions);

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    List<SourceAdditionalParametersTransaction> findAdditionalParametersTransactionWithFilters(long lastSeenId, long fromId, int size);

    @Transactional
    void copyAdditionalParametersTransaction(List<AdditionalParametersTransaction> additionalParametersTransaction);

    Optional<IdRange> findMinAndMaxIdByDateRange(LocalDateTime fromDate,LocalDateTime toDate);

    Optional<IdRange> findMinAndMaxId();

}
