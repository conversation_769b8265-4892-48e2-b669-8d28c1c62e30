package vu.migration.service.fraud.transaction;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vu.data.models.fraud.transaction.SourceAdditionalParametersTransaction;
import vu.data.models.fraud.transaction.SourceTransactionEntity;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.source.repository.fraud.transaction.SourceAdditionalParametersTransactionRepository;
import vu.migration.source.repository.fraud.transaction.SourceTransactionEntityRepository;
import vu.migration.target.entities.fraud.transaction.AdditionalParametersTransaction;
import vu.migration.target.entities.fraud.transaction.TransactionEntity;
import vu.migration.target.repositories.fraud.transaction.AdditionalParameterTransactionRepository;
import vu.migration.target.repositories.fraud.transaction.TransactionEntityRepository;
import vu.migration.util.SpecificationUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class TransactionEntityService implements ITransactionEntityService{
    private final SourceTransactionEntityRepository sourceTransactionEntityRepository;
    private final TransactionEntityRepository transactionEntityRepository;
    private final SourceAdditionalParametersTransactionRepository sourceAdditionalParametersTransactionRepository;
    private final AdditionalParameterTransactionRepository additionalParameterTransactionRepository;

    @PersistenceContext(unitName = "Target")
    private EntityManager targetEntityManager;

    public TransactionEntityService(SourceTransactionEntityRepository sourceTransactionEntityRepository, TransactionEntityRepository transactionEntityRepository, SourceAdditionalParametersTransactionRepository sourceAdditionalParametersTransactionRepository, AdditionalParameterTransactionRepository additionalParameterTransactionRepository){
        this.sourceTransactionEntityRepository=sourceTransactionEntityRepository;
        this.transactionEntityRepository=transactionEntityRepository;
        this.sourceAdditionalParametersTransactionRepository=sourceAdditionalParametersTransactionRepository;
        this.additionalParameterTransactionRepository=additionalParameterTransactionRepository;
    }

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    @Override
    public List<SourceTransactionEntity> findTransactionsWithFilters(long lastSeenId, long fromId, int size) {
        Specification<SourceTransactionEntity> spec = SpecificationUtils.filteredByIdRange("id", "id", lastSeenId, fromId, size, false);
        return sourceTransactionEntityRepository.findAll(spec);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyTransactions(List<TransactionEntity> transactions) {
        transactionEntityRepository.saveAll(transactions);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    @Override
    public List<SourceAdditionalParametersTransaction> findAdditionalParametersTransactionWithFilters(long lastSeenId, long fromId, int size) {
        Specification<SourceAdditionalParametersTransaction> spec = SpecificationUtils.filteredByIdRange("id", "transactionId", lastSeenId, fromId, size, false);
        return sourceAdditionalParametersTransactionRepository.findAll(spec);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyAdditionalParametersTransaction(List<AdditionalParametersTransaction> additionalParametersTransaction) {
        additionalParameterTransactionRepository.saveAll(additionalParametersTransaction);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Optional<IdRange> findMinAndMaxIdByDateRange(LocalDateTime fromDate,LocalDateTime toDate){
        Optional<SourceTransactionEntity> minIdList = sourceTransactionEntityRepository.findFirstByEventDateBetweenOrderByEventDateAscIdAsc(fromDate, toDate);
        Optional<SourceTransactionEntity> maxIdList = sourceTransactionEntityRepository.findFirstByEventDateBetweenOrderByEventDateDescIdDesc(fromDate, toDate);

        Long minId = minIdList.isEmpty() ? null : minIdList.get().getId();
        Long maxId = maxIdList.isEmpty() ? null : maxIdList.get().getId();

        if (minId == null && maxId == null) {
            return Optional.empty();
        }

        return Optional.of(new IdRange(minId, maxId));
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Optional<IdRange> findMinAndMaxId(){
        return sourceTransactionEntityRepository.findMinAndMaxId();
    }
}
