package vu.migration.service.fraud.event;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import vu.data.models.fraud.event.SourceAdditionalParametersEvent;
import vu.data.models.fraud.event.SourceEvents;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.target.entities.fraud.event.AdditionalParametersEvent;
import vu.migration.target.entities.fraud.event.Events;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface IEventService{
    Page<SourceEvents> findEventsWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size);

    @Transactional
    void copyEvent(List<Events> events);

    Page<SourceAdditionalParametersEvent> findAdditionalParametersEventWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size);

    @Transactional
    void copyAdditionalParametersEvent(List<AdditionalParametersEvent> additionalParametersTransaction);

    Optional<IdRange> findMinAndMaxIdByDateRange(LocalDateTime fromDate,LocalDateTime toDate);

    Optional<IdRange> findMinAndMaxId();
}
