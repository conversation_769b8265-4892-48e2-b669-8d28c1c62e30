package vu.migration.service.fraud.event;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import vu.data.models.fraud.event.SourceRuleMatchedEvent;
import vu.data.models.fraud.transaction.SourceRuleMatchedTransaction;
import vu.migration.source.repository.fraud.event.SourceRuleMatchedEventRepository;
import vu.migration.source.repository.fraud.transaction.SourceRuleMatchedTransactionRepository;
import vu.migration.target.entities.fraud.event.RuleMatchedEvent;
import vu.migration.target.entities.fraud.transaction.RuleMatchedTransaction;
import vu.migration.target.repositories.fraud.event.RuleMatchedEventRepository;
import vu.migration.target.repositories.fraud.transaction.RuleMatchedTransactionRepository;
import vu.migration.util.SpecificationUtils;

import java.util.List;
import java.util.Optional;

@Service
public class RuleMatchedEventService implements IRuleMatchedEventService{

    private final SourceRuleMatchedEventRepository sourceRuleMatchedEventRepository;
    private final SourceRuleMatchedTransactionRepository sourceRuleMatchedTransactionRepository;
    private final RuleMatchedTransactionRepository ruleMatchedTransactionRepository;
    private final RuleMatchedEventRepository ruleMatchedEventRepository;

    @PersistenceContext(unitName = "Target")
    private EntityManager targetEntityManager;

    public RuleMatchedEventService(SourceRuleMatchedEventRepository sourceRuleMatchedEventRepository,SourceRuleMatchedTransactionRepository sourceRuleMatchedTransactionRepository,RuleMatchedTransactionRepository ruleMatchedTransactionRepository,RuleMatchedEventRepository ruleMatchedEventRepository){
        this.sourceRuleMatchedEventRepository=sourceRuleMatchedEventRepository;
        this.sourceRuleMatchedTransactionRepository=sourceRuleMatchedTransactionRepository;
        this.ruleMatchedTransactionRepository=ruleMatchedTransactionRepository;
        this.ruleMatchedEventRepository=ruleMatchedEventRepository;
    }

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    @Override
    public List<SourceRuleMatchedTransaction> findRuleMatchedTransactionWithFilters(long lastSeenId, long fromId, int size) {
        Specification<SourceRuleMatchedTransaction> spec = SpecificationUtils.filteredByIdRange("id", "eventId", lastSeenId, fromId, size, false);
        return sourceRuleMatchedTransactionRepository.findAll(spec);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyRuleMatchedTransaction(List<RuleMatchedTransaction> ruleMatchedTransaction) {
        ruleMatchedTransactionRepository.saveAll(ruleMatchedTransaction);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<SourceRuleMatchedEvent> findRuleMatchedEventWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size) {
        Specification<SourceRuleMatchedEvent> spec = SpecificationUtils.buildRangeSpec("id", fromId, toId);

        Pageable pageable = SpecificationUtils.buildPageable(fromId, toId, page, size);

        return sourceRuleMatchedEventRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyRuleMatchedEvent(List<RuleMatchedEvent> ruleMatchedEvent) {
        ruleMatchedEventRepository.saveAll(ruleMatchedEvent);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }
}
