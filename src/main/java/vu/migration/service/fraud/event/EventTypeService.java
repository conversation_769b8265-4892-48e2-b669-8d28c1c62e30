package vu.migration.service.fraud.event;

import org.springframework.stereotype.Service;
import vu.data.models.fraud.event.SourceEventType;
import vu.migration.source.repository.fraud.event.SourceEventTypeRepository;

@Service
public class EventTypeService implements IEventTypeService{
    private final SourceEventTypeRepository sourceEventTypeRepository;

    public EventTypeService(SourceEventTypeRepository sourceEventTypeRepository){
        this.sourceEventTypeRepository=sourceEventTypeRepository;
    }

    @Override
    public SourceEventType getEventType(int id){
        if(sourceEventTypeRepository.findById(id).isPresent())
            return sourceEventTypeRepository.findById(id).get();
        return null;
    }
}
