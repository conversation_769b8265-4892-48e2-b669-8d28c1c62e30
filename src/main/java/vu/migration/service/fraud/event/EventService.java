package vu.migration.service.fraud.event;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import vu.data.models.fraud.event.SourceAdditionalParametersEvent;
import vu.data.models.fraud.event.SourceEvents;
import vu.migration.source.repository.fraud.event.SourceAdditionalParametersEventRepository;
import vu.migration.source.repository.fraud.event.SourceEventRepository;
import vu.migration.source.repository.fraud.transaction.IdRange;
import vu.migration.target.entities.fraud.event.AdditionalParametersEvent;
import vu.migration.target.entities.fraud.event.Events;
import vu.migration.target.repositories.fraud.event.AdditionalParameterEventRepository;
import vu.migration.target.repositories.fraud.event.EventRepository;
import vu.migration.util.SpecificationUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class EventService implements IEventService{
    private final SourceEventRepository sourceEventRepository;
    private final SourceAdditionalParametersEventRepository sourceAdditionalParametersEventRepository;
    private final EventRepository eventRepository;
    private final AdditionalParameterEventRepository additionalParameterEventRepository;

    @PersistenceContext(unitName = "Target")
    private EntityManager targetEntityManager;

    public EventService(SourceEventRepository sourceEventRepository,SourceAdditionalParametersEventRepository sourceAdditionalParametersEventRepository,EventRepository eventRepository,AdditionalParameterEventRepository additionalParameterEventRepository){
        this.sourceEventRepository=sourceEventRepository;
        this.sourceAdditionalParametersEventRepository=sourceAdditionalParametersEventRepository;
        this.eventRepository=eventRepository;
        this.additionalParameterEventRepository=additionalParameterEventRepository;
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<SourceEvents> findEventsWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size) {
        Specification<SourceEvents> spec = SpecificationUtils.buildRangeSpec("id", fromId, toId);

        Pageable pageable = SpecificationUtils.buildPageable(fromId, toId, page, size);

        return sourceEventRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyEvent(List<Events> events) {
        eventRepository.saveAll(events);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<SourceAdditionalParametersEvent> findAdditionalParametersEventWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size) {
        Specification<SourceAdditionalParametersEvent> spec = SpecificationUtils.buildRangeSpec("id", fromId, toId);

        Pageable pageable = SpecificationUtils.buildPageable(fromId, toId, page, size);

        return sourceAdditionalParametersEventRepository.findAll(spec, pageable);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyAdditionalParametersEvent(List<AdditionalParametersEvent> additionalParametersTransaction) {
        additionalParameterEventRepository.saveAll(additionalParametersTransaction);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Optional<IdRange> findMinAndMaxIdByDateRange(LocalDateTime fromDate,LocalDateTime toDate){
        return sourceEventRepository.findMinAndMaxIdByDateRange(fromDate, toDate);
    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Optional<IdRange> findMinAndMaxId(){
        return sourceEventRepository.findMinAndMaxId();
    }
}
