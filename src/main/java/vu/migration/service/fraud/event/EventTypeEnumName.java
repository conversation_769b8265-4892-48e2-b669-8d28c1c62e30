package vu.migration.service.fraud.event;

public class EventTypeEnumName {
    public enum EnumEventType{
        NONE,           // 0
        TRANSACTION,     // 1
        EVENT,          // 2
        FINGERPRINT_BROWSER_V1,          // 3
        FINGERPRINT_MOBILE,          // 4
        FINGERPRINT_BROWSER_V2
    }

    public static final int TRANSACTION = 1;
    public static final int EVENT = 2;
    public static final int FINGERPRINT_BROWSER_V1 = 3;
    public static final int FINGERPRINT_MOBILE = 4;
    public static final int FINGERPRINT_BROWSER_V2 = 5;
}