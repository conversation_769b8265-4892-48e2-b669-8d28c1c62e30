package vu.migration.service.fraud.event;

import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import vu.data.models.fraud.event.SourceRuleMatchedEvent;
import vu.data.models.fraud.transaction.SourceRuleMatchedTransaction;
import vu.migration.target.entities.fraud.event.RuleMatchedEvent;
import vu.migration.target.entities.fraud.transaction.RuleMatchedTransaction;

import java.util.List;
import java.util.Optional;

public interface IRuleMatchedEventService{
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    List<SourceRuleMatchedTransaction> findRuleMatchedTransactionWithFilters(long lastSeenId, long fromId, int size);

    @Transactional
    void copyRuleMatchedTransaction(List<RuleMatchedTransaction> ruleMatchedTransaction);

    Page<SourceRuleMatchedEvent> findRuleMatchedEventWithFilters(Optional<Long> fromId,Optional<Long> toId,int page,int size);

    @Transactional
    void copyRuleMatchedEvent(List<RuleMatchedEvent> ruleMatchedEvent);
}
