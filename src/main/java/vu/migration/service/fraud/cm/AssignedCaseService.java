package vu.migration.service.fraud.cm;

import jakarta.persistence.criteria.Predicate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import vu.data.models.fraud.cm.SourceAssignedCase;
import vu.data.models.fraud.cm.SourceCaseAdditionalInfo;
import vu.data.models.fraud.cm.SourceCaseAttachment;
import vu.data.models.fraud.cm.SourceLastAssignedCase;
import vu.data.models.fraud.event.CaseStatusEnumName;
import vu.migration.source.repository.fraud.cm.SourceAssignedCaseRepository;
import vu.migration.source.repository.fraud.cm.SourceCaseAdditionalInfoRepository;
import vu.migration.source.repository.fraud.cm.SourceCaseAttachmentRepository;
import vu.migration.source.repository.fraud.cm.SourceLastAssignedCaseRepository;
import vu.migration.target.entities.fraud.cm.AssignedCase;
import vu.migration.target.entities.fraud.cm.CaseAdditionalInfo;
import vu.migration.target.entities.fraud.cm.CaseAttachment;
import vu.migration.target.entities.fraud.cm.LastAssignedCase;
import vu.migration.target.entities.fraud.transaction.TransactionEntity;
import vu.migration.target.repositories.fraud.cm.AssignedCaseRepository;
import vu.migration.target.repositories.fraud.cm.CaseAdditionalInfoRepository;
import vu.migration.target.repositories.fraud.cm.CaseAttachmentRepository;
import vu.migration.target.repositories.fraud.cm.LastAssignedCaseRepository;
import vu.migration.util.SpecificationUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class AssignedCaseService implements IAssignedCaseService{
    private final AssignedCaseRepository assignedCaseRepository;
    private final SourceAssignedCaseRepository sourceAssignedCaseRepository;
    private final LastAssignedCaseRepository lastAssignedCaseRepository;
    private final SourceLastAssignedCaseRepository sourceLastAssignedCaseRepository;
    private final CaseAdditionalInfoRepository caseAdditionalInfoRepository;
    private final SourceCaseAdditionalInfoRepository sourceCaseAdditionalInfoRepository;
    private final CaseAttachmentRepository caseAttachmentRepository;
    private final SourceCaseAttachmentRepository sourceCaseAttachmentRepository;

    @PersistenceContext(unitName = "Target")
    private EntityManager targetEntityManager;


    public AssignedCaseService(AssignedCaseRepository assignedCaseRepository,SourceAssignedCaseRepository sourceAssignedCaseRepository,LastAssignedCaseRepository lastAssignedCaseRepository,SourceLastAssignedCaseRepository sourceLastAssignedCaseRepository,CaseAdditionalInfoRepository caseAdditionalInfoRepository,SourceCaseAdditionalInfoRepository sourceCaseAdditionalInfoRepository,CaseAttachmentRepository caseAttachmentRepository,SourceCaseAttachmentRepository sourceCaseAttachmentRepository){
        this.assignedCaseRepository=assignedCaseRepository;
        this.sourceAssignedCaseRepository=sourceAssignedCaseRepository;
        this.lastAssignedCaseRepository=lastAssignedCaseRepository;
        this.sourceLastAssignedCaseRepository=sourceLastAssignedCaseRepository;
        this.caseAdditionalInfoRepository=caseAdditionalInfoRepository;
        this.sourceCaseAdditionalInfoRepository=sourceCaseAdditionalInfoRepository;
        this.caseAttachmentRepository=caseAttachmentRepository;
        this.sourceCaseAttachmentRepository=sourceCaseAttachmentRepository;
    }

    @Override
    public void save(AssignedCase assignedCase){
        assignedCaseRepository.save(assignedCase);
    }

    @Override
    public List<SourceAssignedCase> getAllAssignedCaseByTransactionId(Long transactionId){
        return sourceAssignedCaseRepository.findAllByTransactionId(transactionId);
    }

    @Override
    public CaseStatusEnumName.Enum getCaseStatusConvert(Enum caseStatusEnumNameSource){
        return switch (caseStatusEnumNameSource.name()) {
            case "ASSIGNED" -> CaseStatusEnumName.Enum.ASSIGNED;
            case "FRAUD" -> CaseStatusEnumName.Enum.FRAUD;
            case "IN_PROGRESS" -> CaseStatusEnumName.Enum.IN_PROGRESS;
            case "INVALID" -> CaseStatusEnumName.Enum.INVALID;
            case "LEGIT" -> CaseStatusEnumName.Enum.LEGIT;
            case "PENDING" -> CaseStatusEnumName.Enum.PENDING;
            default -> CaseStatusEnumName.Enum.NONE;
        };

    }

    @Override
    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    public Page<SourceAssignedCase> getAllAssignedCase(Pageable page){
        return sourceAssignedCaseRepository.findAll(page);
    }

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    @Override
    public List<SourceAssignedCase> findAssignedCaseWithFilters(long lastSeenId, long fromId, int size) {
        Specification<SourceAssignedCase> spec = SpecificationUtils.filteredByIdRange("id", "transactionId", lastSeenId, fromId, size, false);
        return sourceAssignedCaseRepository.findAll(spec);
    }

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    @Override
    public List<SourceCaseAdditionalInfo> findCaseAdditionalInfoWithFilters(long lastSeenId, long fromId, int size) {
        Specification<SourceCaseAdditionalInfo> spec = SpecificationUtils.filteredByIdRange("id", "transactionId", lastSeenId, fromId, size, false);
        return sourceCaseAdditionalInfoRepository.findAll(spec);
    }

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    @Override
    public List<SourceCaseAttachment> findCaseAttachmentWithFilters(long lastSeenId, long fromId, int size) {
        Specification<SourceCaseAttachment> spec = SpecificationUtils.filteredByIdRange("id", "transactionId", lastSeenId, fromId, size, false);
        return sourceCaseAttachmentRepository.findAll(spec);
    }

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    @Override
    public List<SourceLastAssignedCase> findLastAssignedCaseWithFilters(long lastSeenId, long fromId, int size) {
        Specification<SourceLastAssignedCase> spec = SpecificationUtils.filteredByIdRange("id", "transactionId", lastSeenId, fromId, size, false);
        return sourceLastAssignedCaseRepository.findAll(spec);
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyAssignedCase(List<AssignedCase> assignedCase) {
        assignedCaseRepository.saveAll(assignedCase);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyCaseAdditionalInfo(List<CaseAdditionalInfo> caseAdditionalInfo) {
        caseAdditionalInfoRepository.saveAll(caseAdditionalInfo);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyCaseAttachment(List<CaseAttachment> caseAttachment) {
        caseAttachmentRepository.saveAll(caseAttachment);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }

    @Override
    @Transactional(transactionManager = "transactionTargetManager")
    public void copyLastAssignedCase(List<LastAssignedCase> lastAssignedCase){
        lastAssignedCaseRepository.saveAll(lastAssignedCase);
        targetEntityManager.flush();
        targetEntityManager.clear();
    }
}
