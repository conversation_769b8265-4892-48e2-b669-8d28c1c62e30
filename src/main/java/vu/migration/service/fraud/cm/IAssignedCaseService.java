package vu.migration.service.fraud.cm;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;
import vu.data.models.fraud.cm.SourceAssignedCase;
import vu.data.models.fraud.cm.SourceCaseAdditionalInfo;
import vu.data.models.fraud.cm.SourceCaseAttachment;
import vu.data.models.fraud.cm.SourceLastAssignedCase;
import vu.data.models.fraud.event.CaseStatusEnumName;
import vu.migration.target.entities.fraud.cm.AssignedCase;
import vu.migration.target.entities.fraud.cm.CaseAdditionalInfo;
import vu.migration.target.entities.fraud.cm.CaseAttachment;
import vu.migration.target.entities.fraud.cm.LastAssignedCase;

import java.util.List;
import java.util.Optional;

public interface IAssignedCaseService{
    void save(AssignedCase assignedCase);

    List<SourceAssignedCase> getAllAssignedCaseByTransactionId(Long transactionId);

    CaseStatusEnumName.Enum getCaseStatusConvert(Enum caseStatusEnumNameSource);

    Page<SourceAssignedCase> getAllAssignedCase(Pageable page);

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    List<SourceAssignedCase> findAssignedCaseWithFilters(long lastSeenId, long fromId, int size);

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    List<SourceCaseAdditionalInfo> findCaseAdditionalInfoWithFilters(long lastSeenId, long fromId, int size);

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    List<SourceCaseAttachment> findCaseAttachmentWithFilters(long lastSeenId, long fromId, int size);

    @Transactional(readOnly = true, transactionManager = "transactionSourceManager")
    List<SourceLastAssignedCase> findLastAssignedCaseWithFilters(long lastSeenId, long fromId, int size);

    @Transactional
    void copyAssignedCase(List<AssignedCase> assignedCase);

    @Transactional
    void copyCaseAdditionalInfo(List<CaseAdditionalInfo> caseAdditionalInfo);

    @Transactional
    void copyCaseAttachment(List<CaseAttachment> caseAttachment);

    @Transactional
    void copyLastAssignedCase(List<LastAssignedCase> lastAssignedCase);
}
