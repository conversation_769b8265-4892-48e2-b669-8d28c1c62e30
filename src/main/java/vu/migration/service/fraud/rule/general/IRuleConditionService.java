package vu.migration.service.fraud.rule.general;

import vu.data.models.fraud.rule.general.SourceRuleCondition;
import vu.migration.target.entities.fraud.rule.general.RuleCondition;

import java.util.List;

public interface IRuleConditionService{
    RuleCondition getRuleConditionById(int id);

    void save(RuleCondition ruleCondition);

    List<SourceRuleCondition> getAllRuleCondition();

    SourceRuleCondition getRuleConditionByRuleId(int id);
}
