package vu.migration.service.fraud.rule.general;

import vu.data.models.fraud.rule.general.SourceRuleEventStatement;
import vu.data.models.fraud.rule.general.SourceRuleCondition;
import vu.data.models.fraud.rule.general.SourceRuleItem;
import vu.migration.target.entities.fraud.rule.general.RuleCondition;
import vu.migration.target.entities.fraud.rule.general.RuleItem;

import java.util.List;

public interface IRuleItemService{

    Boolean existsByNameAndChannelIdAndOperationTypeId(String name,int channelId,int operationTypeId);

    int save(RuleItem ruleItem);

    void saveAllRuleItem(List<RuleItem> ruleItems);

    List<SourceRuleItem> getAllRuleItem();

    RuleItem getRuleItemBySourceRuleItemId(int sourceRuleItemId);

    List<SourceRuleCondition> getAllRuleCondition();

    void saveAllRuleCondition(List<RuleCondition> ruleConditions);

    SourceRuleItem findByRuleItemId(int ruleId);

    List<SourceRuleEventStatement> findAllRuleEventStatementByRuleId(int ruleId);
}
