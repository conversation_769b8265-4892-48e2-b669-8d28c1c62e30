package vu.migration.service.fraud.rule.general;

import org.springframework.stereotype.Service;
import vu.data.models.fraud.rule.general.SourceRuleCondition;
import vu.migration.source.repository.fraud.rule.general.SourceRuleConditionRepository;
import vu.migration.target.entities.fraud.rule.general.RuleCondition;
import vu.migration.target.repositories.fraud.rule.general.RuleConditionRepository;

import java.util.List;
import java.util.Optional;

@Service
public class RuleConditionService implements IRuleConditionService{
    private final RuleConditionRepository ruleConditionRepository;
    private final SourceRuleConditionRepository sourceRuleConditionRepository;

    public RuleConditionService(RuleConditionRepository ruleConditionRepository,SourceRuleConditionRepository sourceRuleConditionRepository){
        this.ruleConditionRepository=ruleConditionRepository;
        this.sourceRuleConditionRepository=sourceRuleConditionRepository;
    }

    @Override
    public RuleCondition getRuleConditionById(int id){
        Optional<RuleCondition> ruleCondition = ruleConditionRepository.findById(id);
        return ruleCondition.orElse(null);
    }

    @Override
    public void save(RuleCondition ruleCondition){
        ruleConditionRepository.save(ruleCondition);
    }

    @Override
    public List<SourceRuleCondition> getAllRuleCondition(){
        return sourceRuleConditionRepository.findAll();
    }

    @Override
    public SourceRuleCondition getRuleConditionByRuleId(int id){
        Optional<SourceRuleCondition> ruleCondition = sourceRuleConditionRepository.findById(id);
        return ruleCondition.orElse(null);
    }
}
