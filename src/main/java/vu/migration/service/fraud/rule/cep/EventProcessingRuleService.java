package vu.migration.service.fraud.rule.cep;

import org.springframework.stereotype.Service;
import vu.migration.eventProcessing.model.RuleCepEventStatement;
import vu.migration.eventProcessing.model.RuleCepItem;
import vu.migration.eventProcessing.repositories.IRuleCepRepository;

import java.util.concurrent.atomic.AtomicLong;


@Service
public class EventProcessingRuleService implements IEventProcessingRuleService{

    private final IRuleCepRepository repository;

    public EventProcessingRuleService(IRuleCepRepository repository){
        this.repository=repository;
    }

    private final AtomicLong rulesVersion = new AtomicLong(0);

    @Override
    public RuleCepItem save(RuleCepItem rule) {
        for (RuleCepEventStatement statement : rule.getRuleEventStatements()) {
            statement.setRule(rule);
        }

        RuleCepItem savedRule = repository.save(rule);

        rulesVersion.incrementAndGet();
        return savedRule;
    }

}
