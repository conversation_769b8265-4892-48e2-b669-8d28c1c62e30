package vu.migration.service.fraud.rule.general;

import org.springframework.stereotype.Service;
import vu.data.models.fraud.rule.general.SourceRuleEventStatement;
import vu.data.models.fraud.rule.general.SourceRuleCondition;
import vu.data.models.fraud.rule.general.SourceRuleItem;
import vu.migration.source.repository.fraud.rule.general.SourceRuleConditionRepository;
import vu.migration.source.repository.fraud.rule.general.SourceRuleEventStatementRepository;
import vu.migration.source.repository.fraud.rule.general.SourceRuleItemRepository;
import vu.migration.target.entities.fraud.rule.general.RuleCondition;
import vu.migration.target.entities.fraud.rule.general.RuleItem;
import vu.migration.target.repositories.fraud.rule.general.RuleConditionRepository;
import vu.migration.target.repositories.fraud.rule.general.RuleRepository;

import java.util.List;
import java.util.Optional;

@Service
public class RuleItemService implements IRuleItemService{
    private final RuleRepository ruleItemRepository;
    private final SourceRuleItemRepository sourceRuleItemRepository;
    private final RuleConditionRepository ruleConditionRepository;
    private final SourceRuleConditionRepository sourceRuleConditionRepository;
    private final SourceRuleEventStatementRepository sourceRuleEventStatementRepository;

    public RuleItemService(RuleRepository ruleItemRepository,SourceRuleItemRepository sourceRuleItemRepository,RuleConditionRepository ruleConditionRepository,SourceRuleConditionRepository sourceRuleConditionRepository,SourceRuleEventStatementRepository sourceRuleEventStatementRepository){
        this.ruleItemRepository=ruleItemRepository;
        this.sourceRuleItemRepository=sourceRuleItemRepository;
        this.ruleConditionRepository=ruleConditionRepository;
        this.sourceRuleConditionRepository=sourceRuleConditionRepository;
        this.sourceRuleEventStatementRepository=sourceRuleEventStatementRepository;
    }

    @Override
    public Boolean existsByNameAndChannelIdAndOperationTypeId(String name,int channelId,int operationTypeId){
       return ruleItemRepository.existsByNameAndChannelIdAndOperationTypeId(name,channelId,operationTypeId);
    }

    @Override
    public int save(RuleItem ruleItem){
        return ruleItemRepository.saveAndFlush(ruleItem).getId();
    }

    @Override
    public void saveAllRuleItem(List<RuleItem> ruleItems){
        ruleItemRepository.saveAll(ruleItems);
    }

    @Override
    public List<SourceRuleItem> getAllRuleItem(){
        return sourceRuleItemRepository.findAll();
    }

    @Override
    public RuleItem getRuleItemBySourceRuleItemId(int sourceRuleItemId){
        Optional<RuleItem> ruleItem = ruleItemRepository.findById(sourceRuleItemId);
        return ruleItem.orElseGet(RuleItem::new);
    }

    @Override
    public List<SourceRuleCondition> getAllRuleCondition(){
        return sourceRuleConditionRepository.findAll();
    }

    @Override
    public void saveAllRuleCondition(List<RuleCondition> ruleConditions){
        ruleConditionRepository.saveAll(ruleConditions);
    }

    @Override
    public SourceRuleItem findByRuleItemId(int ruleId){
        Optional<SourceRuleItem> ruleItem = sourceRuleItemRepository.findById(ruleId);
        return ruleItem.orElseGet(SourceRuleItem::new);
    }

    @Override
    public List<SourceRuleEventStatement> findAllRuleEventStatementByRuleId(int ruleId){
        return sourceRuleEventStatementRepository.findAllByRuleId(ruleId);
    }
}
