package vu.migration.service.fraud.rule;

public class RuleActionEnumName {
        public enum Enum {
                NONE,
                ALLOW,
                AUTH,
                DENY,
                REVIEW,
                BLOCK,
                CASE
        }

        public static final int NONE = 0;
        public static String getName(int actionId) {
                return Enum.values()[actionId].name();
        }
}