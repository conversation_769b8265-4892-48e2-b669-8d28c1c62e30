package vu.migration.service.fraud.rule.cep.model;

public class BaseResponse {
    private int code;
    private String message;
    private String traceId;
    private CepRuleResponse data;

    public BaseResponse() {
    }

    public BaseResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public BaseResponse(ResponseCodes responseCode) {
        this.code = responseCode.getValue();
        this.message = responseCode.getReason();
    }

    public int getCode() {
        return this.code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    @Override
    public String toString() {
        return "BaseResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", traceId='" + traceId + '\'' +
                '}';
    }

    public CepRuleResponse getData(){
        return data;
    }

    public void setData(CepRuleResponse data){
        this.data=data;
    }
}