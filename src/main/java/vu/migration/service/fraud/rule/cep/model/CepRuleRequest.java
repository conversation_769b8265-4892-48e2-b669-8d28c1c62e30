package vu.migration.service.fraud.rule.cep.model;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class CepRuleRequest {
    private String name;
    private boolean enabled;
    private short idAction;
    private String jsonFormat;
    private int idCriticalLevel;
    private List<EventStatement> eventStatements = new ArrayList<>();
}