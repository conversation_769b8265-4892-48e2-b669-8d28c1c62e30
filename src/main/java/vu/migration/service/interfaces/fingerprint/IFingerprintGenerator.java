package vu.migration.service.interfaces.fingerprint;


import vu.migration.service.fingerprint.generator.Fingerprint;
import vu.migration.target.entities.fingerprint.browser.agent.FingerprintAgentValue;
import vu.migration.target.entities.fingerprint.browser.component.IComponentRetriever;

public interface IFingerprintGenerator {
    Fingerprint generate(IComponentRetriever componentRetriever);
    Fingerprint generate(IComponentRetriever componentRetriever, FingerprintAgentValue agent);
}
