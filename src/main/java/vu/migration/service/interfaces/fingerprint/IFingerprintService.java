package vu.migration.service.interfaces.fingerprint;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vu.data.models.fingerprint.v2.SourceFingerprintV2UserHashes;
import vu.migration.util.FingerprintV1AndDeviceInfo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface IFingerprintService{
    Page<SourceFingerprintV2UserHashes> fingerprintV2UserHashesFindAll(Pageable page,Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate);

    Page<FingerprintV1AndDeviceInfo> fingerprintUserHashesFindAll(Pageable page,Optional<LocalDateTime> fromDate,Optional<LocalDateTime> toDate);

    void copyFingerprintV2(List<SourceFingerprintV2UserHashes> userFingerprintV2);

    void copyFingerprint(List<FingerprintV1AndDeviceInfo> userFingerprint);
}
