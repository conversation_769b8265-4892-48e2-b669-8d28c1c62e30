package vu.migration.service.interfaces.fingerprint;

import vu.migration.service.fingerprint.FingerprintResult;
import vu.migration.target.entities.dto.fingerprint.UpdateFingerprint;

import java.util.UUID;

public interface IFingerprintStateUpdater {
    FingerprintResult setAsVerifiedFingerprint(String userid, UUID token) throws Exception;

    FingerprintResult setAsVerifiedFingerprint(UpdateFingerprint updateFingerprint) throws Exception;

    FingerprintResult setAsInvalidFingerprint(UpdateFingerprint updateFingerprint) throws Exception;
}
