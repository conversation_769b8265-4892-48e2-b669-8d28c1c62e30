package vu.migration.service.interfaces.fingerprint;

import vu.migration.target.entities.fingerprint.FingerprintCollection;
import vu.migration.target.entities.fingerprint.FingerprintUser;

import java.util.List;

public interface IFingerprintUsersService {
    FingerprintUser getFingerprintUser(String userId);

    List<FingerprintCollection> getUserFingerprintCollections(String userId);

    FingerprintUser saveNewFingerprintUser(FingerprintUser user);

}
