package vu.migration.service.interfaces.fingerprint.rule;

import vu.migration.target.common.Paged;
import vu.migration.target.entities.common.Filter;
import vu.migration.target.entities.fingerprint.FingerprintHash;
import vu.migration.target.entities.fingerprint.FingerprintType;
import vu.migration.target.entities.fingerprint.FingerprintUserHash;

public interface IHashFingerprintService {

    //List<FingerprintHash> getPagedHashesByUserId(String userId, int start, int end);
    Paged<FingerprintHash> getPagedHashes(Filter filter,FingerprintType fingerprintType);

    boolean verifyFingerprintCoincidence(FingerprintUserHash fp, FingerprintHash compareTo);

}
