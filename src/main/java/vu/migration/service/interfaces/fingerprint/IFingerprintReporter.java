package vu.migration.service.interfaces.fingerprint;

import vu.migration.target.entities.dto.IUserCount;
import vu.migration.target.entities.fingerprint.RangeCount;

import java.util.Date;
import java.util.List;

public interface IFingerprintReporter {

    List<IUserCount> getTopUsersWIthUnverifiedFingerprints(Date from, Date to);
    int getUnverifiedFingerprintsCountByDate(Date from, Date to);
    int getUnverifiedFingerprintUserCountByDate(Date from, Date to);
    int getCountUnverifiedFingerprintsByDatesWithoutRules(Date from, Date to);
    RangeCount getCountRangeByDates(Date from, Date to);
}
