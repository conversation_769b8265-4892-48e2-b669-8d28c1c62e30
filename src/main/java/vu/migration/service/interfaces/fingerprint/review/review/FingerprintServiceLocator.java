package vu.migration.service.interfaces.fingerprint.review.review;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import vu.migration.service.interfaces.fingerprint.rule.IHashFingerprintService;
import vu.migration.target.entities.fingerprint.FingerprintType;

@Service
public class FingerprintServiceLocator implements IFingerprintServiceLocator {

    @Autowired
    @Qualifier("BrowserFingerprint")
    IHashFingerprintService browserFingerprintService;

    @Autowired
    @Qualifier("DeviceFingerprint")
    IHashFingerprintService deviceFingerprintService;

    @Override
    public IHashFingerprintService getService(FingerprintType serviceType) {

        switch (serviceType) {
            case BrowserV1:
            case BrowserV2:
                return browserFingerprintService;

            case Device:
                return deviceFingerprintService;
        }

        return null;
    }
}
