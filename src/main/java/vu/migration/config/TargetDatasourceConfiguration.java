package vu.migration.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "targetEntityManagerFactory",
        transactionManagerRef = "transactionTargetManager",
        basePackages = { "vu.migration.target.repositories" })
public class TargetDatasourceConfiguration{

    @Bean
    @ConfigurationProperties("spring.target")
    public DataSourceProperties dataTargetProperties() {
        return new DataSourceProperties();
    }

        @Primary
        @Bean(name="targetDatasource")
        @ConfigurationProperties(prefix = "spring.target.hikari")
        public DataSource datasource(@Value("${spring.target.extraParams:}") String extraParams){
                DataSourceProperties props = dataTargetProperties();
                String url = props.getUrl();
                if (extraParams != null && !extraParams.isBlank() && url != null && !url.isBlank()) {
                        props.setUrl(appendParams(url, extraParams));
                }
                return props.initializeDataSourceBuilder().build();
        }

        private static String appendParams(String url, String extra) {
                if (extra == null || extra.isBlank()) return url;
                String delimiter = url.contains("?") ? "&" : "?";
                return url + delimiter + extra;
        }

    @Primary
    @Bean(name="targetEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryBean
            (EntityManagerFactoryBuilder builder,
             @Qualifier("targetDatasource") DataSource dataSource,
             @Value("${spring.target.dialect}") String dialect) {

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", dialect);
        // Enable proper batching optimizations
        properties.put("hibernate.order_inserts", true);
        properties.put("hibernate.order_updates", true);
        // Respect global batch size if provided via env BATCH_SIZE (defaults handled by Spring config)
        properties.put("hibernate.jdbc.batch_size", System.getProperty("BATCH_SIZE", System.getenv().getOrDefault("BATCH_SIZE", "20000")));
        properties.put("hibernate.physical_naming_strategy",
                "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
        properties.put("hibernate.implicit_naming_strategy",
                "org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl");
        properties.put("hibernate.hbm2ddl.auto", "none");

        return builder
                .dataSource(dataSource)
                .packages("vu.migration.target.entities")
                .persistenceUnit("Target")
                .properties(properties)
                .build();
    }

    @Primary
    @Bean
    public PlatformTransactionManager transactionTargetManager(
            @Qualifier("targetEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactory.getObject()));
    }
}
