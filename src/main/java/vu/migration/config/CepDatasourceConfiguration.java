package vu.migration.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "cepEntityManagerFactory",
        transactionManagerRef = "transactionCepManager",
        basePackages = { "vu.migration.eventProcessing.repositories" })
public class CepDatasourceConfiguration{

    @Bean
    @ConfigurationProperties("spring.cep")
    public DataSourceProperties dataCepProperties() {
        return new DataSourceProperties();
    }

        @Bean(name="cepDatasource")
        @ConfigurationProperties(prefix = "spring.cep.hikari")
        public DataSource datasource(@Value("${spring.cep.extraParams:}") String extraParams){
                DataSourceProperties props = dataCepProperties();
                String url = props.getUrl();
                if (extraParams != null && !extraParams.isBlank() && url != null && !url.isBlank()) {
                        props.setUrl(appendParams(url, extraParams));
                }
                return props.initializeDataSourceBuilder().build();
        }

        private static String appendParams(String url, String extra) {
                if (extra == null || extra.isBlank()) return url;
                String delimiter = url.contains("?") ? "&" : "?";
                return url + delimiter + extra;
        }

    @Bean(name="cepEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryBean
            (EntityManagerFactoryBuilder builder,
             @Qualifier("cepDatasource") DataSource dataSource,
             @Value("${spring.cep.dialect}") String dialect) {

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", dialect);
        // Enable proper batching optimizations
        properties.put("hibernate.order_inserts", true);
        properties.put("hibernate.order_updates", true);
        properties.put("hibernate.jdbc.batch_size", System.getProperty("BATCH_SIZE", System.getenv().getOrDefault("BATCH_SIZE", "20000")));
        properties.put("hibernate.physical_naming_strategy",
                "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
        properties.put("hibernate.implicit_naming_strategy",
                "org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl");
        properties.put("hibernate.hbm2ddl.auto", "none");

        return builder
                .dataSource(dataSource)
                .packages("vu.migration.eventProcessing.model")
                .persistenceUnit("Cep")
                .properties(properties)
                .build();
    }

    @Bean
    public PlatformTransactionManager transactionCepManager(
            @Qualifier("cepEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactory.getObject()));
    }
}
