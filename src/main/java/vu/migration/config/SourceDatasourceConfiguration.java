package vu.migration.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "sourceEntityManagerFactory",
        transactionManagerRef = "transactionSourceManager",
        basePackages = { "vu.migration.source.repository" })
public class SourceDatasourceConfiguration{
    @Bean
    @ConfigurationProperties("spring.source")
    public DataSourceProperties dataSourceProperties() {
        return new DataSourceProperties();
    }


    @Bean(name="sourceDataSource")
    @ConfigurationProperties(prefix = "spring.source.hikari")
    public DataSource datasource(@Value("${spring.source.extraParams:}") String extraParams){
        DataSourceProperties props = dataSourceProperties();
        String url = props.getUrl();
        if (extraParams != null && !extraParams.isBlank() && url != null && !url.isBlank()) {
            props.setUrl(appendParams(url, extraParams));
        }
        return props.initializeDataSourceBuilder().build();
    }

    private static String appendParams(String url, String extra) {
        if (extra == null || extra.isBlank()) return url;
        String delimiter = url.contains("?") ? "&" : "?";
        return url + delimiter + extra;
    }


    @Bean(name = "sourceEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryBean(
            EntityManagerFactoryBuilder builder,
            @Qualifier("sourceDataSource") DataSource dataSource,
            @Value("${spring.source.dialect}") String dialect,
            @Value("${app.migration.source-fetch-size:1000}") Integer fetchSize) {

        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", dialect);
        // Optimize read-side fetching for large scans
        properties.put("hibernate.jdbc.fetch_size", fetchSize);
        properties.put("hibernate.physical_naming_strategy",
                "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
        properties.put("hibernate.implicit_naming_strategy",
                "org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl");
        properties.put("hibernate.hbm2ddl.auto", "none");

        return builder
                .dataSource(dataSource)
                .packages("vu.data.models")
                .persistenceUnit("Source")
                .properties(properties)
                .build();
    }


    @Bean
    public PlatformTransactionManager transactionSourceManager(
            @Qualifier("sourceEntityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactory.getObject()));
    }
}
