package vu.migration.source.repository.report.profile;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vu.data.models.report.profile.SourceUserBusinessStatItem;
import vu.migration.target.entities.profile.UserBusinessStatItem;

@Repository
public interface SourceUserBusinessStatsRepository extends JpaRepository<SourceUserBusinessStatItem, Long> {
    @Query("SELECT new vu.migration.target.entities.profile.UserBusinessStatItem " +
            "(ubsi.id,ubsi.userId,ubsi.businessName,ubsi.maxAmount,ubsi.dailyMaxCount,ubsi.weeklyMaxCount,ubsi.monthlyMaxCount,ubsi.dailyNextUpdate,ubsi.weeklyNextUpdate,ubsi.monthlyNextUpdate,ubsi.newDailyProfile,ubsi.newWeeklyProfile,ubsi.newMonthlyProfile) " +
            "FROM  SourceUserBusinessStatItem ubsi ORDER BY ubsi.id ASC")
    Page<UserBusinessStatItem> getAllUserBusinessStatItem(Pageable pageable);
}
