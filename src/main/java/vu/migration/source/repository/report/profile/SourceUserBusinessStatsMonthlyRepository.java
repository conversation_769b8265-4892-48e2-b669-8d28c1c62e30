package vu.migration.source.repository.report.profile;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vu.data.models.report.profile.SourceUserBusinessStatDetailMonthly;
import vu.migration.target.entities.profile.UserBusinessStatDetailMonthly;

@Repository
public interface SourceUserBusinessStatsMonthlyRepository extends JpaRepository<SourceUserBusinessStatDetailMonthly, Integer> {
    @Query("SELECT new vu.migration.target.entities.profile.UserBusinessStatDetailMonthly" +
            "(ubsdm.id,ubsdm.userId,ubsdm.lastTransactionDate,ubsdm.periodMonth,ubsdm.periodYear,ubsdm.transactionCount,ubsdm.accumulatedAmount) " +
            "FROM  SourceUserBusinessStatDetailMonthly ubsdm ORDER BY ubsdm.id ASC")
    Page<UserBusinessStatDetailMonthly> getAllUserBusinessStatDetailMonthly(Pageable pageable);
}