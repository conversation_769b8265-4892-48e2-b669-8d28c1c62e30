package vu.migration.source.repository.report.profile;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vu.data.models.report.profile.SourceUserBusinessStatDetailDay;
import vu.migration.target.entities.profile.UserBusinessStatDetailDay;

@Repository
public interface SourceUserBusinessStatsDailyRepository extends JpaRepository<SourceUserBusinessStatDetailDay, Integer> {
    @Query("SELECT new vu.migration.target.entities.profile.UserBusinessStatDetailDay" +
            "(ubsdd.id,ubsdd.userId,ubsdd.lastTransactionDate,ubsdd.periodMonth,ubsdd.periodYear,ubsdd.accumulatedAmount,ubsdd.transactionCount,ubsdd.dayOfMonth) " +
            "FROM  SourceUserBusinessStatDetailDay ubsdd ORDER BY ubsdd.id ASC")
    Page<UserBusinessStatDetailDay> getAllUserBusinessStatDetailDay(Pageable pageable);
}