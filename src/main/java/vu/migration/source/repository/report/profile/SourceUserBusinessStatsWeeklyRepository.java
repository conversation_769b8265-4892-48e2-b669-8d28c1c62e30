package vu.migration.source.repository.report.profile;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vu.data.models.report.profile.SourceUserBusinessStatsDetailWeekly;
import vu.migration.target.entities.profile.UserBusinessStatsDetailWeekly;

@Repository
public interface SourceUserBusinessStatsWeeklyRepository extends JpaRepository<SourceUserBusinessStatsDetailWeekly, Integer>{
    @Query("SELECT new vu.migration.target.entities.profile.UserBusinessStatsDetailWeekly" +
            "(ubsdw.id,ubsdw.userId,ubsdw.lastTransactionDate,ubsdw.periodMonth,ubsdw.periodYear,ubsdw.accumulatedAmount,ubsdw.transactionCount,ubsdw.weekOfMonth) " +
            "FROM  SourceUserBusinessStatsDetailWeekly ubsdw ORDER BY ubsdw.id ASC")
    Page<UserBusinessStatsDetailWeekly> getAllUserBusinessStatDetailWeekly(Pageable pageable);
}