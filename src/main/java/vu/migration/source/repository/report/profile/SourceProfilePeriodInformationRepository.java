package vu.migration.source.repository.report.profile;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vu.data.models.report.profile.SourceProfilePeriodInformation;
import vu.migration.source.repository.fraud.transaction.IdRange;

import java.util.Optional;

public interface SourceProfilePeriodInformationRepository extends JpaRepository<SourceProfilePeriodInformation,Integer>, JpaSpecificationExecutor<SourceProfilePeriodInformation>{

    @Query("SELECT new vu.migration.source.repository.fraud.transaction.IdRange(MIN(e.id), MAX(e.id)) " +
            "FROM SourceProfilePeriodInformation e " +
            "WHERE (e.periodYear * 100 + e.periodMonth) BETWEEN :fromPeriod AND :toPeriod")
    Optional<IdRange> findMinAndMaxIdByDateRange(@Param("fromPeriod") Integer fromPeriod,
                                                 @Param("toPeriod") Integer toPeriod);

    @Query("SELECT new vu.migration.source.repository.fraud.transaction.IdRange(MIN(e.id), MAX(e.id)) " +
            "FROM SourceProfilePeriodInformation e")
    Optional<IdRange> findMinAndMaxId();

}
