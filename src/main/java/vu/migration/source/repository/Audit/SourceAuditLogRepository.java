package vu.migration.source.repository.Audit;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vu.data.models.audit.SourceAuditLog;
import vu.migration.target.entities.audit.AuditLog;

public interface SourceAuditLogRepository extends JpaRepository<SourceAuditLog, Long>{
    @Query("SELECT new vu.migration.target.entities.audit.AuditLog(al.id,al.dateAudit,al.action,al.field,al.userName,al.roleName) FROM  SourceAuditLog al " +
            "WHERE al.id > 0")
    Page<AuditLog> findAllAuditLog(Pageable pageable);
}
