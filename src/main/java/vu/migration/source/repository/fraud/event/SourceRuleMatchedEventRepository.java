package vu.migration.source.repository.fraud.event;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import vu.data.models.fraud.event.SourceRuleMatchedEvent;

import java.util.Optional;

public interface SourceRuleMatchedEventRepository extends JpaRepository<SourceRuleMatchedEvent,Integer>, JpaSpecificationExecutor<SourceRuleMatchedEvent> {
    Optional<SourceRuleMatchedEvent> findByEventId(Integer eventId);

    Page<SourceRuleMatchedEvent> findAllByTriggeredRuleItemId(int ruleID,Pageable pageable);
}
