package vu.migration.source.repository.fraud.transaction;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import vu.data.models.fraud.transaction.SourceRuleMatchedTransaction;
import java.util.Optional;

public interface SourceRuleMatchedTransactionRepository extends JpaRepository<SourceRuleMatchedTransaction,Integer>, JpaSpecificationExecutor<SourceRuleMatchedTransaction> {
    Optional<SourceRuleMatchedTransaction> findByEventId(Integer eventId);

    Page<SourceRuleMatchedTransaction> findAllByTriggeredRuleItemId(int eventID,Pageable pageable);
}
