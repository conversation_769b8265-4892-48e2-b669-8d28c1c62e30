package vu.migration.source.repository.fraud.cm;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import vu.data.models.fraud.cm.SourceCaseAttachment;

import java.util.List;

public interface SourceCaseAttachmentRepository extends JpaRepository<SourceCaseAttachment,Integer>, JpaSpecificationExecutor<SourceCaseAttachment> {
    List<SourceCaseAttachment> findAllByTransactionId(Long transactionId);

}
