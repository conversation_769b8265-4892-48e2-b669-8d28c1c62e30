package vu.migration.source.repository.fraud.event;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vu.data.models.fraud.event.SourceEvents;
import vu.migration.source.repository.fraud.transaction.IdRange;

import java.time.LocalDateTime;
import java.util.Optional;

public interface SourceEventRepository extends JpaRepository<SourceEvents, Long>, JpaSpecificationExecutor<SourceEvents>{
    @Query("SELECT new vu.migration.source.repository.fraud.transaction.IdRange(MIN(e.id), MAX(e.id)) " +
            "FROM SourceEvents e WHERE e.eventDate BETWEEN :fromDate AND :toDate")
    Optional<IdRange> findMinAndMaxIdByDateRange(@Param("fromDate") LocalDateTime fromDate,@Param("toDate") LocalDateTime toDate);

    @Query("SELECT new vu.migration.source.repository.fraud.transaction.IdRange(MIN(e.id), MAX(e.id)) " +
            "FROM SourceEvents e")
    Optional<IdRange> findMinAndMaxId();
}
