package vu.migration.source.repository.fraud.transaction;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import vu.data.models.fraud.transaction.SourceTransactionEntity;

import java.time.LocalDateTime;
import java.util.Optional;

public interface SourceTransactionEntityRepository extends JpaRepository<SourceTransactionEntity,Long>, JpaSpecificationExecutor<SourceTransactionEntity> {
    Optional<SourceTransactionEntity> findFirstByEventDateBetweenOrderByEventDateAscIdAsc(
            LocalDateTime startDate,
            LocalDateTime endDate
    );

    Optional<SourceTransactionEntity> findFirstByEventDateBetweenOrderByEventDateDescIdDesc(
            LocalDateTime startDate,
            LocalDateTime endDate
    );

    @Query("SELECT new vu.migration.source.repository.fraud.transaction.IdRange(MIN(e.id), MAX(e.id)) " +
            "FROM SourceTransactionEntity e")
    Optional<IdRange> findMinAndMaxId();
}

