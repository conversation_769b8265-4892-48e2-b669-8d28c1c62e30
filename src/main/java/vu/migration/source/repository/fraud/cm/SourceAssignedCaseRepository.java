package vu.migration.source.repository.fraud.cm;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import vu.data.models.fraud.cm.SourceAssignedCase;

import java.util.List;

public interface SourceAssignedCaseRepository extends JpaRepository<SourceAssignedCase,Long>, JpaSpecificationExecutor<SourceAssignedCase> {

    List<SourceAssignedCase> findAllByTransactionId(Long transactionId);

}
