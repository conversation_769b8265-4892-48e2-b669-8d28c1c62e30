package vu.migration.source.repository.fraud.transaction;

public class IdRange {
    private Long minId;
    private Long maxId;

    public IdRange(Long minId, Long maxId) {
        this.minId = minId;
        this.maxId = maxId;
    }

    public IdRange(Number minId, Number maxId) {
        this.minId = minId == null ? null : minId.longValue();
        this.maxId = maxId == null ? null : maxId.longValue();
    }

    public Long getMinId() { return minId; }
    public Long getMaxId() { return maxId; }
}
