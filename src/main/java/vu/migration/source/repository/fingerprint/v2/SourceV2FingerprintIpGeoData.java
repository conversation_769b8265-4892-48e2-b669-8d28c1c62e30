package vu.migration.source.repository.fingerprint.v2;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vu.data.models.fingerprint.v2.SourceFingerprintIpGeoData;

public interface SourceV2FingerprintIpGeoData extends JpaRepository<SourceFingerprintIpGeoData,Long>{
    @Query("SELECT new vu.migration.target.entities.fingerprint.browser.server.GeoData(" +
            "figd.accuracyRadius,figd.latitude,figd.longitude,figd.country,figd.city,figd.subdivisions) " +
            "FROM SourceFingerprintIpGeoData figd Where figd.userId = ?1")
    vu.migration.target.entities.fingerprint.browser.server.GeoData getGeoDataByUser(String user);
}
