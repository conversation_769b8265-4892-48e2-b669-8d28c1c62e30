package vu.migration.source.repository.fingerprint.v2;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import vu.data.models.fingerprint.v2.SourceFingerprintV2UserHashes;

import java.time.LocalDateTime;

public interface SourceV2FingerprintUserHashesRepository extends JpaRepository<SourceFingerprintV2UserHashes, Long>{
    Page<SourceFingerprintV2UserHashes> findByCreationDateBetween(LocalDateTime from,LocalDateTime to,Pageable pageable);
    Page<SourceFingerprintV2UserHashes> findAll(Pageable pageable);
}
