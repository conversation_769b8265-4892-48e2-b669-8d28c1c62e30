package vu.migration.source.repository.fingerprint;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vu.data.models.fingerprint.SourceFingerprintUserHashes;
import vu.migration.util.FingerprintV1AndDeviceInfo;

import java.time.LocalDateTime;


public interface SourceFingerprintUserHashesRepository extends JpaRepository<SourceFingerprintUserHashes, Long>{

    @Query("SELECT new vu.migration.util.FingerprintV1AndDeviceInfo(" +
            "fuh.id," +
            "fuh.userId," +
            "fuh.clientHashId," +
            "fuh.status," +
            "fuh.accuracy," +
            "fuh.lastCheckDate," +
            "fuh.creationDate," +
            "fuh.apiKeyId," +
            "fuh.countUsed," +
            "fuh.sourceFingerprintHashes.hash," +
            "fuh.sourceFingerprintHashes.creationDate," +
            "fdv.id," +
            "fdv.randomSeed," +
            "fdv.deviceId," +
            "fdv.deviceName," +
            "fdv.deviceManufacturer," +
            "fdv.deviceResolution," +
            "fdv.isRooted," +
            "fdv.isFingerprintCompatible," +
            "fdv.isGPSCompatible," +
            "fdv.osName," +
            "fdv.osVersion," +
            "fdv.apiLevel," +
            "fv.id," +
            "fv.language," +
            "fv.colorDepth," +
            "fv.screenHeight," +
            "fv.screenWidth," +
            "fv.availableHeight," +
            "fv.availableWidth," +
            "fv.timezoneOffset," +
            "fv.hasSessionStorage," +
            "fv.hasLocalStorage," +
            "fv.hasIndexedDb," +
            "fv.hasAddBehavior," +
            "fv.hasOpenDatabase," +
            "fv.navigatorCpuClass," +
            "fv.navigatorPlatform," +
            "fv.doNotTrack," +
            "fv.plugins," +
            "fv.hasAdBlock," +
            "fv.hasLiedLanguages," +
            "fv.hasLiedResolution," +
            "fv.hasLiedOs," +
            "fv.fonts," +
            "fv.crcCanvas," +
            "fv.crcWebGl," +
            "fav.id," +
            "fav.ua," +
            "fav.browserName," +
            "fav.browserMajor," +
            "fav.browserVersion," +
            "fav.cpuArchitecture," +
            "fav.deviceType," +
            "fav.deviceModel," +
            "fav.deviceVendor," +
            "fav.engineName," +
            "fav.osName," +
            "fav.engineVersion," +
            "fav.osVersion) " +
            "FROM SourceFingerprintUserHashes fuh\n"+
            "LEFT JOIN SourceFingerprintDeviceValue fdv on fuh.sourceFingerprintHashes.id = fdv.hashId " +
            "LEFT JOIN SourceFingerprintAgentValues fav on fuh.sourceFingerprintHashes.id = fav.hashId " +
            "LEFT JOIN SourceFingerprintValues fv on fuh.sourceFingerprintHashes.id = fv.hashId ORDER BY fuh.id ASC")
    Page<FingerprintV1AndDeviceInfo> findAllFingerprintUserHashes(Pageable pageable);

    @Query("SELECT new vu.migration.util.FingerprintV1AndDeviceInfo(" +
            "fuh.id," +
            "fuh.userId," +
            "fuh.clientHashId," +
            "fuh.status," +
            "fuh.accuracy," +
            "fuh.lastCheckDate," +
            "fuh.creationDate," +
            "fuh.apiKeyId," +
            "fuh.countUsed," +
            "fuh.sourceFingerprintHashes.hash," +
            "fuh.sourceFingerprintHashes.creationDate," +
            "fdv.id," +
            "fdv.randomSeed," +
            "fdv.deviceId," +
            "fdv.deviceName," +
            "fdv.deviceManufacturer," +
            "fdv.deviceResolution," +
            "fdv.isRooted," +
            "fdv.isFingerprintCompatible," +
            "fdv.isGPSCompatible," +
            "fdv.osName," +
            "fdv.osVersion," +
            "fdv.apiLevel," +
            "fv.id," +
            "fv.language," +
            "fv.colorDepth," +
            "fv.screenHeight," +
            "fv.screenWidth," +
            "fv.availableHeight," +
            "fv.availableWidth," +
            "fv.timezoneOffset," +
            "fv.hasSessionStorage," +
            "fv.hasLocalStorage," +
            "fv.hasIndexedDb," +
            "fv.hasAddBehavior," +
            "fv.hasOpenDatabase," +
            "fv.navigatorCpuClass," +
            "fv.navigatorPlatform," +
            "fv.doNotTrack," +
            "fv.plugins," +
            "fv.hasAdBlock," +
            "fv.hasLiedLanguages," +
            "fv.hasLiedResolution," +
            "fv.hasLiedOs," +
            "fv.fonts," +
            "fv.crcCanvas," +
            "fv.crcWebGl," +
            "fav.id," +
            "fav.ua," +
            "fav.browserName," +
            "fav.browserMajor," +
            "fav.browserVersion," +
            "fav.cpuArchitecture," +
            "fav.deviceType," +
            "fav.deviceModel," +
            "fav.deviceVendor," +
            "fav.engineName," +
            "fav.osName," +
            "fav.engineVersion," +
            "fav.osVersion) " +
            "FROM SourceFingerprintUserHashes fuh\n"+
            "LEFT JOIN SourceFingerprintDeviceValue fdv on fuh.sourceFingerprintHashes.id = fdv.hashId " +
            "LEFT JOIN SourceFingerprintAgentValues fav on fuh.sourceFingerprintHashes.id = fav.hashId " +
            "LEFT JOIN SourceFingerprintValues fv on fuh.sourceFingerprintHashes.id = fv.hashId " +
            "WHERE fuh.creationDate BETWEEN :from AND :to " +
            "ORDER BY fuh.id ASC")
    Page<FingerprintV1AndDeviceInfo> findByCreationDateBetween(LocalDateTime from,LocalDateTime to,Pageable pageable);
}
