package vu.data.models.administration.user;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import jakarta.persistence.*;

import java.util.Collection;

@Entity
@NoArgsConstructor
@Getter
@Table(name = "\"Role\"")
public class SourceRole{
    @Id
    private Long id;

    private String name;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "roles_privileges", joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "privilege_id", referencedColumnName = "id"))
    @ToString.Exclude
    private Collection<SourcePrivilege> sourcePrivileges;

    public SourceRole(String name) {
        super();
        this.name = name;
    }

    public String getName(){
        return name;
    }

    public Collection<SourcePrivilege> getSourcePrivileges(){
        return sourcePrivileges;
    }
}
