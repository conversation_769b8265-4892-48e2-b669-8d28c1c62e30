package vu.data.models.administration.list;


import jakarta.persistence.AttributeConverter;


public class SourceFileEntryTypeConverter implements AttributeConverter<SourceFileEntry.Type, Integer>{

    @Override
    public Integer convertToDatabaseColumn(SourceFileEntry.Type filetype) {
        return filetype.ordinal();
    }

    @Override
    public SourceFileEntry.Type convertToEntityAttribute(Integer ordinal) {
        return SourceFileEntry.Type.values()[ordinal];
    }
}
