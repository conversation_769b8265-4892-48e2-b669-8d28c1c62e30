package vu.data.models.administration.user;

import lombok.Getter;

import jakarta.persistence.*;

import java.util.Collection;

@Entity
@Getter
@Table(name = "\"User\"")
public class SourceUser{
    @Id
    private Long id;

    private String email;

    @Column(nullable = true)
    private boolean enabled;

    @Column(name = "firstname")
    private String firstName;

    @Column(name = "forceresetpassword")
    private boolean forceResetPassword;

    @Column(name = "lastname")
    private String lastName;

    @Column(length = 60)
    private String password;

    @Column(name = "tokenexpired")
    private boolean tokenExpired;

    @Column(name = "username")
    private String userName;

    @Column(name = "role_changed")
    private boolean roleChanged;

    @Column(name = "isldapuser")
    private boolean ldapUser;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(name = "users_roles", joinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id"), inverseJoinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id"))
    private Collection<SourceRole> sourceRoles;
}
