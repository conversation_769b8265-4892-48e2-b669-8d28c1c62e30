package vu.data.models.administration.settings.parameter;

import jakarta.persistence.*;
import lombok.Getter;

@Getter
@Entity
@Table(name="\"wsParameterAll\"")
public class SourceParameter{
    @Id
    private int id;

    @Column(name = "\"idEventType\"")
    private int eventTypeId;

    @Column(name = "\"userGenerated\"")
    private boolean userGenerated;

    private String name;

    @Column(name = "\"parameterType\"")
    private int parameterTypeId;

    @Column(name = "\"columnName\"")
    private String columnName;

    private int visibility;

    @Column(name = "\"idParameterInfo\"")
    private Integer parameterInfoId;
}
