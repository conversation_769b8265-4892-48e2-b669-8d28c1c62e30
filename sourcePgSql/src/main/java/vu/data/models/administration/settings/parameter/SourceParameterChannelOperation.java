package vu.data.models.administration.settings.parameter;

import jakarta.persistence.*;
import lombok.Getter;

@Getter
@Entity
@Table(name = "\"wsParameterByChannelOperation\"")
public class SourceParameterChannelOperation{

    @Id
    private Integer id;

    @Column(name = "\"idChannel\"")
    private Integer channelId;

    @Column(name = "\"idParameter\"")
    private Integer parameterId;

    @Column(name = "\"idOperationType\"")
    private Integer operationTypeId;

    private Boolean enabled;
}
