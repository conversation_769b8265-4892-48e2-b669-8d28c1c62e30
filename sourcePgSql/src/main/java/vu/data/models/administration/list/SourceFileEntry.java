package vu.data.models.administration.list;

import lombok.Getter;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Entity
@Table(name = "fileentry")
public class SourceFileEntry{

    public static final int FILE_NOT_PROCESSED = 0;
    public static final int PROCESSING_IN_PROGRESS = 1;
    public static final int FILE_ALREADY_PROCESSED = 2;
    public static final int ERROR_ON_PROCESSING = 3;
    public static final int ERROR_ON_CHECK_FILE = 4;

    public enum Type {
        None,
        CustomList,
        IpList,
        Event,
        Transaction,
        ATM
    }

    @Id
    private int id;

    @Column(name = "filename")
    private String fileName;

    @Column(name = "status")
    private int fileStatus;

    @Column(name = "path")
    private String filePath;

    @Column(name = "uploaddate")
    private Date uploadDate;

    @Column(name = "aditionalfieldid")
    private int additionalFieldId;

    @Convert(converter = SourceFileEntryTypeConverter.class)
    @Column(name = "filetype")
    private Type type;

    @Column(name = "auto")
    private boolean autoImport;

    @Column(name = "status_detail")
    private String statusDetail;

    @Column(name = "filesize")
    private double fileSizeInKB;

}
