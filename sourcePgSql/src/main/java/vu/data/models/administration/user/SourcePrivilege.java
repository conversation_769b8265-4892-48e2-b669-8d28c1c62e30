package vu.data.models.administration.user;

import jakarta.persistence.*;
import lombok.Getter;

import java.util.Collection;

@Getter
@Entity
@Table(name = "\"Privilege\"")
public class SourcePrivilege{
    @Id
    private Long id;

    private String name;

    @Column(name = "description_key")
    private String descriptionKey;

    @Column(name = "id_parent")
    private Long parentId;

    @ManyToMany(mappedBy = "sourcePrivileges")
    private Collection<SourceRole> sourceRoles;
}
