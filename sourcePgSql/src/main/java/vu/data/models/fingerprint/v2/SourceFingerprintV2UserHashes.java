package vu.data.models.fingerprint.v2;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.ToString;
import vu.data.models.administration.apikey.SourceApiKey;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Getter
@Table(name = "fingerprint_user_hashes")
public class SourceFingerprintV2UserHashes{

    public enum Status {
        NONE,                   //  0
        UNVERIFIED,             //  1
        VERIFIED,               //  2
        INVALID                 //  3
    }

    @Id
    private long id;
    @Column(name = "id_apikey")
    private int apiKeyId;
    @Column(name = "id_client_hash")
    private long clientHashId;
    @Column(name = "id_server_hash")
    private long serverHashId;
    @Column(name = "iduser")
    private String userId;
    @Column(name = "creation_date")
    private Date creationDate = new Date();
    @Column(name = "last_checkdate")
    private Date lastCheckDate = new Date();
    @Enumerated(EnumType.ORDINAL)
    private Status status;
    @Column(name = "accuracy")
    private double accuracyPercent;
    @Column(name = "count_used")
    private int countUsed;
    @Column(name = "combined_hash")
    private String combinedHash;
    private boolean incognito;

    @ManyToOne
    @JoinColumn(name = "id_client_hash", referencedColumnName = "id", insertable = false, updatable = false)
    @ToString.Exclude
    @JsonIgnore
    private SourceFingerprintClientHash sourceFingerprintClientHash;

    @ManyToOne
    @JoinColumn(name = "id_apikey", referencedColumnName = "id", insertable = false, updatable = false)
    @ToString.Exclude
    @JsonIgnore
    private SourceApiKey apikey;

}