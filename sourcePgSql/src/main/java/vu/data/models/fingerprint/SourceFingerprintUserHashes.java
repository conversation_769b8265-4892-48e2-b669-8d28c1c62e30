package vu.data.models.fingerprint;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.ToString;
import vu.data.models.administration.apikey.SourceApiKey;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Getter
@Table(name = "fingerprintuserhashes")
public class SourceFingerprintUserHashes{

    public enum Status {
        NONE,                   //  0
        UNVERIFIED,             //  1
        VERIFIED,               //  2
        INVALID                 //  3
    }

    @Id
    private long id;
    @Column(name = "iduser")
    private String userId;
    @Column(name = "idhash")
    private long clientHashId;
    @Enumerated(EnumType.ORDINAL)
    private Status status;
    private double accuracy;
    @Column(name = "lastcheckdate")
    private Date lastCheckDate = new Date();
    @Column(name = "creationdate")
    private Date creationDate = new Date();
    @Column(name = "idapikey")
    private int apiKeyId;
    @Column(name = "countused")
    private int countUsed;

    @ManyToOne
    @JoinColumn(name = "idhash", referencedColumnName = "id", insertable = false, updatable = false)
    @ToString.Exclude
    @JsonIgnore
    private SourceFingerprintHashes sourceFingerprintHashes;

    @ManyToOne
    @JoinColumn(name = "idapikey", referencedColumnName = "id", insertable = false, updatable = false)
    @ToString.Exclude
    @JsonIgnore
    private SourceApiKey apikey;

}