package vu.data.models.fingerprint;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@Getter
@Table(name = "fingerprintvalues")
public class SourceFingerprintValues{

    @Id
    private long id;

    @Column(name = "idhash")
    private Long hashId;

    private String language;

    @Column(name = "\"colorDepth\"")
    private Integer colorDepth;

    @Column(name = "\"screenHeight\"")
    private Integer screenHeight;

    @Column(name = "\"screenWidth\"")
    private Integer screenWidth;

    @Column(name = "\"availableHeight\"")
    private Integer availableHeight;

    @Column(name = "\"availableWidth\"")
    private Integer availableWidth;

    @Column(name = "\"timezoneOffset\"")
    private Integer timezoneOffset;

    @Column(name = "\"hasSessionStorage\"")
    private Boolean hasSessionStorage;

    @Column(name = "\"hasLocalStorage\"")
    private Boolean hasLocalStorage;

    @Column(name = "\"hasIndexedDb\"")
    private Boolean hasIndexedDb;

    @Column(name = "\"hasAddBehavior\"")
    private Boolean hasAddBehavior;

    @Column(name = "\"hasOpenDatabase\"")
    private Boolean hasOpenDatabase;

    @Column(name = "\"navigatorCpuClass\"")
    private String navigatorCpuClass;

    @Column(name = "\"navigatorPlatform\"")
    private String navigatorPlatform;

    @Column(name = "\"doNotTrack\"")
    private String doNotTrack;

    private String plugins;

    @Column(name = "\"hasAdBlock\"")
    private Boolean hasAdBlock;

    @Column(name = "\"hasLiedLanguages\"")
    private Boolean hasLiedLanguages;

    @Column(name = "\"hasLiedResolution\"")
    private Boolean hasLiedResolution;

    @Column(name = "\"hasLiedOs\"")
    private Boolean hasLiedOs;

    private String fonts;

    @Column(name = "\"crcCanvas\"")
    private String crcCanvas;

    @Column(name = "\"crcWebGl\"")
    private String crcWebGl;
}
