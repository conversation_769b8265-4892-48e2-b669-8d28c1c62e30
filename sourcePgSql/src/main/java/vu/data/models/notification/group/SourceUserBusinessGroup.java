package vu.data.models.notification.group;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Entity
@Table(name = "user_businessgroup")
public class SourceUserBusinessGroup{

    @Id
    private int id;

    @Column(name = "id_business_group")
    private int businessGroupId;

    @Column(name = "id_user")
    private long userId;

}
