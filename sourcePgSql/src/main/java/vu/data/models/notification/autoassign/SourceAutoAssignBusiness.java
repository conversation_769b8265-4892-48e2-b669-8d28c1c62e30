package vu.data.models.notification.autoassign;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Entity
@Table(name = "case_business_auto_assign")
public class SourceAutoAssignBusiness{
    @Id
    private Integer id;

    @Column(name = "channel_id")
    private Integer channelId;

    @Column(name = "operation_type_id")
    private Integer operationTypeId;

    @Column(name = "business_group_id")
    private Integer businessGroupId;
}
