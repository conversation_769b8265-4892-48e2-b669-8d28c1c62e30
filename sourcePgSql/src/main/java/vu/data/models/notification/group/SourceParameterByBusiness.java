package vu.data.models.notification.group;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Entity
@Table(name = "parameter_business")
public class SourceParameterByBusiness{

    @Id
    private int id;

    private String business;

    @Column(name = "daily_max_count")
    private int dailyMaxCount;

    @Column(name = "weekly_max_count")
    private int weeklyMaxCount;

    @Column(name = "monthly_max_count")
    private int monthlyMaxCount;

    @Column(name = "daily_max_amount")
    private double dailyMaxAmount;

    @Column(name = "weekly_max_amount")
    private double weeklyMaxAmount;

    @Column(name = "monthly_max_amount")
    private double monthlyMaxAmount;

    private boolean enabled;
}
