package vu.data.models.notification.group;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Entity
@Table(name = "business_group")
public class SourceBusinessGroup{

    @Id
    private int id;

    @Column(name = "name")
    private String name;

    @Column(name = "supervisor_user_id")
    private Long supervisorUserId;

    public Long getSupervisorUserId() {
        return supervisorUserId == null ? 1 : supervisorUserId;
    }
}
