package vu.data.models.notification;

import lombok.Getter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Entity
@Table(name = "notifications_config")
public class SourceNotificationConfig{
    @Id
    private int id;

    @Column(name = "id_business_group")
    private int businessGroupId;

    @Column(name = "id_notification_type")
    private int notificationTypeId;

    @Column(name = "value")
    private String notification_value;

    private boolean enabled;
}
