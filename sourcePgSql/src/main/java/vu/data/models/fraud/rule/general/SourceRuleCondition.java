package vu.data.models.fraud.rule.general;

import jakarta.persistence.*;
import lombok.Getter;

@Getter
@Entity
@Table(name = "\"ruleCondition\"")
public class SourceRuleCondition{
    @Id
    private int id;

    @Column(name = "\"idRule\"")
    private int ruleId;

    @Column(name = "\"idField\"")
    private Integer fieldId;

    @Column(name = "\"idOperator\"")
    private int operatorId;

    @Column(name = "\"idConditionType\"")
    private int conditionTypeId;

    @Column(name = "value")
    private String conditionValue;

    @Column(name = "\"idConnector\"")
    private int connectorId;

    @Column(name = "\"idGroup\"")
    private int groupId;

    @Column(name = "\"idParentGroup\"")
    private int parentGroupId;

    @Column(name = "\"idCustomList\"")
    private int customListId;

    @Column(name = "\"idDeltaType\"")
    private int deltaTypeId;

    @Column(name = "\"idConditionRuleType\"")
    private int conditionRuleTypeId;
}
