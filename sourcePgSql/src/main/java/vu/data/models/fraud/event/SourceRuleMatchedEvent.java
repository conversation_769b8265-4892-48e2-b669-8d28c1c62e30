package vu.data.models.fraud.event;

import lombok.Getter;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Entity
@Table(name = "rule_matched_events")
public class SourceRuleMatchedEvent{
    @Id
    private int id;

    @Column(name = "id_event")
    private Integer eventId;

    @Column(name = "id_rule")
    private Integer triggeredRuleItemId;

    @Column(name = "triggered_date")
    private Date triggeredDate;
}
