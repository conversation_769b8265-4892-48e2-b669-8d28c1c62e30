package vu.data.models.fraud.event;

import lombok.Data;

import jakarta.persistence.*;

@Data
@Entity
@Table(name = "\"customEventFields\"")
public class SourceAdditionalParametersEvent{
    @Id
    @Column(name = "\"idEvent\"")
    private long id;

    private String field1;
    private String field2;
    private String field3;
    private String field4;
    private String field5;
    private String field6;
    private String field7;
    private String field8;
    private String field9;
    private String field10;
    private String field11;
    private String field12;
    private String field13;
    private String field14;
    private String field15;
    private String field16;
    private String field17;
    private String field18;
    private String field19;
    private String field20;
    private String field21;
    private String field22;
    private String field23;
    private String field24;
    private String field25;
    private String field26;
    private String field27;
    private String field28;
    private String field29;
    private String field30;
    private String field31;
    private String field32;
    private String field33;
    private String field34;
    private String field35;
    private String field36;
    private String field37;
    private String field38;
    private String field39;
    private String field40;
    private String field41;
    private String field42;
    private String field43;
    private String field44;
    private String field45;
    private String field46;
    private String field47;
    private String field48;
    private String field49;
    private String field50;
    private String field51;
    private String field52;
    private String field53;
    private String field54;
    private String field55;
    private String field56;
    private String field57;
    private String field58;
    private String field59;
    private String field60;
    private String field61;
    private String field62;
    private String field63;
    private String field64;
    private String field65;
    private String field66;
    private String field67;
    private String field68;
    private String field69;
    private String field70;
    private String field71;
    private String field72;
    private String field73;
    private String field74;
    private String field75;
    private String field76;
    private String field77;
    private String field78;
    private String field79;
    private String field80;
    private String field81;
    private String field82;
    private String field83;
    private String field84;
    private String field85;
    private String field86;
    private String field87;
    private String field88;
    private String field89;
    private String field90;
    private String field91;
    private String field92;
    private String field93;
    private String field94;
    private String field95;
    private String field96;
    private String field97;
    private String field98;
    private String field99;
    private String field100;
}
