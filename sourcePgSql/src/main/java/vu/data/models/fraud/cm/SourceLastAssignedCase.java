package vu.data.models.fraud.cm;

import lombok.Data;
import lombok.Getter;

import jakarta.persistence.*;

@Data
@Getter
@Entity
@Table(name = "last_assigned_cases")
public class SourceLastAssignedCase{

    @Id
    private Integer id;

    @Column(name = "id_transaction")
    private long transactionId;

    @Column(name = "id_user")
    private Long userId;

    @Column(name = "id_business_group")
    private Integer businessGroupId;
}
