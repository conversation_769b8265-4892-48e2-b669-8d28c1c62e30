package vu.data.models.fraud.transaction;


import lombok.Getter;
import vu.data.models.fraud.event.CaseStatusEnumName;
import vu.data.models.fraud.rule.general.RuleActionEnumName;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Entity
@Table(name = "eventtransaction")
public class SourceTransactionEntity{
    @Id
    private long id;
    @Column(name = "\"idChannel\"")
    private Integer channelId;

    @Column(name = "\"idOperationType\"")
    private int operationTypeId;

    @Column(name = "eventdate")
    private Date eventDate;

    @Column(name = "\"creditAccount\"")
    private String creditAccount;

    @Column(name = "\"debitAccount\"")
    private String debitAccount;

    @Column(name = "amount")
    private double amount;

    @Column(name = "\"idUser\"")
    private String userId;

    @Column(name = "idcasestate")
    private short caseStatusId = (short) CaseStatusEnumName.Enum.NONE.ordinal();

    @Column(name = "ip")
    private String ip;

    @Column(name = "\"idAction\"")
    private Short actionId = (short) RuleActionEnumName.Enum.NONE.ordinal();
}
