package vu.data.models.report.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@NoArgsConstructor
@Getter
@Table(name = "profileperiodinfobycreditaccount")
public class SourceProfilePeriodInfoByCreditAccount{

    @Id
    private Integer id;

    @Column(name = "idprofileperiodinfo")
    private Integer profilePeriodInfoId;

    @Column(name = "maxamount")
    private Double maxAmount;

    @Column(name = "accumulatedamount")
    private Double accumulatedAmount;

    @Column(name = "creditaccount")
    private String creditAccount;

}

