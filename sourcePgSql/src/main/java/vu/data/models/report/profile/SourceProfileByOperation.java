package vu.data.models.report.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Entity
@NoArgsConstructor
@Getter
@Table(name = "profile_by_operation")
public class SourceProfileByOperation{

    @Id
    private int id;

    @Column(name = "id_profile_periodinfo")
    private int profilePeriodInfoId;

    @Column(name = "max_amount")
    private double maxAmount;

    @Column(name = "accumulated_amount")
    private double accumulatedAmount;

    @Column(name = "event_count")
    private int eventCount;

    @Column(name = "id_operation_type")
    private int operationTypeId;

}
