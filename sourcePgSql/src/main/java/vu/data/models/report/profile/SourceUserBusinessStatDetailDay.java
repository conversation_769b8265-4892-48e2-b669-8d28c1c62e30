package vu.data.models.report.profile;

import  jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Entity
@NoArgsConstructor
@Getter
@Table(name = "user_business_daily_stats")
public class SourceUserBusinessStatDetailDay{
    @Id
    private long id;

    @Column(name = "day_of_month")
    private int dayOfMonth;

    @Column(name = "id_user_business_stat")
    @NotNull
    private String userId;

    @Column(name = "month")
    private Integer periodMonth;

    @Column(name = "transaction_count")
    private int transactionCount;

    @Column(name = "amount_accumulated")
    private double accumulatedAmount;

    @Column(name = "year")
    private Integer periodYear;

    @Column(name = "last_transaction_date")
    private Date lastTransactionDate;
}
