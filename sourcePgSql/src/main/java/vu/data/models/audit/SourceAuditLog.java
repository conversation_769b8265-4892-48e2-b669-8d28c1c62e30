package vu.data.models.audit;

import lombok.Getter;

import jakarta.persistence.*;
import java.util.Date;


@Entity
@Getter
@Table(name = "audit_log")
public class SourceAuditLog{

    @Id
    private Long id;

    @Column(name = "dateaudit")
    private Date dateAudit;

    private String action;

    @Column(name = "field")
    private String field;

    @Column(name = "username")
    private String userName;

    @Column(name = "rolename")
    private String roleName;

}
