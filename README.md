# Migration Tool

This tool is aimed to migrate Fraud v3 to Fraud 5

## Getting started

### Compile docker image

```bash
docker build --build-arg MAVEN_PROFILE=mssql -f ./Dockerfile.build-war -t migration-v3-to-v5-mssql .
```

### Run

```bash
docker run -it --rm -e SOURCE_DIALECT=org.hibernate.dialect.SQLServer2012Dialect -e SOURCE_URL_DB=yourSourceUrl -e SOURCE_USER_DB=yourSourceUser -e SOURCE_PASSWORD_DB=yourSourcePassword -e TARGET_DIALECT=org.hibernate.dialect.SQLServer2012Dialect -e TARGET_URL_DB=yourTargetUrl -e TARGET_USER_DB=yourTargetUser -e TARGET_PASSWORD_DB=yourTargetPassword migration-v3-to-v5-mssql -e CEP_DIALECT=org.hibernate.dialect.SQLServer2012Dialect -e CEP_URL_DB=yourCepUrl -e CEP_USER_DB=yourCepUser -e CEP_PASSWORD_DB=yourCepPassword java -cp app.jar vu.migration.MigrationApplication
```



