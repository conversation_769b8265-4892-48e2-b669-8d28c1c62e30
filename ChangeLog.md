# Changelog

## [2.2] - 12-08-2025
### CHANGED
- [12-08-2025] [TASK-934](https://git.corp.vusecurity.com/team-fraud/dashboards/fraud-correctivos/-/issues/934):
  - Data truncation on additional transaction and event parameters.
### Fixed
- [11-08-2025] [TASK-2762](https://git.corp.vusecurity.com/team-fraud/dashboards/fraud-evoluciones/-/issues/2762):
  - Fixed, migrador no finaliza con Transacciones.
  
## [2.1] - 21-07-2025
### CHANGED
- [21-07-2025] [TASK-2744](https://git.corp.vusecurity.com/team-fraud/dashboards/fraud-evoluciones/-/issues/2744):
  - Migration 2.1 application with control of what to migrate by command line.

## [2.0] - 11-07-2025
### Fixed
- [11-07-2025] [TASK-2738](https://git.corp.vusecurity.com/team-fraud/dashboards/fraud-evoluciones/-/issues/2738):
  - Migration 2.0 application with control of what to migrate.

## [1.5] - 25-09-2024
### Fixed
- [24-09-2024] [TASK-2549](https://git.corp.vusecurity.com/team-fraud/dashboards/fraud-evoluciones/-/issues/2549):
  - Fixed so that the program copies additional parameters.

## [1.4] - 22-07-2024
### Fixed
- [19-07-2024] [TASK-2538](https://git.corp.vusecurity.com/team-fraud/dashboards/fraud-evoluciones/-/issues/2538):
  - Se corrige para que el programa respete los id de la data de origen en canal y tipo de operación.

## [1.3] - 14-06-2024
### Added
- [14-06-2024] [TASK-2491](https://git.corp.vusecurity.com/team-fraud/dashboards/fraud-evoluciones/-/issues/2491):
  - Se agregan para migrado de data de V3 a V5 de las siguientes funcionalidades:
    - Copy Tablas relacionadas con parámetros por especialidad.

### Fixed
- [14-06-2024] [TASK-2491](https://git.corp.vusecurity.com/team-fraud/dashboards/fraud-evoluciones/-/issues/2491):
  - Corregido error en la tabla business_group el campo "supervisor_user_id" estaba en nulo

## [1.2] - 21-03-2024
### Added
- [20-03-2024] [TASK-2450](https://git.corp.vusecurity.com/team-fraud/fraud-evoluciones/-/issues/2450):
  - Se agregan para migrado de data de V3 a V5 de las siguientes funcionalidades:
    - Copy Fingerprint V1.
    - Soporta migrar de Postgres a Microsoft Sql Server

## [1.1] - 10-01-2024
### Added
- [08-01-2024] [TASK-2412](https://git.corp.vusecurity.com/team-fraud/fraud-evoluciones/-/issues/2412):
  - Se agregan para migrado de data de V3 a V5 de las siguientes funcionalidades:
    - Copy Fingerprint.

## [1.0] - 04-09-2023
### Added
- [28-09-2023] [TASK-2145](https://git.corp.vusecurity.com/team-fraud/fraud-evoluciones/-/issues/2145):
  - Creación del proyecto con Spring Boot, Spring Data & JPA
  - Se agrega disponibilidad para motor base de datos Microsoft SQL Server
  - Se agregan para migrado de data de V3 a V5 de las siguientes funcionalidades:
    - Update System Parameter.
    - Copy Api Key.
    - Copy Auth Role.
    - Copy Auth Fa User.
    - Copy List.
    - Copy Channel.
    - Copy Operation Type.
    - Copy Parameter.
    - Copy Parameter Channel Operation.
    - Copy Parameter Query Channel Operation.
    - Copy Rules.
    - Copy Business Group.
    - Copy Notification.
    - Copy Transaction Entity.
    - Copy Profile.
    - Copy AuditLog.
    - Update Sequence.